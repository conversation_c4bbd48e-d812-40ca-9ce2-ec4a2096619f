#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫模块
提供多种反爬虫策略和技术

Author: HuiCai Team
Date: 2025-01-15
"""

import random
import time
import json
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging


class UserAgentRotator:
    """User-Agent轮换器"""
    
    def __init__(self):
        self.user_agents = [
            # Chrome
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            
            # Firefox
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
            
            # Safari
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            
            # Edge
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        ]
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)


class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self):
        self.proxies = []
        self.current_index = 0
        self.failed_proxies = set()
    
    def add_proxy(self, proxy: str):
        """添加代理"""
        self.proxies.append(proxy)
    
    def get_next_proxy(self) -> Optional[Dict[str, str]]:
        """获取下一个可用代理"""
        if not self.proxies:
            return None
        
        attempts = 0
        while attempts < len(self.proxies):
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
            
            if proxy not in self.failed_proxies:
                return {
                    'http': proxy,
                    'https': proxy
                }
            
            attempts += 1
        
        return None
    
    def mark_proxy_failed(self, proxy: str):
        """标记代理失败"""
        self.failed_proxies.add(proxy)


class RequestThrottler:
    """请求节流器"""
    
    def __init__(self, min_delay: float = 1.0, max_delay: float = 5.0):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request_time = 0
        self.request_count = 0
        self.start_time = time.time()
    
    def wait(self):
        """等待适当的时间间隔"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        # 基础延迟
        delay = random.uniform(self.min_delay, self.max_delay)
        
        # 根据请求频率调整延迟
        if elapsed < 1.0:
            delay *= 2  # 如果请求太频繁，增加延迟
        
        # 随机化延迟模式
        if random.random() < 0.1:  # 10%的概率使用更长延迟
            delay *= random.uniform(2, 4)
        
        if elapsed < delay:
            sleep_time = delay - elapsed
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        elapsed_time = time.time() - self.start_time
        return {
            'total_requests': self.request_count,
            'elapsed_time': elapsed_time,
            'requests_per_minute': (self.request_count / elapsed_time) * 60 if elapsed_time > 0 else 0
        }


class SessionManager:
    """会话管理器"""
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 0.3):
        self.session = requests.Session()
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self._setup_session()
    
    def _setup_session(self):
        """设置会话"""
        # 重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 默认超时
        self.session.timeout = 30
    
    def get_session(self) -> requests.Session:
        """获取会话"""
        return self.session
    
    def close(self):
        """关闭会话"""
        self.session.close()


class AntiCrawlerManager:
    """反爬虫管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.ua_rotator = UserAgentRotator()
        self.proxy_rotator = ProxyRotator()
        self.throttler = RequestThrottler(
            min_delay=self.config.get('min_delay', 1.0),
            max_delay=self.config.get('max_delay', 5.0)
        )
        self.session_manager = SessionManager(
            max_retries=self.config.get('max_retries', 3)
        )
        
        # 请求历史
        self.request_history = []
        self.max_history = 1000
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'timeout_errors': 0,
            'connection_errors': 0,
            'http_errors': 0,
            'proxy_errors': 0
        }
    
    def add_proxies(self, proxies: List[str]):
        """添加代理列表"""
        for proxy in proxies:
            self.proxy_rotator.add_proxy(proxy)
        self.logger.info(f"添加了 {len(proxies)} 个代理")
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """发起请求"""
        # 等待节流
        self.throttler.wait()
        
        # 准备请求参数
        headers = kwargs.get('headers', {})
        headers['User-Agent'] = self.ua_rotator.get_random_user_agent()
        
        # 添加常见的浏览器头部
        headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        kwargs['headers'] = headers
        
        # 获取代理
        proxy = self.proxy_rotator.get_next_proxy()
        if proxy:
            kwargs['proxies'] = proxy
        
        session = self.session_manager.get_session()
        
        try:
            # 记录请求开始时间
            start_time = time.time()
            
            # 发起请求
            response = session.request(method, url, **kwargs)
            
            # 记录请求历史
            self._record_request(url, method, response.status_code, time.time() - start_time)
            
            # 检查响应状态
            if response.status_code == 200:
                self.logger.debug(f"请求成功: {url}")
                return response
            elif response.status_code == 429:
                self.logger.warning(f"请求被限流: {url}")
                self._handle_rate_limit()
                return None
            else:
                self.logger.warning(f"请求返回状态码 {response.status_code}: {url}")
                self.error_stats['http_errors'] += 1
                return None
                
        except requests.exceptions.Timeout:
            self.logger.error(f"请求超时: {url}")
            self.error_stats['timeout_errors'] += 1
            self.error_stats['total_errors'] += 1
            return None
            
        except requests.exceptions.ConnectionError:
            self.logger.error(f"连接错误: {url}")
            self.error_stats['connection_errors'] += 1
            self.error_stats['total_errors'] += 1
            
            # 如果使用了代理，标记代理失败
            if proxy:
                self.proxy_rotator.mark_proxy_failed(proxy['http'])
                self.error_stats['proxy_errors'] += 1
            
            return None
            
        except Exception as e:
            self.logger.error(f"请求异常: {url}, 错误: {e}")
            self.error_stats['total_errors'] += 1
            return None
    
    def _record_request(self, url: str, method: str, status_code: int, duration: float):
        """记录请求历史"""
        record = {
            'url': url,
            'method': method,
            'status_code': status_code,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
        
        self.request_history.append(record)
        
        # 保持历史记录在限制范围内
        if len(self.request_history) > self.max_history:
            self.request_history = self.request_history[-self.max_history:]
    
    def _handle_rate_limit(self):
        """处理限流"""
        # 增加延迟
        self.throttler.min_delay *= 1.5
        self.throttler.max_delay *= 1.5
        
        # 等待更长时间
        wait_time = random.uniform(10, 30)
        self.logger.info(f"遇到限流，等待 {wait_time:.1f} 秒")
        time.sleep(wait_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        throttler_stats = self.throttler.get_stats()
        
        return {
            'throttler': throttler_stats,
            'errors': self.error_stats,
            'request_history_count': len(self.request_history),
            'proxy_count': len(self.proxy_rotator.proxies),
            'failed_proxy_count': len(self.proxy_rotator.failed_proxies)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.error_stats = {
            'total_errors': 0,
            'timeout_errors': 0,
            'connection_errors': 0,
            'http_errors': 0,
            'proxy_errors': 0
        }
        self.request_history = []
        self.throttler.request_count = 0
        self.throttler.start_time = time.time()
    
    def close(self):
        """关闭管理器"""
        self.session_manager.close()
        self.logger.info("反爬虫管理器已关闭")


# 预定义的反爬虫配置
ANTI_CRAWLER_CONFIGS = {
    'conservative': {
        'min_delay': 2.0,
        'max_delay': 8.0,
        'max_retries': 5
    },
    'moderate': {
        'min_delay': 1.0,
        'max_delay': 5.0,
        'max_retries': 3
    },
    'aggressive': {
        'min_delay': 0.5,
        'max_delay': 2.0,
        'max_retries': 2
    }
}


def create_anti_crawler_manager(mode: str = 'moderate') -> AntiCrawlerManager:
    """创建反爬虫管理器"""
    config = ANTI_CRAWLER_CONFIGS.get(mode, ANTI_CRAWLER_CONFIGS['moderate'])
    return AntiCrawlerManager(config)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    manager = create_anti_crawler_manager('moderate')
    
    # 测试请求
    response = manager.make_request('https://httpbin.org/user-agent')
    if response:
        print("测试请求成功")
        print(f"状态码: {response.status_code}")
    
    # 显示统计信息
    stats = manager.get_stats()
    print(f"统计信息: {stats}")
    
    manager.close()
