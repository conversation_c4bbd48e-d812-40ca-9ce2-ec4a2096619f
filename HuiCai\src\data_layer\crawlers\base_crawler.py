#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫基类
提供通用的爬虫功能和反爬虫机制

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import aiohttp
import random
import time
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
import logging
from datetime import datetime, date
import json
from urllib.parse import urljoin, urlparse
import hashlib


class BaseCrawler(ABC):
    """爬虫基类"""
    
    def __init__(self, config_manager, logger: logging.Logger, lottery_type: str):
        """
        初始化爬虫基类
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
            lottery_type: 彩票类型
        """
        self.config_manager = config_manager
        self.logger = logger
        self.lottery_type = lottery_type
        
        # 爬虫配置
        self.crawler_config = config_manager.get('crawler', {})
        self.lottery_config = config_manager.get_lottery_config(lottery_type)
        
        # 反爬虫配置
        self.user_agents = self.crawler_config.get('user_agents', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ])
        
        self.request_delay = self.crawler_config.get('request_delay', 1)
        self.timeout = self.crawler_config.get('timeout', 30)
        self.retry_times = self.crawler_config.get('retry_times', 3)
        
        # HTTP会话
        self.session = None
        
        # 请求统计
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_request_time = 0
    
    async def initialize(self):
        """初始化爬虫"""
        # 创建HTTP会话
        connector = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self._get_default_headers()
        )
        
        self.logger.info(f"{self.lottery_type}爬虫初始化完成")
    
    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def _get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = self._get_default_headers()
        headers['User-Agent'] = random.choice(self.user_agents)
        return headers
    
    async def _delay_request(self):
        """请求延迟"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_delay:
            delay = self.request_delay - time_since_last
            await asyncio.sleep(delay)
        
        self.last_request_time = time.time()
    
    async def _make_request(self, url: str, method: str = 'GET', 
                           headers: Optional[Dict] = None,
                           params: Optional[Dict] = None,
                           data: Optional[Dict] = None,
                           json_data: Optional[Dict] = None) -> Optional[aiohttp.ClientResponse]:
        """
        发起HTTP请求
        
        Args:
            url: 请求URL
            method: 请求方法
            headers: 请求头
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            
        Returns:
            aiohttp.ClientResponse: 响应对象
        """
        if not self.session:
            await self.initialize()
        
        # 请求延迟
        await self._delay_request()
        
        # 合并请求头
        request_headers = self._get_random_headers()
        if headers:
            request_headers.update(headers)
        
        self.request_count += 1
        
        for attempt in range(self.retry_times):
            try:
                self.logger.debug(f"请求 {url} (尝试 {attempt + 1}/{self.retry_times})")
                
                async with self.session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    params=params,
                    data=data,
                    json=json_data
                ) as response:
                    
                    if response.status == 200:
                        self.success_count += 1
                        return response
                    else:
                        self.logger.warning(f"请求失败，状态码: {response.status}")
                        
            except Exception as e:
                self.logger.warning(f"请求异常 (尝试 {attempt + 1}): {e}")
                
                if attempt < self.retry_times - 1:
                    # 指数退避
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    await asyncio.sleep(wait_time)
        
        self.error_count += 1
        self.logger.error(f"请求最终失败: {url}")
        return None
    
    async def get_html(self, url: str, **kwargs) -> Optional[str]:
        """获取HTML内容"""
        response = await self._make_request(url, **kwargs)
        if response:
            try:
                return await response.text(encoding='utf-8')
            except Exception as e:
                self.logger.error(f"读取HTML内容失败: {e}")
        return None
    
    async def get_json(self, url: str, **kwargs) -> Optional[Dict]:
        """获取JSON内容"""
        response = await self._make_request(url, **kwargs)
        if response:
            try:
                return await response.json()
            except Exception as e:
                self.logger.error(f"读取JSON内容失败: {e}")
        return None
    
    def _generate_cache_key(self, url: str, params: Optional[Dict] = None) -> str:
        """生成缓存键"""
        key_data = f"{url}_{params or ''}"
        return f"crawler_{self.lottery_type}_{hashlib.md5(key_data.encode()).hexdigest()}"
    
    @abstractmethod
    async def crawl_latest_draw(self) -> Optional[Dict[str, Any]]:
        """
        爬取最新开奖数据
        
        Returns:
            Dict: 开奖数据
        """
        pass
    
    @abstractmethod
    async def crawl_historical_draws(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        爬取历史开奖数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict]: 历史开奖数据列表
        """
        pass
    
    @abstractmethod
    def parse_draw_data(self, raw_data: Any) -> Optional[Dict[str, Any]]:
        """
        解析开奖数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            Dict: 解析后的开奖数据
        """
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        success_rate = (self.success_count / self.request_count * 100) if self.request_count > 0 else 0
        
        return {
            'lottery_type': self.lottery_type,
            'request_count': self.request_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': round(success_rate, 2)
        }
    
    async def close(self):
        """关闭爬虫"""
        if self.session:
            await self.session.close()
            self.logger.info(f"{self.lottery_type}爬虫已关闭")
    
    def __del__(self):
        """析构函数"""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
