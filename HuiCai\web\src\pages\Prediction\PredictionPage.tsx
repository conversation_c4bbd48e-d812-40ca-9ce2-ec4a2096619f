import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Select,
  Button,
  Space,
  Spin,
  Alert,
  Progress,
  Tag,
  Divider,
  Typography,
  Statistic,
  Table,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import {
  RocketOutlined,
  TrophyOutlined,
  ExperimentOutlined,
  HistoryOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import {
  makePrediction,
  fetchPredictionHistory,
  setSelectedLotteryType,
  setSelectedMethod,
  clearCurrentPrediction,
} from '../../store/slices/predictionSlice';
import './PredictionPage.css';

const { Option } = Select;
const { Title, Text } = Typography;

const PredictionPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const {
    currentPrediction,
    history,
    loading,
    error,
    selectedLotteryType,
    selectedMethod,
  } = useAppSelector((state) => state.prediction);

  const [evaluateModalVisible, setEvaluateModalVisible] = useState(false);
  const [selectedPrediction, setSelectedPrediction] = useState<any>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    dispatch(fetchPredictionHistory({ limit: 20 }));
  }, [dispatch]);

  const lotteryOptions = [
    { value: 'shuangseqiu', label: '双色球', description: '6红球+1蓝球' },
    { value: 'daletou', label: '大乐透', description: '5前区+2后区' },
    { value: 'fucai3d', label: '福彩3D', description: '3位数字' },
  ];

  const methodOptions = [
    { value: 'traditional', label: '传统机器学习', icon: <ExperimentOutlined />, color: 'blue' },
    { value: 'chinese', label: '中国特色算法', icon: <StarOutlined />, color: 'gold' },
    { value: 'deeplearning', label: '深度学习', icon: <RocketOutlined />, color: 'purple' },
    { value: 'comprehensive', label: '综合预测', icon: <TrophyOutlined />, color: 'green' },
  ];

  const handlePredict = () => {
    dispatch(makePrediction({ lotteryType: selectedLotteryType, method: selectedMethod }));
  };

  const handleLotteryChange = (value: string) => {
    dispatch(setSelectedLotteryType(value));
    dispatch(clearCurrentPrediction());
  };

  const handleMethodChange = (value: string) => {
    dispatch(setSelectedMethod(value));
    dispatch(clearCurrentPrediction());
  };

  const handleEvaluate = (prediction: any) => {
    setSelectedPrediction(prediction);
    setEvaluateModalVisible(true);
  };

  const handleEvaluateSubmit = (values: any) => {
    // 这里处理评估逻辑
    console.log('Evaluate:', selectedPrediction, values);
    setEvaluateModalVisible(false);
    form.resetFields();
  };

  const renderPredictionResult = () => {
    if (!currentPrediction) return null;

    const { predicted_numbers, confidence, method, details } = currentPrediction;

    return (
      <Card title="预测结果" className="prediction-result">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div className="predicted-numbers">
              <Title level={4}>推荐号码</Title>
              <div className="numbers-display">
                {predicted_numbers.map((num, index) => (
                  <span key={index} className={`number-ball ${getNumberClass(index, selectedLotteryType)}`}>
                    {String(num).padStart(2, '0')}
                  </span>
                ))}
              </div>
            </div>
          </Col>
          
          <Col xs={24} sm={8}>
            <Statistic
              title="置信度"
              value={Math.round(confidence * 100)}
              suffix="%"
              valueStyle={{ color: confidence > 0.7 ? '#3f8600' : confidence > 0.5 ? '#1890ff' : '#cf1322' }}
            />
            <Progress 
              percent={Math.round(confidence * 100)} 
              status={confidence > 0.7 ? 'success' : confidence > 0.5 ? 'normal' : 'exception'}
            />
          </Col>
          
          <Col xs={24} sm={8}>
            <Statistic
              title="预测方法"
              value={methodOptions.find(m => m.value === method)?.label || method}
              prefix={methodOptions.find(m => m.value === method)?.icon}
            />
          </Col>
          
          <Col xs={24} sm={8}>
            <Statistic
              title="彩票类型"
              value={lotteryOptions.find(l => l.value === selectedLotteryType)?.label}
            />
          </Col>
        </Row>

        {details && (
          <>
            <Divider />
            <Title level={5}>详细分析</Title>
            <div className="prediction-details">
              {renderPredictionDetails(details, method)}
            </div>
          </>
        )}
      </Card>
    );
  };

  const renderPredictionDetails = (details: any, method: string) => {
    switch (method) {
      case 'comprehensive':
        return (
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card size="small" title="各模型置信度">
                {details.confidence_analysis && Object.entries(details.confidence_analysis).map(([model, conf]: [string, any]) => (
                  <div key={model} style={{ marginBottom: 8 }}>
                    <Text>{getModelName(model)}: </Text>
                    <Progress percent={Math.round(conf * 100)} size="small" />
                  </div>
                ))}
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="策略建议">
                <Text>{details.final_recommendation?.strategy_advice || '暂无建议'}</Text>
                <br />
                <Tag color={getRiskColor(details.final_recommendation?.risk_assessment)}>
                  {details.final_recommendation?.risk_assessment || '风险未知'}
                </Tag>
              </Card>
            </Col>
          </Row>
        );
      case 'chinese':
        return (
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card size="small" title="五行分析">
                <Text>主导五行: {details.wuxing_result?.dominant_wuxing || '未知'}</Text>
                <br />
                <Text>平衡度: {details.wuxing_result?.balance_score?.toFixed(3) || '0'}</Text>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" title="八卦分析">
                <Text>主卦: {details.bagua_result?.main_bagua || '未知'}</Text>
                <br />
                <Text>和谐度: {details.bagua_result?.harmony_score?.toFixed(3) || '0'}</Text>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" title="运势分析">
                <Text>运势等级: {details.fortune_result?.fortune_level || '未知'}</Text>
                <br />
                <Text>运势评分: {details.fortune_result?.fortune_score?.toFixed(3) || '0'}</Text>
              </Card>
            </Col>
          </Row>
        );
      default:
        return <Text>暂无详细分析信息</Text>;
    }
  };

  const getNumberClass = (index: number, lotteryType: string) => {
    if (lotteryType === 'shuangseqiu') {
      return index < 6 ? 'red-ball' : 'blue-ball';
    } else if (lotteryType === 'daletou') {
      return index < 5 ? 'red-ball' : 'blue-ball';
    }
    return 'normal-ball';
  };

  const getModelName = (model: string) => {
    const names: { [key: string]: string } = {
      'traditional_ml': '传统ML',
      'chinese_algorithms': '中国算法',
      'deep_learning': '深度学习',
    };
    return names[model] || model;
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case '低风险': return 'green';
      case '中等风险': return 'orange';
      case '高风险': return 'red';
      default: return 'default';
    }
  };

  const historyColumns = [
    {
      title: '预测日期',
      dataIndex: 'prediction_date',
      key: 'prediction_date',
    },
    {
      title: '彩票类型',
      dataIndex: 'lottery_type',
      key: 'lottery_type',
      render: (type: string) => lotteryOptions.find(l => l.value === type)?.label || type,
    },
    {
      title: '预测号码',
      dataIndex: 'predicted_numbers',
      key: 'predicted_numbers',
      render: (numbers: number[]) => (
        <code style={{ background: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>
          {numbers.join(', ')}
        </code>
      ),
    },
    {
      title: '模型类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (type: string) => {
        const method = methodOptions.find(m => m.value === type);
        return method ? <Tag color={method.color}>{method.label}</Tag> : type;
      },
    },
    {
      title: '准确率',
      dataIndex: 'accuracy',
      key: 'accuracy',
      render: (accuracy: number) => accuracy ? `${Math.round(accuracy * 100)}%` : '待验证',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => handleEvaluate(record)}>
            评估
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="prediction-page">
      <Row gutter={[16, 16]}>
        {/* 预测控制面板 */}
        <Col span={24}>
          <Card title="智能预测" extra={<HistoryOutlined />}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={8} md={6}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong>选择彩票类型</Text>
                  <Select
                    value={selectedLotteryType}
                    onChange={handleLotteryChange}
                    style={{ width: '100%' }}
                  >
                    {lotteryOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div>
                          <div>{option.label}</div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {option.description}
                          </Text>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Space>
              </Col>
              
              <Col xs={24} sm={8} md={6}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong>选择预测方法</Text>
                  <Select
                    value={selectedMethod}
                    onChange={handleMethodChange}
                    style={{ width: '100%' }}
                  >
                    {methodOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <Space>
                          {option.icon}
                          <span>{option.label}</span>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Space>
              </Col>
              
              <Col xs={24} sm={8} md={6}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong>开始预测</Text>
                  <Button
                    type="primary"
                    size="large"
                    icon={<RocketOutlined />}
                    onClick={handlePredict}
                    loading={loading}
                    style={{ width: '100%' }}
                  >
                    开始预测
                  </Button>
                </Space>
              </Col>
            </Row>
            
            {error && (
              <Alert
                message="预测失败"
                description={error}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        </Col>

        {/* 预测结果 */}
        {loading && (
          <Col span={24}>
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>
                  <Text>正在进行智能预测分析...</Text>
                </div>
              </div>
            </Card>
          </Col>
        )}

        {currentPrediction && !loading && (
          <Col span={24}>
            {renderPredictionResult()}
          </Col>
        )}

        {/* 历史预测记录 */}
        <Col span={24}>
          <Card title="历史预测记录">
            <Table
              dataSource={history}
              columns={historyColumns}
              pagination={{ pageSize: 10 }}
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 评估模态框 */}
      <Modal
        title="评估预测结果"
        open={evaluateModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setEvaluateModalVisible(false)}
      >
        <Form form={form} onFinish={handleEvaluateSubmit} layout="vertical">
          <Form.Item label="实际开奖号码" name="actualNumbers" rules={[{ required: true }]}>
            <Space.Compact>
              <InputNumber min={1} max={50} placeholder="号码1" />
              <InputNumber min={1} max={50} placeholder="号码2" />
              <InputNumber min={1} max={50} placeholder="号码3" />
              {/* 根据彩票类型动态添加更多输入框 */}
            </Space.Compact>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PredictionPage;
