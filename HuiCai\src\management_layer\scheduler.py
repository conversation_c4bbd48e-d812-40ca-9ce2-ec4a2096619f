#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务调度器
负责定时任务的调度和管理

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
from typing import Dict, List, Any, Optional, Callable
import logging
from datetime import datetime, time, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
import pytz


class Scheduler:
    """任务调度器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化任务调度器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 调度器配置
        self.scheduler_config = config_manager.get('scheduler', {})
        self.timezone = pytz.timezone(self.scheduler_config.get('timezone', 'Asia/Shanghai'))
        
        # 创建调度器
        self.scheduler = AsyncIOScheduler(
            timezone=self.timezone,
            job_defaults=self.scheduler_config.get('job_defaults', {
                'coalesce': False,
                'max_instances': 1
            })
        )
        
        # 任务注册表
        self.registered_jobs: Dict[str, Dict[str, Any]] = {}
        
        # 状态标志
        self.is_running = False
        
        # 系统组件引用
        self.crawler_manager = None
        self.incremental_learner = None
    
    def set_components(self, crawler_manager, incremental_learner):
        """设置系统组件引用"""
        self.crawler_manager = crawler_manager
        self.incremental_learner = incremental_learner
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            self.logger.warning("调度器已在运行")
            return
        
        try:
            # 注册默认任务
            await self._register_default_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            self.logger.info("任务调度器启动成功")
            
        except Exception as e:
            self.logger.error(f"任务调度器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            
            self.logger.info("任务调度器已停止")
            
        except Exception as e:
            self.logger.error(f"停止调度器失败: {e}")
    
    async def _register_default_jobs(self):
        """注册默认任务"""
        try:
            # 1. 数据爬取任务
            await self._register_crawling_jobs()
            
            # 2. 模型训练任务
            await self._register_learning_jobs()
            
            # 3. 系统维护任务
            await self._register_maintenance_jobs()
            
            self.logger.info("默认任务注册完成")
            
        except Exception as e:
            self.logger.error(f"注册默认任务失败: {e}")
            raise
    
    async def _register_crawling_jobs(self):
        """注册爬取任务"""
        # 获取彩票配置
        lottery_configs = self.config_manager.lottery_configs
        
        for lottery_type, config in lottery_configs.items():
            # 获取开奖时间
            draw_days = config.get('draw_days', [])
            draw_time = config.get('draw_time', '21:00')
            
            if not draw_days:
                continue
            
            # 解析开奖时间
            try:
                hour, minute = map(int, draw_time.split(':'))
            except:
                hour, minute = 21, 0
            
            # 为每个开奖日注册任务
            for day in draw_days:
                day_mapping = {
                    'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3,
                    'fri': 4, 'sat': 5, 'sun': 6
                }
                
                if day not in day_mapping:
                    continue
                
                day_of_week = day_mapping[day]
                
                # 开奖后30分钟开始爬取
                crawl_hour = hour
                crawl_minute = minute + 30
                if crawl_minute >= 60:
                    crawl_hour += 1
                    crawl_minute -= 60
                
                job_id = f"crawl_{lottery_type}_{day}"
                
                # 注册定时爬取任务
                self.scheduler.add_job(
                    func=self._crawl_lottery_data,
                    trigger=CronTrigger(
                        day_of_week=day_of_week,
                        hour=crawl_hour,
                        minute=crawl_minute,
                        timezone=self.timezone
                    ),
                    args=[lottery_type],
                    id=job_id,
                    name=f"爬取{config.get('name', lottery_type)}数据",
                    replace_existing=True
                )
                
                self.registered_jobs[job_id] = {
                    'type': 'crawling',
                    'lottery_type': lottery_type,
                    'schedule': f"{day} {crawl_hour:02d}:{crawl_minute:02d}"
                }
                
                self.logger.info(f"注册爬取任务: {job_id}")
        
        # 注册每日数据检查任务
        self.scheduler.add_job(
            func=self._daily_data_check,
            trigger=CronTrigger(
                hour=8,
                minute=0,
                timezone=self.timezone
            ),
            id="daily_data_check",
            name="每日数据检查",
            replace_existing=True
        )
        
        self.registered_jobs["daily_data_check"] = {
            'type': 'maintenance',
            'schedule': "daily 08:00"
        }
    
    async def _register_learning_jobs(self):
        """注册学习任务"""
        # 每小时执行增量学习
        self.scheduler.add_job(
            func=self._hourly_learning,
            trigger=IntervalTrigger(
                hours=1,
                timezone=self.timezone
            ),
            id="hourly_learning",
            name="每小时增量学习",
            replace_existing=True
        )
        
        self.registered_jobs["hourly_learning"] = {
            'type': 'learning',
            'schedule': "every hour"
        }
        
        # 每日模型评估
        self.scheduler.add_job(
            func=self._daily_model_evaluation,
            trigger=CronTrigger(
                hour=2,
                minute=0,
                timezone=self.timezone
            ),
            id="daily_model_evaluation",
            name="每日模型评估",
            replace_existing=True
        )
        
        self.registered_jobs["daily_model_evaluation"] = {
            'type': 'learning',
            'schedule': "daily 02:00"
        }
    
    async def _register_maintenance_jobs(self):
        """注册维护任务"""
        # 每日日志清理
        self.scheduler.add_job(
            func=self._daily_log_cleanup,
            trigger=CronTrigger(
                hour=3,
                minute=0,
                timezone=self.timezone
            ),
            id="daily_log_cleanup",
            name="每日日志清理",
            replace_existing=True
        )
        
        self.registered_jobs["daily_log_cleanup"] = {
            'type': 'maintenance',
            'schedule': "daily 03:00"
        }
        
        # 每周数据备份
        self.scheduler.add_job(
            func=self._weekly_data_backup,
            trigger=CronTrigger(
                day_of_week=0,  # 周一
                hour=4,
                minute=0,
                timezone=self.timezone
            ),
            id="weekly_data_backup",
            name="每周数据备份",
            replace_existing=True
        )
        
        self.registered_jobs["weekly_data_backup"] = {
            'type': 'maintenance',
            'schedule': "weekly monday 04:00"
        }
    
    async def _crawl_lottery_data(self, lottery_type: str):
        """爬取彩票数据任务"""
        try:
            self.logger.info(f"执行定时爬取任务: {lottery_type}")
            
            if not self.crawler_manager:
                self.logger.error("爬虫管理器未初始化")
                return
            
            # 爬取最新数据
            success = await self.crawler_manager.crawl_latest(lottery_type)
            
            if success:
                self.logger.info(f"{lottery_type} 定时爬取任务完成")
            else:
                self.logger.error(f"{lottery_type} 定时爬取任务失败")
                
        except Exception as e:
            self.logger.error(f"执行爬取任务失败: {e}")
    
    async def _daily_data_check(self):
        """每日数据检查任务"""
        try:
            self.logger.info("执行每日数据检查")
            
            if not self.crawler_manager:
                return
            
            # 检查所有彩票类型的数据完整性
            lottery_types = self.crawler_manager.get_supported_lotteries()
            
            for lottery_type in lottery_types:
                # 这里可以添加数据完整性检查逻辑
                pass
            
            self.logger.info("每日数据检查完成")
            
        except Exception as e:
            self.logger.error(f"每日数据检查失败: {e}")
    
    async def _hourly_learning(self):
        """每小时学习任务"""
        try:
            self.logger.info("执行每小时增量学习")
            
            if not self.incremental_learner:
                self.logger.error("增量学习器未初始化")
                return
            
            # 这里增量学习器会自动处理所有彩票类型
            self.logger.info("每小时增量学习完成")
            
        except Exception as e:
            self.logger.error(f"每小时学习任务失败: {e}")
    
    async def _daily_model_evaluation(self):
        """每日模型评估任务"""
        try:
            self.logger.info("执行每日模型评估")
            
            if not self.incremental_learner:
                return
            
            # 评估所有模型的性能
            lottery_configs = self.config_manager.lottery_configs
            
            for lottery_type in lottery_configs.keys():
                model_info = self.incremental_learner.get_model_info(lottery_type)
                if model_info:
                    self.logger.info(f"{lottery_type} 模型准确率: {model_info['accuracy']:.4f}")
            
            self.logger.info("每日模型评估完成")
            
        except Exception as e:
            self.logger.error(f"每日模型评估失败: {e}")
    
    async def _daily_log_cleanup(self):
        """每日日志清理任务"""
        try:
            self.logger.info("执行每日日志清理")
            
            # 这里可以添加日志清理逻辑
            # 例如删除超过30天的日志文件
            
            self.logger.info("每日日志清理完成")
            
        except Exception as e:
            self.logger.error(f"每日日志清理失败: {e}")
    
    async def _weekly_data_backup(self):
        """每周数据备份任务"""
        try:
            self.logger.info("执行每周数据备份")
            
            # 这里可以添加数据备份逻辑
            
            self.logger.info("每周数据备份完成")
            
        except Exception as e:
            self.logger.error(f"每周数据备份失败: {e}")
    
    def add_job(self, func: Callable, trigger, job_id: str, name: str, **kwargs):
        """添加自定义任务"""
        try:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True,
                **kwargs
            )
            
            self.registered_jobs[job_id] = {
                'type': 'custom',
                'name': name,
                'trigger': str(trigger)
            }
            
            self.logger.info(f"添加自定义任务: {job_id}")
            
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
    
    def remove_job(self, job_id: str):
        """移除任务"""
        try:
            self.scheduler.remove_job(job_id)
            
            if job_id in self.registered_jobs:
                del self.registered_jobs[job_id]
            
            self.logger.info(f"移除任务: {job_id}")
            
        except Exception as e:
            self.logger.error(f"移除任务失败: {e}")
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        """获取所有任务信息"""
        jobs = []
        
        for job in self.scheduler.get_jobs():
            job_info = {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time,
                'trigger': str(job.trigger)
            }
            
            if job.id in self.registered_jobs:
                job_info.update(self.registered_jobs[job.id])
            
            jobs.append(job_info)
        
        return jobs
    
    def get_job_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        jobs = self.get_jobs()
        
        stats = {
            'total_jobs': len(jobs),
            'running': self.is_running,
            'job_types': {}
        }
        
        for job in jobs:
            job_type = job.get('type', 'unknown')
            if job_type not in stats['job_types']:
                stats['job_types'][job_type] = 0
            stats['job_types'][job_type] += 1
        
        return stats
