#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫测试脚本
测试反爬虫模块和专用爬虫功能

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import logging
import asyncio
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.crawler_system.anti_crawler import create_anti_crawler_manager
from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler
from src.crawler_system.daletou_crawler import DaletouCrawler
from src.crawler_system.crawler_manager import CrawlerManager


def test_anti_crawler():
    """测试反爬虫模块"""
    print("🛡️ 测试反爬虫模块...")
    
    try:
        # 创建反爬虫管理器
        manager = create_anti_crawler_manager('moderate')
        
        # 测试请求
        print("  📡 测试HTTP请求...")
        response = manager.make_request('https://httpbin.org/user-agent')
        
        if response:
            print(f"    ✅ 请求成功，状态码: {response.status_code}")
            try:
                data = response.json()
                print(f"    📱 User-Agent: {data.get('user-agent', 'N/A')}")
            except:
                print("    📱 响应不是JSON格式")
        else:
            print("    ❌ 请求失败")
        
        # 测试多次请求（验证节流）
        print("  ⏱️ 测试请求节流...")
        start_time = time.time()
        
        for i in range(3):
            response = manager.make_request('https://httpbin.org/delay/1')
            if response:
                print(f"    ✅ 请求 {i+1} 成功")
            else:
                print(f"    ❌ 请求 {i+1} 失败")
        
        elapsed = time.time() - start_time
        print(f"    ⏱️ 总耗时: {elapsed:.2f} 秒")
        
        # 显示统计信息
        stats = manager.get_stats()
        print("  📊 统计信息:")
        print(f"    - 总请求数: {stats['throttler']['total_requests']}")
        print(f"    - 每分钟请求数: {stats['throttler']['requests_per_minute']:.2f}")
        print(f"    - 总错误数: {stats['errors']['total_errors']}")
        
        manager.close()
        print("  ✅ 反爬虫模块测试完成")
        
    except Exception as e:
        print(f"  ❌ 反爬虫模块测试失败: {e}")


def test_shuangseqiu_crawler():
    """测试双色球爬虫"""
    print("🔴 测试双色球爬虫...")
    
    try:
        crawler = ShuangseqiuCrawler()
        
        # 测试爬取最新数据
        print("  📡 爬取最新开奖数据...")
        result = crawler.crawl_latest_data(3)
        
        if result['status'] == 'success':
            print(f"    ✅ 爬取成功，数据源: {result['source']}")
            print(f"    📊 获取 {result['count']} 期数据")
            
            # 显示部分数据
            if result['data']:
                latest = result['data'][0]
                print(f"    🎯 最新一期: {latest.get('issue', 'N/A')}")
                print(f"    📅 开奖日期: {latest.get('date', 'N/A')}")
                red_balls = latest.get('red_balls', [])
                blue_ball = latest.get('blue_ball', 0)
                if red_balls and blue_ball:
                    red_str = ','.join([f"{ball:02d}" for ball in red_balls])
                    print(f"    🔴 开奖号码: {red_str} + {blue_ball:02d}")
        else:
            print(f"    ❌ 爬取失败: {result.get('error', '未知错误')}")
        
        # 显示爬虫统计
        stats = crawler.get_crawler_stats()
        print("  📊 爬虫统计:")
        print(f"    - 爬虫类型: {stats['crawler_type']}")
        print(f"    - 当前数据源: {stats['current_source']}")
        print(f"    - 数据源总数: {stats['total_sources']}")
        
        crawler.close()
        print("  ✅ 双色球爬虫测试完成")
        
    except Exception as e:
        print(f"  ❌ 双色球爬虫测试失败: {e}")


def test_daletou_crawler():
    """测试大乐透爬虫"""
    print("🔵 测试大乐透爬虫...")
    
    try:
        crawler = DaletouCrawler()
        
        # 测试爬取最新数据
        print("  📡 爬取最新开奖数据...")
        result = crawler.crawl_latest_data(3)
        
        if result['status'] == 'success':
            print(f"    ✅ 爬取成功，数据源: {result['source']}")
            print(f"    📊 获取 {result['count']} 期数据")
            
            # 显示部分数据
            if result['data']:
                latest = result['data'][0]
                print(f"    🎯 最新一期: {latest.get('issue', 'N/A')}")
                print(f"    📅 开奖日期: {latest.get('date', 'N/A')}")
                front_balls = latest.get('front_balls', [])
                back_balls = latest.get('back_balls', [])
                if front_balls and back_balls:
                    front_str = ','.join([f"{ball:02d}" for ball in front_balls])
                    back_str = ','.join([f"{ball:02d}" for ball in back_balls])
                    print(f"    🔵 开奖号码: {front_str} + {back_str}")
        else:
            print(f"    ❌ 爬取失败: {result.get('error', '未知错误')}")
        
        # 显示爬虫统计
        stats = crawler.get_crawler_stats()
        print("  📊 爬虫统计:")
        print(f"    - 爬虫类型: {stats['crawler_type']}")
        print(f"    - 当前数据源: {stats['current_source']}")
        print(f"    - 数据源总数: {stats['total_sources']}")
        
        crawler.close()
        print("  ✅ 大乐透爬虫测试完成")
        
    except Exception as e:
        print(f"  ❌ 大乐透爬虫测试失败: {e}")


def test_crawler_manager():
    """测试爬虫管理器"""
    print("🎛️ 测试爬虫管理器...")
    
    try:
        manager = CrawlerManager()
        
        # 测试手动爬取双色球
        print("  🔴 手动爬取双色球数据...")
        result = manager.manual_crawl('shuangseqiu', 2)
        if result['status'] == 'success':
            print(f"    ✅ 爬取成功，保存 {result['saved_count']} 条记录")
        else:
            print(f"    ❌ 爬取失败: {result.get('error', '未知错误')}")
        
        # 测试手动爬取大乐透
        print("  🔵 手动爬取大乐透数据...")
        result = manager.manual_crawl('daletou', 2)
        if result['status'] == 'success':
            print(f"    ✅ 爬取成功，保存 {result['saved_count']} 条记录")
        else:
            print(f"    ❌ 爬取失败: {result.get('error', '未知错误')}")
        
        # 显示爬虫状态
        status = manager.get_crawler_status()
        print("  📊 爬虫管理器状态:")
        print(f"    - 调度器运行: {status['scheduler_running']}")
        print(f"    - 总爬取次数: {status['statistics']['total_crawls']}")
        print(f"    - 成功次数: {status['statistics']['successful_crawls']}")
        print(f"    - 失败次数: {status['statistics']['failed_crawls']}")
        print(f"    - 总记录数: {status['statistics']['total_records']}")
        
        # 显示数据摘要
        summary = manager.get_data_summary()
        print("  📈 数据摘要:")
        for lottery_type, data in summary.items():
            if lottery_type != 'error':
                print(f"    - {lottery_type}:")
                if data.get('latest_issue'):
                    latest = data['latest_issue']
                    print(f"      • 最新期号: {latest.get('issue', 'N/A')}")
                    print(f"      • 开奖日期: {latest.get('date', 'N/A')}")
                print(f"      • 总记录数: {data.get('total_records', 0)}")
        
        manager.close()
        print("  ✅ 爬虫管理器测试完成")
        
    except Exception as e:
        print(f"  ❌ 爬虫管理器测试失败: {e}")


def main():
    """主函数"""
    print("🎯 HuiCai爬虫系统测试")
    print("="*50)
    
    # 设置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 测试反爬虫模块
        test_anti_crawler()
        print()
        
        # 测试双色球爬虫
        test_shuangseqiu_crawler()
        print()
        
        # 测试大乐透爬虫
        test_daletou_crawler()
        print()
        
        # 测试爬虫管理器
        test_crawler_manager()
        print()
        
        print("✅ 所有测试完成！")
        print("="*50)
        print("📝 测试总结:")
        print("  🛡️ 反爬虫模块: 提供User-Agent轮换、请求节流、代理支持")
        print("  🔴 双色球爬虫: 支持多数据源、自动切换、数据验证")
        print("  🔵 大乐透爬虫: 支持多数据源、自动切换、数据验证")
        print("  🎛️ 爬虫管理器: 统一管理、定时调度、数据存储")
        print("  💾 本地数据库: 四个专用数据库，支持数据和学习优化")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        logging.error(f"测试异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
