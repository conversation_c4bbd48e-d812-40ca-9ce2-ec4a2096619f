#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球专用爬虫
专门爬取双色球开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from bs4 import BeautifulSoup
import requests

from .anti_crawler import create_anti_crawler_manager


class ShuangseqiuCrawler:
    """双色球爬虫"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化反爬虫管理器
        self.anti_crawler = create_anti_crawler_manager(
            self.config.get('anti_crawler_mode', 'moderate')
        )
        
        # 数据源配置
        self.data_sources = [
            {
                'name': '中国福利彩票官网',
                'url': 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
                'type': 'api',
                'params': {'name': 'ssq', 'issueCount': 30}
            },
            {
                'name': '500彩票网',
                'url': 'https://datachart.500.com/ssq/history/newinc/history.php',
                'type': 'html',
                'params': {'limit': 30}
            },
            {
                'name': '新浪彩票',
                'url': 'https://datachart.sina.com.cn/lottery/ssq/view/basic_ssq.html',
                'type': 'html',
                'params': {}
            }
        ]
        
        # 当前使用的数据源索引
        self.current_source_index = 0
        
        # 数据验证规则
        self.validation_rules = {
            'red_balls': {
                'count': 6,
                'min_value': 1,
                'max_value': 33,
                'unique': True
            },
            'blue_ball': {
                'count': 1,
                'min_value': 1,
                'max_value': 16
            }
        }
    
    def crawl_latest_data(self, count: int = 10) -> Dict[str, Any]:
        """爬取最新开奖数据"""
        self.logger.info(f"开始爬取双色球最新 {count} 期数据")
        
        for attempt in range(len(self.data_sources)):
            source = self.data_sources[self.current_source_index]
            
            try:
                self.logger.info(f"尝试从 {source['name']} 爬取数据")
                
                if source['type'] == 'api':
                    data = self._crawl_from_api(source, count)
                else:
                    data = self._crawl_from_html(source, count)
                
                if data and len(data) > 0:
                    self.logger.info(f"成功从 {source['name']} 爬取到 {len(data)} 期数据")
                    return {
                        'status': 'success',
                        'source': source['name'],
                        'data': data,
                        'count': len(data),
                        'timestamp': datetime.now().isoformat()
                    }
                
            except Exception as e:
                self.logger.error(f"从 {source['name']} 爬取数据失败: {e}")
            
            # 切换到下一个数据源
            self.current_source_index = (self.current_source_index + 1) % len(self.data_sources)
        
        return {
            'status': 'failed',
            'error': '所有数据源都无法获取数据',
            'timestamp': datetime.now().isoformat()
        }
    
    def _crawl_from_api(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从API爬取数据"""
        url = source['url']
        params = source['params'].copy()
        params['issueCount'] = count
        
        response = self.anti_crawler.make_request(
            url,
            method='POST',
            json=params,
            headers={
                'Content-Type': 'application/json',
                'Referer': 'https://www.cwl.gov.cn/',
                'Origin': 'https://www.cwl.gov.cn'
            }
        )
        
        if not response:
            return []
        
        try:
            data = response.json()
            if data.get('state') == 0 and 'result' in data:
                return self._parse_official_api_data(data['result'])
        except Exception as e:
            self.logger.error(f"解析API数据失败: {e}")
        
        return []
    
    def _crawl_from_html(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从HTML页面爬取数据"""
        url = source['url']
        
        response = self.anti_crawler.make_request(url)
        if not response:
            return []
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if '500.com' in url:
                return self._parse_500_data(soup, count)
            elif 'sina.com' in url:
                return self._parse_sina_data(soup, count)
            
        except Exception as e:
            self.logger.error(f"解析HTML数据失败: {e}")
        
        return []
    
    def _parse_official_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析官方API数据"""
        parsed_data = []
        
        for item in result_data:
            try:
                # 解析开奖号码
                red_balls = item.get('red', '').split(',')
                blue_ball = item.get('blue', '')
                
                if len(red_balls) == 6 and blue_ball:
                    red_balls = [int(ball) for ball in red_balls]
                    blue_ball = int(blue_ball)
                    
                    # 验证数据
                    if self._validate_numbers(red_balls, blue_ball):
                        parsed_data.append({
                            'issue': item.get('code', ''),
                            'date': item.get('date', ''),
                            'red_balls': red_balls,
                            'blue_ball': blue_ball,
                            'sales_amount': item.get('sales', 0),
                            'pool_amount': item.get('poolmoney', 0),
                            'source': '官方API'
                        })
                
            except Exception as e:
                self.logger.warning(f"解析单条官方数据失败: {e}")
                continue
        
        return parsed_data
    
    def _parse_500_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析500彩票网数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'class': 'kj_tablelist02'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析红球
                        red_balls = []
                        for i in range(2, 8):  # 红球在第2-7列
                            red_balls.append(int(cells[i].text.strip()))
                        
                        # 解析蓝球
                        blue_ball = int(cells[8].text.strip())
                        
                        if self._validate_numbers(red_balls, blue_ball):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '500彩票网'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析500彩票网单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析500彩票网数据失败: {e}")
        
        return parsed_data
    
    def _parse_sina_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析新浪彩票数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'id': 'ssq_data'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 10:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析红球
                        red_balls = []
                        for i in range(2, 8):  # 红球在第2-7列
                            red_balls.append(int(cells[i].text.strip()))
                        
                        # 解析蓝球
                        blue_ball = int(cells[8].text.strip())
                        
                        if self._validate_numbers(red_balls, blue_ball):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '新浪彩票'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析新浪彩票单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析新浪彩票数据失败: {e}")
        
        return parsed_data
    
    def _validate_numbers(self, red_balls: List[int], blue_ball: int) -> bool:
        """验证开奖号码"""
        try:
            # 验证红球
            red_rules = self.validation_rules['red_balls']
            if len(red_balls) != red_rules['count']:
                return False
            
            for ball in red_balls:
                if ball < red_rules['min_value'] or ball > red_rules['max_value']:
                    return False
            
            if red_rules['unique'] and len(set(red_balls)) != len(red_balls):
                return False
            
            # 验证蓝球
            blue_rules = self.validation_rules['blue_ball']
            if blue_ball < blue_rules['min_value'] or blue_ball > blue_rules['max_value']:
                return False
            
            return True
            
        except Exception:
            return False
    
    def crawl_historical_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """爬取历史数据"""
        self.logger.info(f"爬取双色球历史数据: {start_date} 到 {end_date}")
        
        # 这里可以实现更复杂的历史数据爬取逻辑
        # 目前返回最新数据作为示例
        return self.crawl_latest_data(100)
    
    def get_crawler_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        anti_crawler_stats = self.anti_crawler.get_stats()
        
        return {
            'crawler_type': '双色球爬虫',
            'current_source': self.data_sources[self.current_source_index]['name'],
            'total_sources': len(self.data_sources),
            'anti_crawler_stats': anti_crawler_stats,
            'validation_rules': self.validation_rules
        }
    
    def close(self):
        """关闭爬虫"""
        self.anti_crawler.close()
        self.logger.info("双色球爬虫已关闭")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    crawler = ShuangseqiuCrawler()
    
    # 测试爬取最新数据
    result = crawler.crawl_latest_data(5)
    print(f"爬取结果: {result}")
    
    # 显示统计信息
    stats = crawler.get_crawler_stats()
    print(f"爬虫统计: {stats}")
    
    crawler.close()
