#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球专用爬虫
专门爬取双色球开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from bs4 import BeautifulSoup
import requests

from .anti_crawler import create_anti_crawler_manager


class ShuangseqiuCrawler:
    """双色球爬虫"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化反爬虫管理器
        self.anti_crawler = create_anti_crawler_manager(
            self.config.get('anti_crawler_mode', 'moderate')
        )
        
        # 数据源配置
        self.data_sources = [
            {
                'name': '模拟数据源',
                'url': 'mock://shuangseqiu/data',
                'type': 'mock',
                'method': 'GET',
                'headers': {},
                'params': {'count': 30}
            },
            {
                'name': '中国福利彩票官网API',
                'url': 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
                'type': 'api',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://www.cwl.gov.cn/kjxx/ssq/',
                    'Origin': 'https://www.cwl.gov.cn'
                },
                'params': {'name': 'ssq', 'issueCount': 30}
            },
            {
                'name': '彩票开奖API',
                'url': 'https://api.apiopen.top/api/lottery',
                'type': 'api',
                'method': 'GET',
                'headers': {
                    'Accept': 'application/json'
                },
                'params': {'type': 'ssq', 'count': 30}
            }
        ]
        
        # 当前使用的数据源索引
        self.current_source_index = 0
        
        # 数据验证规则
        self.validation_rules = {
            'red_balls': {
                'count': 6,
                'min_value': 1,
                'max_value': 33,
                'unique': True
            },
            'blue_ball': {
                'count': 1,
                'min_value': 1,
                'max_value': 16
            }
        }
    
    def crawl_latest_data(self, count: int = 10) -> Dict[str, Any]:
        """爬取最新开奖数据"""
        self.logger.info(f"开始爬取双色球最新 {count} 期数据")
        
        for attempt in range(len(self.data_sources)):
            source = self.data_sources[self.current_source_index]
            
            try:
                self.logger.info(f"尝试从 {source['name']} 爬取数据")

                # 根据数据源类型选择爬取方法
                if source['type'] == 'mock':
                    data = self._generate_mock_data(count)
                else:
                    data = self._crawl_from_api(source, count)
                
                if data and len(data) > 0:
                    self.logger.info(f"成功从 {source['name']} 爬取到 {len(data)} 期数据")
                    return {
                        'status': 'success',
                        'source': source['name'],
                        'data': data,
                        'count': len(data),
                        'timestamp': datetime.now().isoformat()
                    }
                
            except Exception as e:
                self.logger.error(f"从 {source['name']} 爬取数据失败: {e}")
            
            # 切换到下一个数据源
            self.current_source_index = (self.current_source_index + 1) % len(self.data_sources)
        
        return {
            'status': 'failed',
            'error': '所有数据源都无法获取数据',
            'timestamp': datetime.now().isoformat()
        }
    
    def _crawl_from_api(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从API爬取数据"""
        url = source['url']
        method = source.get('method', 'POST')
        headers = source.get('headers', {})
        params = source['params'].copy()

        # 根据不同API调整参数
        if 'cwl.gov.cn' in url and method == 'POST':
            params['issueCount'] = count
            # 使用form-data格式
            response = self.anti_crawler.make_request(
                url, method=method, data=params, headers=headers
            )
        elif 'cwl.gov.cn' in url and method == 'GET':
            params['issueCount'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        elif 'apiopen.top' in url:
            params['count'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        else:
            params['issueCount'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )

        if not response:
            return []

        try:
            data = response.json()

            # 根据不同API解析数据
            if 'cwl.gov.cn' in url:
                if data.get('state') == 0 and 'result' in data:
                    return self._parse_official_api_data(data['result'])
            elif 'apiopen.top' in url:
                if data.get('code') == 200 and 'result' in data:
                    return self._parse_apiopen_data(data['result'])
            else:
                # 通用解析
                if 'result' in data or 'data' in data:
                    result_data = data.get('result', data.get('data', []))
                    return self._parse_generic_api_data(result_data)

        except Exception as e:
            self.logger.error(f"解析API数据失败: {e}")

        return []
    
    def _crawl_from_html(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从HTML页面爬取数据"""
        url = source['url']
        
        response = self.anti_crawler.make_request(url)
        if not response:
            return []
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if '500.com' in url:
                return self._parse_500_data(soup, count)
            elif 'sina.com' in url:
                return self._parse_sina_data(soup, count)
            
        except Exception as e:
            self.logger.error(f"解析HTML数据失败: {e}")
        
        return []
    
    def _parse_official_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析官方API数据"""
        parsed_data = []
        
        for item in result_data:
            try:
                # 解析开奖号码
                red_balls = item.get('red', '').split(',')
                blue_ball = item.get('blue', '')
                
                if len(red_balls) == 6 and blue_ball:
                    red_balls = [int(ball) for ball in red_balls]
                    blue_ball = int(blue_ball)
                    
                    # 验证数据
                    if self._validate_numbers(red_balls, blue_ball):
                        parsed_data.append({
                            'issue': item.get('code', ''),
                            'date': item.get('date', ''),
                            'red_balls': red_balls,
                            'blue_ball': blue_ball,
                            'sales_amount': item.get('sales', 0),
                            'pool_amount': item.get('poolmoney', 0),
                            'source': '官方API'
                        })
                
            except Exception as e:
                self.logger.warning(f"解析单条官方数据失败: {e}")
                continue
        
        return parsed_data
    
    def _parse_500_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析500彩票网数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'class': 'kj_tablelist02'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析红球
                        red_balls = []
                        for i in range(2, 8):  # 红球在第2-7列
                            red_balls.append(int(cells[i].text.strip()))
                        
                        # 解析蓝球
                        blue_ball = int(cells[8].text.strip())
                        
                        if self._validate_numbers(red_balls, blue_ball):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '500彩票网'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析500彩票网单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析500彩票网数据失败: {e}")
        
        return parsed_data
    
    def _parse_sina_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析新浪彩票数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'id': 'ssq_data'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 10:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析红球
                        red_balls = []
                        for i in range(2, 8):  # 红球在第2-7列
                            red_balls.append(int(cells[i].text.strip()))
                        
                        # 解析蓝球
                        blue_ball = int(cells[8].text.strip())
                        
                        if self._validate_numbers(red_balls, blue_ball):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '新浪彩票'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析新浪彩票单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析新浪彩票数据失败: {e}")
        
        return parsed_data

    def _parse_apiopen_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析apiopen.top API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 解析开奖号码
                lottery_no = item.get('lottery_no', '')
                lottery_date = item.get('lottery_date', '')
                lottery_res = item.get('lottery_res', '')

                if lottery_res:
                    # 分割号码 "01,07,12,23,28,33,12"
                    numbers = lottery_res.split(',')
                    if len(numbers) >= 7:
                        red_balls = [int(num) for num in numbers[:6]]
                        blue_ball = int(numbers[6])

                        # 验证数据
                        if self._validate_numbers(red_balls, blue_ball):
                            parsed_data.append({
                                'issue': lottery_no,
                                'date': lottery_date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': 'apiopen.top'
                            })

            except Exception as e:
                self.logger.warning(f"解析apiopen数据失败: {e}")
                continue

        return parsed_data

    def _parse_generic_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析通用API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 尝试多种可能的字段名
                issue = item.get('issue', item.get('code', item.get('period', '')))
                date = item.get('date', item.get('opentime', item.get('time', '')))

                # 尝试解析号码
                red_balls = []
                blue_ball = 0

                # 方式1: 直接字段
                if 'red_balls' in item and 'blue_ball' in item:
                    red_balls = item['red_balls']
                    blue_ball = item['blue_ball']
                # 方式2: 号码字符串
                elif 'numbers' in item:
                    numbers_str = item['numbers']
                    if '+' in numbers_str:
                        parts = numbers_str.split('+')
                        red_part = parts[0].strip()
                        blue_part = parts[1].strip()
                        red_balls = [int(x) for x in red_part.split(',')]
                        blue_ball = int(blue_part)
                    else:
                        numbers = [int(x) for x in numbers_str.split(',')]
                        if len(numbers) >= 7:
                            red_balls = numbers[:6]
                            blue_ball = numbers[6]

                # 验证数据
                if len(red_balls) == 6 and blue_ball > 0:
                    if self._validate_numbers(red_balls, blue_ball):
                        parsed_data.append({
                            'issue': issue,
                            'date': date,
                            'red_balls': red_balls,
                            'blue_ball': blue_ball,
                            'sales_amount': item.get('sales', 0),
                            'pool_amount': item.get('pool', 0),
                            'source': '通用API'
                        })

            except Exception as e:
                self.logger.warning(f"解析通用API数据失败: {e}")
                continue

        return parsed_data

    def _generate_mock_data(self, count: int) -> List[Dict[str, Any]]:
        """生成模拟双色球数据"""
        import random
        from datetime import datetime, timedelta

        mock_data = []
        base_date = datetime.now() - timedelta(days=count * 3)  # 每3天一期

        for i in range(count):
            issue_date = base_date + timedelta(days=i * 3)
            issue = f"2024{str(150 + i).zfill(3)}"  # 从2024150开始

            # 生成红球（1-33，6个不重复）
            red_balls = sorted(random.sample(range(1, 34), 6))

            # 生成蓝球（1-16，1个）
            blue_ball = random.randint(1, 16)

            # 生成销售额和奖池（模拟真实数据）
            sales_amount = random.randint(200000000, 500000000)  # 2-5亿
            pool_amount = random.randint(500000000, 1500000000)  # 5-15亿

            mock_data.append({
                'issue': issue,
                'date': issue_date.strftime('%Y-%m-%d'),
                'red_balls': red_balls,
                'blue_ball': blue_ball,
                'sales_amount': sales_amount,
                'pool_amount': pool_amount,
                'source': '模拟数据源'
            })

        # 按日期倒序排列（最新的在前面）
        mock_data.reverse()

        self.logger.info(f"生成了 {len(mock_data)} 期模拟双色球数据")
        return mock_data

    def _validate_numbers(self, red_balls: List[int], blue_ball: int) -> bool:
        """验证开奖号码"""
        try:
            # 验证红球
            red_rules = self.validation_rules['red_balls']
            if len(red_balls) != red_rules['count']:
                return False
            
            for ball in red_balls:
                if ball < red_rules['min_value'] or ball > red_rules['max_value']:
                    return False
            
            if red_rules['unique'] and len(set(red_balls)) != len(red_balls):
                return False
            
            # 验证蓝球
            blue_rules = self.validation_rules['blue_ball']
            if blue_ball < blue_rules['min_value'] or blue_ball > blue_rules['max_value']:
                return False
            
            return True
            
        except Exception:
            return False
    
    def crawl_historical_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """爬取历史数据"""
        self.logger.info(f"爬取双色球历史数据: {start_date} 到 {end_date}")
        
        # 这里可以实现更复杂的历史数据爬取逻辑
        # 目前返回最新数据作为示例
        return self.crawl_latest_data(100)
    
    def get_crawler_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        anti_crawler_stats = self.anti_crawler.get_stats()
        
        return {
            'crawler_type': '双色球爬虫',
            'current_source': self.data_sources[self.current_source_index]['name'],
            'total_sources': len(self.data_sources),
            'anti_crawler_stats': anti_crawler_stats,
            'validation_rules': self.validation_rules
        }
    
    def close(self):
        """关闭爬虫"""
        self.anti_crawler.close()
        self.logger.info("双色球爬虫已关闭")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    crawler = ShuangseqiuCrawler()
    
    # 测试爬取最新数据
    result = crawler.crawl_latest_data(5)
    print(f"爬取结果: {result}")
    
    # 显示统计信息
    stats = crawler.get_crawler_stats()
    print(f"爬虫统计: {stats}")
    
    crawler.close()
