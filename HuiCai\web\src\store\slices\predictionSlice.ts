import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { predictionAPI } from '../../services/api';

export interface PredictionResult {
  id: string;
  lottery_type: string;
  prediction_date: string;
  model_type: string;
  predicted_numbers: number[];
  confidence: number;
  method: 'traditional' | 'chinese' | 'deeplearning' | 'comprehensive';
  details?: any;
}

export interface PredictionHistory {
  id: string;
  lottery_type: string;
  prediction_date: string;
  predicted_numbers: number[];
  actual_numbers?: number[];
  accuracy?: number;
  match_count?: number;
  model_type: string;
}

interface PredictionState {
  currentPrediction: PredictionResult | null;
  history: PredictionHistory[];
  loading: boolean;
  error: string | null;
  selectedLotteryType: string;
  selectedMethod: string;
}

const initialState: PredictionState = {
  currentPrediction: null,
  history: [],
  loading: false,
  error: null,
  selectedLotteryType: 'shuangseqiu',
  selectedMethod: 'comprehensive',
};

// 异步thunks
export const makePrediction = createAsyncThunk(
  'prediction/makePrediction',
  async (params: { lotteryType: string; method: string }) => {
    const response = await predictionAPI.predict(params.lotteryType, params.method);
    return response.data;
  }
);

export const fetchPredictionHistory = createAsyncThunk(
  'prediction/fetchHistory',
  async (params: { lotteryType?: string; limit?: number }) => {
    const response = await predictionAPI.getHistory(params);
    return response.data;
  }
);

export const evaluatePrediction = createAsyncThunk(
  'prediction/evaluate',
  async (params: { predictionId: string; actualNumbers: number[] }) => {
    const response = await predictionAPI.evaluate(params.predictionId, params.actualNumbers);
    return response.data;
  }
);

const predictionSlice = createSlice({
  name: 'prediction',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedLotteryType: (state, action: PayloadAction<string>) => {
      state.selectedLotteryType = action.payload;
    },
    setSelectedMethod: (state, action: PayloadAction<string>) => {
      state.selectedMethod = action.payload;
    },
    clearCurrentPrediction: (state) => {
      state.currentPrediction = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // makePrediction
      .addCase(makePrediction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(makePrediction.fulfilled, (state, action) => {
        state.loading = false;
        state.currentPrediction = action.payload;
      })
      .addCase(makePrediction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '预测失败';
      })
      // fetchPredictionHistory
      .addCase(fetchPredictionHistory.fulfilled, (state, action) => {
        state.history = action.payload;
      })
      // evaluatePrediction
      .addCase(evaluatePrediction.fulfilled, (state, action) => {
        const index = state.history.findIndex(h => h.id === action.payload.id);
        if (index !== -1) {
          state.history[index] = { ...state.history[index], ...action.payload };
        }
      });
  },
});

export const {
  clearError,
  setSelectedLotteryType,
  setSelectedMethod,
  clearCurrentPrediction,
} = predictionSlice.actions;

export default predictionSlice.reducer;
