/* HuiCai Web App 样式 */

.App {
  min-height: 100vh;
  background-color: #f0f2f5;
}

/* 布局样式 */
.app-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

/* Logo样式 */
.logo {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 16px;
  border-radius: 8px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin-right: 12px;
}

.logo-text {
  color: white;
}

/* 仪表板样式 */
.dashboard {
  padding: 0;
}

.dashboard .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dashboard .ant-statistic-title {
  color: #666;
  font-size: 14px;
}

.dashboard .ant-statistic-content {
  color: #262626;
}

/* 预测页面样式 */
.prediction-page {
  padding: 0;
}

.prediction-result {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: none;
}

.predicted-numbers {
  text-align: center;
  margin-bottom: 24px;
}

.numbers-display {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.number-ball {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s;
}

.number-ball:hover {
  transform: scale(1.1);
}

.red-ball {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.blue-ball {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.normal-ball {
  background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
}

.prediction-details {
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo {
    margin: 8px;
    padding: 12px 16px;
  }
  
  .logo-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-right: 8px;
  }
  
  .numbers-display {
    gap: 6px;
  }
  
  .number-ball {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
  
  .app-header {
    padding: 0 16px;
  }
}

@media (max-width: 576px) {
  .numbers-display {
    gap: 4px;
  }
  
  .number-ball {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.prediction-result {
  animation: fadeIn 0.5s ease-out;
}

/* 卡片悬停效果 */
.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

/* 状态标签 */
.status-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 图表容器 */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* 统计卡片 */
.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-card .ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* 时间线样式 */
.ant-timeline-item-content {
  margin-left: 16px;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 按钮样式 */
.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 进度条样式 */
.ant-progress-bg {
  background: linear-gradient(to right, #667eea, #764ba2);
}

/* 标签样式 */
.ant-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 输入框样式 */
.ant-input:focus,
.ant-input-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 选择器样式 */
.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #667eea;
}

.ant-select-focused .ant-select-selector {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 模态框样式 */
.ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px 8px 0 0;
}

.ant-modal-title {
  color: white;
}

.ant-modal-close {
  color: white;
}

.ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* 消息提示样式 */
.ant-message-notice-content {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 通知样式 */
.ant-notification-notice {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
}

/* 抽屉样式 */
.ant-drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ant-drawer-title {
  color: white;
}

.ant-drawer-close {
  color: white;
}

/* 分页样式 */
.ant-pagination-item-active {
  background: #667eea;
  border-color: #667eea;
}

.ant-pagination-item-active a {
  color: white;
}

/* 开关样式 */
.ant-switch-checked {
  background: #667eea;
}

/* 滑块样式 */
.ant-slider-track {
  background: linear-gradient(to right, #667eea, #764ba2);
}

.ant-slider-handle {
  border-color: #667eea;
}

.ant-slider-handle:focus {
  box-shadow: 0 0 0 5px rgba(102, 126, 234, 0.2);
}
