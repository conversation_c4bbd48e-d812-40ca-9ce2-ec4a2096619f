#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai仪表盘服务器
简化的Web服务器，只提供仪表盘页面

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import json
import logging
from datetime import datetime
import asyncio
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class DashboardHandler(BaseHTTPRequestHandler):
    """仪表盘请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.crawler_manager = None
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/dashboard':
            self.serve_dashboard()
        elif self.path == '/api/predictions':
            self.serve_predictions()
        elif self.path == '/api/latest-data':
            self.serve_latest_data()
        else:
            self.send_error(404, "Page not found")
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/update-data':
            self.handle_update_data()
        else:
            self.send_error(404, "API not found")
    
    def serve_dashboard(self):
        """提供仪表盘页面"""
        try:
            dashboard_path = project_root / 'dashboard_template.html'
            with open(dashboard_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error loading dashboard: {e}")
    
    def serve_predictions(self):
        """提供预测数据"""
        try:
            # 模拟预测数据
            predictions = {
                'shuangseqiu': {
                    'lstm': {
                        'red_balls': [5, 12, 19, 26, 31, 33],
                        'blue_ball': 8
                    },
                    'rf': {
                        'red_balls': [2, 15, 22, 27, 30, 32],
                        'blue_ball': 11
                    },
                    'svm': {
                        'red_balls': [7, 14, 18, 25, 29, 31],
                        'blue_ball': 6
                    },
                    'ensemble': {
                        'red_balls': [1, 9, 17, 23, 28, 33],
                        'blue_ball': 15
                    }
                },
                'daletou': {
                    'lstm': {
                        'front_balls': [2, 15, 22, 28, 34],
                        'back_balls': [5, 11]
                    },
                    'rf': {
                        'front_balls': [8, 16, 23, 29, 33],
                        'back_balls': [2, 9]
                    },
                    'svm': {
                        'front_balls': [4, 11, 18, 25, 32],
                        'back_balls': [7, 12]
                    },
                    'ensemble': {
                        'front_balls': [6, 13, 20, 27, 35],
                        'back_balls': [1, 10]
                    }
                }
            }
            
            self.send_json_response(predictions)
            
        except Exception as e:
            self.send_error(500, f"Error getting predictions: {e}")
    
    def serve_latest_data(self):
        """提供最新开奖数据"""
        try:
            # 尝试从数据库获取真实数据
            latest_data = self.get_latest_from_database()
            
            if not latest_data:
                # 如果没有数据，返回模拟数据
                latest_data = {
                    'shuangseqiu': {
                        'issue': '2025001',
                        'date': '2025-01-15',
                        'red_balls': [3, 8, 16, 21, 28, 33],
                        'blue_ball': 12
                    },
                    'daletou': {
                        'issue': '25001',
                        'date': '2025-01-15',
                        'front_balls': [5, 12, 19, 26, 31],
                        'back_balls': [3, 8]
                    }
                }
            
            self.send_json_response(latest_data)
            
        except Exception as e:
            self.send_error(500, f"Error getting latest data: {e}")
    
    def handle_update_data(self):
        """处理数据更新请求"""
        try:
            print("🔄 收到数据更新请求...")

            # 获取爬虫管理器实例
            if not hasattr(self.server, 'crawler_manager'):
                from src.crawler_system.crawler_manager import CrawlerManager
                self.server.crawler_manager = CrawlerManager()
                print("✅ 爬虫管理器初始化完成")

            manager = self.server.crawler_manager

            # 爬取双色球数据
            print("📡 开始爬取双色球数据...")
            ssq_result = manager.manual_crawl('shuangseqiu', 3)
            print(f"🔴 双色球爬取结果: {ssq_result['status']}")

            # 爬取大乐透数据
            print("📡 开始爬取大乐透数据...")
            dlt_result = manager.manual_crawl('daletou', 3)
            print(f"🔵 大乐透爬取结果: {dlt_result['status']}")

            # 获取最新的开奖数据用于页面更新
            latest_data = self.get_latest_from_database()

            # 如果数据库中有数据，使用真实数据；否则从爬取结果中提取
            if not latest_data:
                print("📊 数据库中没有数据，从爬取结果中提取...")
                latest_data = {}

                # 从双色球爬取结果中提取最新数据
                if ssq_result['status'] == 'success' and ssq_result.get('data'):
                    ssq_latest = ssq_result['data'][0]
                    latest_data['shuangseqiu'] = {
                        'issue': ssq_latest.get('issue', '未知期号'),
                        'date': ssq_latest.get('date', '未知日期'),
                        'red_balls': ssq_latest.get('red_balls', []),
                        'blue_ball': ssq_latest.get('blue_ball', 0)
                    }
                    print(f"从爬取结果提取双色球数据: {latest_data['shuangseqiu']}")
                else:
                    print(f"双色球爬取失败或无数据: {ssq_result}")

                # 从大乐透爬取结果中提取最新数据
                if dlt_result['status'] == 'success' and dlt_result.get('data'):
                    dlt_latest = dlt_result['data'][0]
                    latest_data['daletou'] = {
                        'issue': dlt_latest.get('issue', '未知期号'),
                        'date': dlt_latest.get('date', '未知日期'),
                        'front_balls': dlt_latest.get('front_balls', []),
                        'back_balls': dlt_latest.get('back_balls', [])
                    }
                    print(f"从爬取结果提取大乐透数据: {latest_data['daletou']}")
                else:
                    print(f"大乐透爬取失败或无数据: {dlt_result}")
            else:
                print("📊 使用数据库中的最新数据")

            # 模拟算法准确率更新
            algorithms = {
                'lstm': 85.2 + (hash(str(datetime.now())) % 10 - 5) * 0.1,
                'random_forest': 78.9 + (hash(str(datetime.now())) % 8 - 4) * 0.1,
                'svm': 72.4 + (hash(str(datetime.now())) % 6 - 3) * 0.1,
                'ensemble': 88.7 + (hash(str(datetime.now())) % 4 - 2) * 0.1
            }

            result = {
                'status': 'success',
                'shuangseqiu': {
                    'status': ssq_result['status'],
                    'data': latest_data.get('shuangseqiu'),
                    'saved_count': ssq_result.get('saved_count', 0)
                },
                'daletou': {
                    'status': dlt_result['status'],
                    'data': latest_data.get('daletou'),
                    'saved_count': dlt_result.get('saved_count', 0)
                },
                'algorithms': algorithms,
                'timestamp': datetime.now().isoformat(),
                'message': '数据更新完成'
            }

            print(f"✅ 数据更新完成: 双色球{ssq_result.get('saved_count', 0)}条, 大乐透{dlt_result.get('saved_count', 0)}条")
            self.send_json_response(result)

        except Exception as e:
            print(f"❌ 数据更新失败: {e}")
            import traceback
            traceback.print_exc()

            error_response = {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'message': '数据更新失败'
            }
            self.send_json_response(error_response, status=500)

    def get_latest_from_database(self):
        """从数据库获取最新数据"""
        try:
            from src.database.local_database_manager import LocalDatabaseManager
            from src.database.data_access_layer import ShuangseqiuDataAccess, DaletouDataAccess

            db_manager = LocalDatabaseManager()

            # 获取双色球最新数据
            ssq_data = ShuangseqiuDataAccess(db_manager)
            ssq_latest = ssq_data.get_latest_results(1)

            # 获取大乐透最新数据
            dlt_data = DaletouDataAccess(db_manager)
            dlt_latest = dlt_data.get_latest_results(1)

            result = {}

            if ssq_latest:
                ssq = ssq_latest[0]
                result['shuangseqiu'] = {
                    'issue': ssq.get('issue', '未知期号'),
                    'date': ssq.get('date', '未知日期'),
                    'red_balls': ssq.get('red_balls', []),
                    'blue_ball': ssq.get('blue_ball', 0)
                }
                print(f"从数据库获取双色球数据: {result['shuangseqiu']}")

            if dlt_latest:
                dlt = dlt_latest[0]
                result['daletou'] = {
                    'issue': dlt.get('issue', '未知期号'),
                    'date': dlt.get('date', '未知日期'),
                    'front_balls': dlt.get('front_balls', []),
                    'back_balls': dlt.get('back_balls', [])
                }
                print(f"从数据库获取大乐透数据: {result['daletou']}")

            db_manager.close_all_connections()
            return result if result else None

        except Exception as e:
            print(f"从数据库获取数据失败: {e}")
            return None

    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)

        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))

    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def run_dashboard_server(port=8000):
    """运行仪表盘服务器"""
    print(f"🎯 启动HuiCai仪表盘服务器...")
    print(f"📍 端口: {port}")
    print(f"🌐 访问地址: http://localhost:{port}")
    print("-" * 50)

    try:
        server = HTTPServer(('localhost', port), DashboardHandler)
        print(f"✅ 服务器启动成功！")
        print(f"📊 仪表盘地址: http://localhost:{port}/dashboard")
        print(f"🔄 按 Ctrl+C 停止服务器")
        print("-" * 50)

        server.serve_forever()

    except KeyboardInterrupt:
        print(f"\n🛑 收到停止信号，正在关闭服务器...")
        server.shutdown()
        print(f"✅ 服务器已关闭")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='HuiCai仪表盘服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口 (默认: 8000)')

    args = parser.parse_args()

    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    run_dashboard_server(args.port)

if __name__ == "__main__":
    main()
