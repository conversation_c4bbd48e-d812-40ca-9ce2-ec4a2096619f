#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的爬虫

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fixed_crawler():
    """测试修复后的爬虫"""
    print("🔴 测试修复后的双色球爬虫...")
    print("-" * 50)
    
    try:
        from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler
        
        # 创建爬虫实例
        crawler = ShuangseqiuCrawler()
        print("✅ 爬虫创建成功")
        
        # 测试爬取数据
        print("\n📡 爬取最新3期数据...")
        result = crawler.crawl_latest_data(3)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            
            print(f"\n🎯 开奖数据详情:")
            for i, draw in enumerate(result['data']):
                issue = draw.get('issue', '未知期号')
                date = draw.get('date', '未知日期')
                red_balls = draw.get('red_balls', [])
                blue_ball = draw.get('blue_ball', 0)
                source = draw.get('source', '未知来源')
                
                red_str = ' '.join([f"{ball:02d}" for ball in red_balls])
                print(f"  {i+1}. 期号: {issue}")
                print(f"     日期: {date}")
                print(f"     号码: {red_str} + {blue_ball:02d}")
                print(f"     来源: {source}")
                print()
            
            # 验证期号和日期不为None
            all_valid = True
            for draw in result['data']:
                if not draw.get('issue') or not draw.get('date'):
                    all_valid = False
                    print(f"❌ 发现无效数据: {draw}")
            
            if all_valid:
                print("✅ 所有数据都包含有效的期号和日期！")
                return True
            else:
                print("❌ 部分数据缺少期号或日期")
                return False
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            crawler.close()
        except:
            pass

def test_database_insert():
    """测试数据库插入"""
    print("\n💾 测试数据库插入...")
    print("-" * 50)
    
    try:
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import ShuangseqiuDataAccess
        
        # 创建数据库管理器
        db_manager = LocalDatabaseManager()
        ssq_data = ShuangseqiuDataAccess(db_manager)
        
        # 测试数据
        test_data = [
            {
                'issue': '2025001',
                'date': '2025-01-01',
                'red_balls': [1, 7, 12, 23, 28, 33],
                'blue_ball': 12,
                'sales_amount': 300000000,
                'pool_amount': 800000000,
                'source': '测试数据'
            }
        ]
        
        print("📝 插入测试数据...")
        inserted_count = ssq_data.batch_insert_draw_results(test_data)
        print(f"✅ 成功插入 {inserted_count} 条记录")
        
        # 查询验证
        print("🔍 查询验证...")
        latest = ssq_data.get_latest_results(1)
        if latest:
            print(f"✅ 查询成功，最新记录: {latest[0]}")
            return True
        else:
            print("❌ 查询失败，未找到记录")
            return False
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            db_manager.close_all_connections()
        except:
            pass

def main():
    """主函数"""
    print("🎯 快速测试修复后的爬虫系统")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success_count = 0
    total_tests = 2
    
    # 测试爬虫
    if test_fixed_crawler():
        success_count += 1
    
    # 测试数据库
    if test_database_insert():
        success_count += 1
    
    # 显示结果
    print("=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！修复成功！")
        print("\n✅ 修复内容:")
        print("  🔧 期号和日期解析优化")
        print("  🔧 默认期号和日期生成")
        print("  🔧 数据库约束问题解决")
        print("  🔧 错误处理改进")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
