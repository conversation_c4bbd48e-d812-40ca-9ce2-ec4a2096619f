#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国特色算法模块
集成五行八卦、生肖周期、节气时令等传统文化算法

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
import logging
from enum import Enum
import math


class WuXing(Enum):
    """五行枚举"""
    JIN = "金"    # 金
    MU = "木"     # 木
    SHUI = "水"   # 水
    HUO = "火"    # 火
    TU = "土"     # 土


class ShengXiao(Enum):
    """生肖枚举"""
    SHU = "鼠"    # 鼠
    NIU = "牛"    # 牛
    HU = "虎"     # 虎
    TU = "兔"     # 兔
    LONG = "龙"   # 龙
    SHE = "蛇"    # 蛇
    MA = "马"     # 马
    YANG = "羊"   # 羊
    HOU = "猴"    # 猴
    JI = "鸡"     # 鸡
    GOU = "狗"    # 狗
    ZHU = "猪"    # 猪


class ChineseAlgorithms:
    """中国特色算法集合"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化中国特色算法
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 五行相生相克关系
        self.wuxing_sheng = {
            WuXing.JIN: WuXing.SHUI,   # 金生水
            WuXing.SHUI: WuXing.MU,    # 水生木
            WuXing.MU: WuXing.HUO,     # 木生火
            WuXing.HUO: WuXing.TU,     # 火生土
            WuXing.TU: WuXing.JIN      # 土生金
        }
        
        self.wuxing_ke = {
            WuXing.JIN: WuXing.MU,     # 金克木
            WuXing.MU: WuXing.TU,      # 木克土
            WuXing.TU: WuXing.SHUI,    # 土克水
            WuXing.SHUI: WuXing.HUO,   # 水克火
            WuXing.HUO: WuXing.JIN     # 火克金
        }
        
        # 数字与五行对应关系
        self.number_wuxing = {
            1: WuXing.SHUI, 6: WuXing.SHUI,    # 1,6属水
            2: WuXing.HUO, 7: WuXing.HUO,      # 2,7属火
            3: WuXing.MU, 8: WuXing.MU,        # 3,8属木
            4: WuXing.JIN, 9: WuXing.JIN,      # 4,9属金
            5: WuXing.TU, 0: WuXing.TU         # 5,0属土
        }
        
        # 生肖与数字对应关系（按年份）
        self.shengxiao_years = [
            ShengXiao.SHU, ShengXiao.NIU, ShengXiao.HU, ShengXiao.TU,
            ShengXiao.LONG, ShengXiao.SHE, ShengXiao.MA, ShengXiao.YANG,
            ShengXiao.HOU, ShengXiao.JI, ShengXiao.GOU, ShengXiao.ZHU
        ]
        
        # 二十四节气
        self.jieqi_names = [
            "立春", "雨水", "惊蛰", "春分", "清明", "谷雨",
            "立夏", "小满", "芒种", "夏至", "小暑", "大暑",
            "立秋", "处暑", "白露", "秋分", "寒露", "霜降",
            "立冬", "小雪", "大雪", "冬至", "小寒", "大寒"
        ]
        
        # 吉凶数字配置
        self.lucky_numbers = {1, 3, 6, 8, 9, 16, 18, 21, 23, 28, 31, 33}
        self.unlucky_numbers = {4, 7, 14, 17, 22, 26, 29, 34}
        
        self.logger.info("中国特色算法模块初始化完成")
    
    def get_number_wuxing(self, number: int) -> WuXing:
        """获取数字对应的五行"""
        last_digit = number % 10
        return self.number_wuxing.get(last_digit, WuXing.TU)
    
    def get_current_shengxiao(self, target_date: date = None) -> ShengXiao:
        """获取指定日期的生肖"""
        if target_date is None:
            target_date = date.today()
        
        # 生肖以农历年为准，这里简化为公历年
        year_index = (target_date.year - 1900) % 12
        return self.shengxiao_years[year_index]
    
    def get_current_jieqi(self, target_date: date = None) -> str:
        """获取当前节气（简化计算）"""
        if target_date is None:
            target_date = date.today()
        
        # 简化的节气计算，每个月两个节气
        month = target_date.month
        day = target_date.day
        
        jieqi_index = (month - 1) * 2
        if day >= 15:  # 简化：每月15日后为下一个节气
            jieqi_index += 1
        
        return self.jieqi_names[jieqi_index % 24]
    
    def wuxing_analysis(self, numbers: List[int]) -> Dict[str, Any]:
        """
        五行分析算法
        
        Args:
            numbers: 数字列表
            
        Returns:
            Dict: 五行分析结果
        """
        try:
            # 统计各五行的数量
            wuxing_count = {wx: 0 for wx in WuXing}
            
            for num in numbers:
                wx = self.get_number_wuxing(num)
                wuxing_count[wx] += 1
            
            # 计算五行平衡度
            total_count = len(numbers)
            wuxing_balance = {}
            for wx, count in wuxing_count.items():
                wuxing_balance[wx.value] = count / total_count
            
            # 找出主导五行
            dominant_wuxing = max(wuxing_count.items(), key=lambda x: x[1])
            
            # 计算相生相克关系
            sheng_score = 0  # 相生得分
            ke_score = 0     # 相克得分
            
            for i, num1 in enumerate(numbers):
                for j, num2 in enumerate(numbers[i+1:], i+1):
                    wx1 = self.get_number_wuxing(num1)
                    wx2 = self.get_number_wuxing(num2)
                    
                    if self.wuxing_sheng.get(wx1) == wx2 or self.wuxing_sheng.get(wx2) == wx1:
                        sheng_score += 1
                    elif self.wuxing_ke.get(wx1) == wx2 or self.wuxing_ke.get(wx2) == wx1:
                        ke_score += 1
            
            # 预测下一个有利的五行
            next_favorable_wuxing = self.wuxing_sheng.get(dominant_wuxing[0], WuXing.TU)
            
            return {
                'wuxing_distribution': wuxing_balance,
                'dominant_wuxing': dominant_wuxing[0].value,
                'sheng_score': sheng_score,
                'ke_score': ke_score,
                'balance_score': 1 - max(wuxing_balance.values()),  # 平衡度越高越好
                'next_favorable_wuxing': next_favorable_wuxing.value,
                'recommended_numbers': self._get_wuxing_numbers(next_favorable_wuxing)
            }
            
        except Exception as e:
            self.logger.error(f"五行分析失败: {e}")
            return {}
    
    def _get_wuxing_numbers(self, wuxing: WuXing, max_num: int = 33) -> List[int]:
        """获取指定五行对应的数字"""
        numbers = []
        for num in range(1, max_num + 1):
            if self.get_number_wuxing(num) == wuxing:
                numbers.append(num)
        return numbers
    
    def shengxiao_cycle_analysis(self, historical_data: pd.DataFrame, 
                                target_date: date = None) -> Dict[str, Any]:
        """
        生肖周期分析算法
        
        Args:
            historical_data: 历史数据
            target_date: 目标日期
            
        Returns:
            Dict: 生肖周期分析结果
        """
        try:
            if target_date is None:
                target_date = date.today()
            
            current_shengxiao = self.get_current_shengxiao(target_date)
            
            # 分析历史数据中各生肖年份的号码特征
            shengxiao_patterns = {}
            
            for _, row in historical_data.iterrows():
                draw_date = pd.to_datetime(row['draw_date']).date()
                draw_shengxiao = self.get_current_shengxiao(draw_date)
                numbers = row['numbers']
                
                if draw_shengxiao not in shengxiao_patterns:
                    shengxiao_patterns[draw_shengxiao] = []
                
                shengxiao_patterns[draw_shengxiao].extend(numbers)
            
            # 计算当前生肖的号码偏好
            current_pattern = shengxiao_patterns.get(current_shengxiao, [])
            
            if current_pattern:
                # 统计号码频率
                number_freq = {}
                for num in current_pattern:
                    number_freq[num] = number_freq.get(num, 0) + 1
                
                # 计算偏好度
                total_count = len(current_pattern)
                number_preference = {num: count/total_count 
                                   for num, count in number_freq.items()}
                
                # 推荐高频号码
                recommended = sorted(number_preference.items(), 
                                   key=lambda x: x[1], reverse=True)[:10]
            else:
                number_preference = {}
                recommended = []
            
            return {
                'current_shengxiao': current_shengxiao.value,
                'historical_patterns': {sx.value: len(patterns) 
                                      for sx, patterns in shengxiao_patterns.items()},
                'number_preference': number_preference,
                'recommended_numbers': [num for num, _ in recommended],
                'confidence': len(current_pattern) / len(historical_data) if len(historical_data) > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"生肖周期分析失败: {e}")
            return {}
    
    def jieqi_timing_analysis(self, historical_data: pd.DataFrame,
                             target_date: date = None) -> Dict[str, Any]:
        """
        节气时令分析算法
        
        Args:
            historical_data: 历史数据
            target_date: 目标日期
            
        Returns:
            Dict: 节气分析结果
        """
        try:
            if target_date is None:
                target_date = date.today()
            
            current_jieqi = self.get_current_jieqi(target_date)
            
            # 分析各节气的号码特征
            jieqi_patterns = {}
            
            for _, row in historical_data.iterrows():
                draw_date = pd.to_datetime(row['draw_date']).date()
                draw_jieqi = self.get_current_jieqi(draw_date)
                numbers = row['numbers']
                
                if draw_jieqi not in jieqi_patterns:
                    jieqi_patterns[draw_jieqi] = {
                        'numbers': [],
                        'sum_avg': 0,
                        'odd_count': 0,
                        'even_count': 0
                    }
                
                pattern = jieqi_patterns[draw_jieqi]
                pattern['numbers'].extend(numbers)
                pattern['sum_avg'] += sum(numbers)
                pattern['odd_count'] += sum(1 for n in numbers if n % 2 == 1)
                pattern['even_count'] += sum(1 for n in numbers if n % 2 == 0)
            
            # 计算当前节气的特征
            current_pattern = jieqi_patterns.get(current_jieqi, {})
            
            if current_pattern and current_pattern['numbers']:
                numbers_count = len(current_pattern['numbers'])
                draws_count = numbers_count // 7  # 假设每期7个号码
                
                avg_sum = current_pattern['sum_avg'] / draws_count if draws_count > 0 else 0
                odd_ratio = current_pattern['odd_count'] / numbers_count
                even_ratio = current_pattern['even_count'] / numbers_count
                
                # 基于节气特征推荐号码范围
                if avg_sum > 100:  # 高和值节气
                    recommended_range = (15, 33)
                elif avg_sum < 80:  # 低和值节气
                    recommended_range = (1, 20)
                else:  # 中等和值节气
                    recommended_range = (8, 25)
            else:
                avg_sum = 0
                odd_ratio = 0.5
                even_ratio = 0.5
                recommended_range = (1, 33)
            
            return {
                'current_jieqi': current_jieqi,
                'jieqi_characteristics': {
                    'average_sum': avg_sum,
                    'odd_ratio': odd_ratio,
                    'even_ratio': even_ratio
                },
                'recommended_range': recommended_range,
                'seasonal_trend': self._get_seasonal_trend(target_date)
            }
            
        except Exception as e:
            self.logger.error(f"节气时令分析失败: {e}")
            return {}
    
    def _get_seasonal_trend(self, target_date: date) -> str:
        """获取季节趋势"""
        month = target_date.month
        
        if month in [3, 4, 5]:
            return "春季上升"
        elif month in [6, 7, 8]:
            return "夏季旺盛"
        elif month in [9, 10, 11]:
            return "秋季收敛"
        else:
            return "冬季蛰伏"
    
    def yijing_bagua_analysis(self, numbers: List[int]) -> Dict[str, Any]:
        """
        易经八卦分析算法
        
        Args:
            numbers: 数字列表
            
        Returns:
            Dict: 八卦分析结果
        """
        try:
            # 八卦对应关系
            bagua_map = {
                0: "坤", 1: "震", 2: "坎", 3: "兑",
                4: "艮", 5: "离", 6: "巽", 7: "乾"
            }
            
            # 将数字转换为八卦
            bagua_sequence = []
            for num in numbers:
                bagua_index = num % 8
                bagua_sequence.append(bagua_map[bagua_index])
            
            # 计算八卦分布
            bagua_count = {}
            for bagua in bagua_sequence:
                bagua_count[bagua] = bagua_count.get(bagua, 0) + 1
            
            # 主卦分析
            main_bagua = max(bagua_count.items(), key=lambda x: x[1])[0]
            
            # 八卦相配分析
            bagua_harmony = self._calculate_bagua_harmony(bagua_sequence)
            
            # 预测下一个有利八卦
            next_bagua = self._predict_next_bagua(bagua_sequence)
            
            return {
                'bagua_sequence': bagua_sequence,
                'bagua_distribution': bagua_count,
                'main_bagua': main_bagua,
                'harmony_score': bagua_harmony,
                'next_favorable_bagua': next_bagua,
                'recommended_numbers': self._get_bagua_numbers(next_bagua)
            }
            
        except Exception as e:
            self.logger.error(f"易经八卦分析失败: {e}")
            return {}
    
    def _calculate_bagua_harmony(self, bagua_sequence: List[str]) -> float:
        """计算八卦和谐度"""
        # 简化的和谐度计算
        unique_bagua = set(bagua_sequence)
        diversity = len(unique_bagua) / 8  # 多样性
        
        # 相邻八卦的和谐度
        harmony_pairs = [
            ("乾", "坤"), ("震", "巽"), ("坎", "离"), ("艮", "兑")
        ]
        
        harmony_score = 0
        for i in range(len(bagua_sequence) - 1):
            current = bagua_sequence[i]
            next_bagua = bagua_sequence[i + 1]
            
            for pair in harmony_pairs:
                if (current, next_bagua) in [pair, pair[::-1]]:
                    harmony_score += 1
                    break
        
        return (harmony_score / max(1, len(bagua_sequence) - 1)) * diversity
    
    def _predict_next_bagua(self, bagua_sequence: List[str]) -> str:
        """预测下一个有利八卦"""
        if not bagua_sequence:
            return "乾"
        
        # 基于最后一个八卦预测
        last_bagua = bagua_sequence[-1]
        
        # 简化的预测逻辑
        bagua_cycle = ["乾", "兑", "离", "震", "巽", "坎", "艮", "坤"]
        
        try:
            current_index = bagua_cycle.index(last_bagua)
            next_index = (current_index + 1) % 8
            return bagua_cycle[next_index]
        except ValueError:
            return "乾"
    
    def _get_bagua_numbers(self, bagua: str, max_num: int = 33) -> List[int]:
        """获取指定八卦对应的数字"""
        bagua_to_index = {
            "坤": 0, "震": 1, "坎": 2, "兑": 3,
            "艮": 4, "离": 5, "巽": 6, "乾": 7
        }
        
        target_index = bagua_to_index.get(bagua, 0)
        numbers = []
        
        for num in range(1, max_num + 1):
            if num % 8 == target_index:
                numbers.append(num)
        
        return numbers
    
    def lucky_number_analysis(self, numbers: List[int]) -> Dict[str, Any]:
        """
        吉凶数字分析
        
        Args:
            numbers: 数字列表
            
        Returns:
            Dict: 吉凶分析结果
        """
        try:
            lucky_count = sum(1 for num in numbers if num in self.lucky_numbers)
            unlucky_count = sum(1 for num in numbers if num in self.unlucky_numbers)
            neutral_count = len(numbers) - lucky_count - unlucky_count
            
            # 计算吉凶比例
            total = len(numbers)
            lucky_ratio = lucky_count / total if total > 0 else 0
            unlucky_ratio = unlucky_count / total if total > 0 else 0
            neutral_ratio = neutral_count / total if total > 0 else 0
            
            # 整体吉凶评分
            fortune_score = lucky_ratio * 1.0 + neutral_ratio * 0.5 + unlucky_ratio * 0.0
            
            # 推荐更多吉利数字
            available_lucky = [num for num in self.lucky_numbers if num <= 33]
            
            return {
                'lucky_count': lucky_count,
                'unlucky_count': unlucky_count,
                'neutral_count': neutral_count,
                'lucky_ratio': lucky_ratio,
                'unlucky_ratio': unlucky_ratio,
                'neutral_ratio': neutral_ratio,
                'fortune_score': fortune_score,
                'fortune_level': self._get_fortune_level(fortune_score),
                'recommended_lucky_numbers': available_lucky[:10]
            }
            
        except Exception as e:
            self.logger.error(f"吉凶数字分析失败: {e}")
            return {}
    
    def _get_fortune_level(self, score: float) -> str:
        """获取运势等级"""
        if score >= 0.8:
            return "大吉"
        elif score >= 0.6:
            return "中吉"
        elif score >= 0.4:
            return "小吉"
        elif score >= 0.2:
            return "平"
        else:
            return "凶"
    
    def comprehensive_chinese_analysis(self, numbers: List[int], 
                                     historical_data: pd.DataFrame = None,
                                     target_date: date = None) -> Dict[str, Any]:
        """
        综合中国特色分析
        
        Args:
            numbers: 当前数字列表
            historical_data: 历史数据
            target_date: 目标日期
            
        Returns:
            Dict: 综合分析结果
        """
        try:
            result = {
                'analysis_date': target_date or date.today(),
                'input_numbers': numbers
            }
            
            # 五行分析
            result['wuxing_analysis'] = self.wuxing_analysis(numbers)
            
            # 易经八卦分析
            result['bagua_analysis'] = self.yijing_bagua_analysis(numbers)
            
            # 吉凶数字分析
            result['fortune_analysis'] = self.lucky_number_analysis(numbers)
            
            # 如果有历史数据，进行时间相关分析
            if historical_data is not None and not historical_data.empty:
                result['shengxiao_analysis'] = self.shengxiao_cycle_analysis(
                    historical_data, target_date)
                result['jieqi_analysis'] = self.jieqi_timing_analysis(
                    historical_data, target_date)
            
            # 综合评分
            result['comprehensive_score'] = self._calculate_comprehensive_score(result)
            
            # 综合推荐
            result['comprehensive_recommendation'] = self._generate_comprehensive_recommendation(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"综合中国特色分析失败: {e}")
            return {}
    
    def _calculate_comprehensive_score(self, analysis_result: Dict[str, Any]) -> float:
        """计算综合评分"""
        try:
            scores = []
            
            # 五行平衡分
            if 'wuxing_analysis' in analysis_result:
                wuxing = analysis_result['wuxing_analysis']
                balance_score = wuxing.get('balance_score', 0)
                scores.append(balance_score * 0.3)
            
            # 八卦和谐分
            if 'bagua_analysis' in analysis_result:
                bagua = analysis_result['bagua_analysis']
                harmony_score = bagua.get('harmony_score', 0)
                scores.append(harmony_score * 0.3)
            
            # 吉凶分
            if 'fortune_analysis' in analysis_result:
                fortune = analysis_result['fortune_analysis']
                fortune_score = fortune.get('fortune_score', 0)
                scores.append(fortune_score * 0.4)
            
            return sum(scores) if scores else 0.5
            
        except Exception as e:
            self.logger.error(f"计算综合评分失败: {e}")
            return 0.5
    
    def _generate_comprehensive_recommendation(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合推荐"""
        try:
            recommendations = {
                'recommended_numbers': [],
                'avoid_numbers': [],
                'strategy': "",
                'confidence': 0.0
            }
            
            # 收集各算法的推荐
            all_recommendations = []
            
            if 'wuxing_analysis' in analysis_result:
                wuxing_rec = analysis_result['wuxing_analysis'].get('recommended_numbers', [])
                all_recommendations.extend(wuxing_rec)
            
            if 'bagua_analysis' in analysis_result:
                bagua_rec = analysis_result['bagua_analysis'].get('recommended_numbers', [])
                all_recommendations.extend(bagua_rec)
            
            if 'fortune_analysis' in analysis_result:
                fortune_rec = analysis_result['fortune_analysis'].get('recommended_lucky_numbers', [])
                all_recommendations.extend(fortune_rec)
            
            # 统计推荐频次
            number_votes = {}
            for num in all_recommendations:
                number_votes[num] = number_votes.get(num, 0) + 1
            
            # 选择高票数字
            if number_votes:
                sorted_numbers = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
                recommendations['recommended_numbers'] = [num for num, _ in sorted_numbers[:10]]
                recommendations['confidence'] = max(number_votes.values()) / len(analysis_result)
            
            # 避免的数字
            if 'fortune_analysis' in analysis_result:
                recommendations['avoid_numbers'] = list(self.unlucky_numbers)
            
            # 策略建议
            comprehensive_score = analysis_result.get('comprehensive_score', 0.5)
            if comprehensive_score >= 0.7:
                recommendations['strategy'] = "当前数字组合吉利，建议保持类似选择"
            elif comprehensive_score >= 0.4:
                recommendations['strategy'] = "数字组合中等，建议适当调整"
            else:
                recommendations['strategy'] = "建议重新选择，注重五行平衡和吉利数字"
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成综合推荐失败: {e}")
            return {}
