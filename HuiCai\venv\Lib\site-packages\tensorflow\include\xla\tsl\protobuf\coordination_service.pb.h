// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/tsl/protobuf/coordination_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
namespace tensorflow {
class BarrierError;
struct BarrierErrorDefaultTypeInternal;
extern BarrierErrorDefaultTypeInternal _BarrierError_default_instance_;
class BarrierRequest;
struct BarrierRequestDefaultTypeInternal;
extern BarrierRequestDefaultTypeInternal _BarrierRequest_default_instance_;
class BarrierResponse;
struct BarrierResponseDefaultTypeInternal;
extern BarrierResponseDefaultTypeInternal _BarrierResponse_default_instance_;
class CancelBarrierRequest;
struct CancelBarrierRequestDefaultTypeInternal;
extern CancelBarrierRequestDefaultTypeInternal _CancelBarrierRequest_default_instance_;
class CancelBarrierResponse;
struct CancelBarrierResponseDefaultTypeInternal;
extern CancelBarrierResponseDefaultTypeInternal _CancelBarrierResponse_default_instance_;
class CoordinatedTask;
struct CoordinatedTaskDefaultTypeInternal;
extern CoordinatedTaskDefaultTypeInternal _CoordinatedTask_default_instance_;
class CoordinatedTaskStateInfo;
struct CoordinatedTaskStateInfoDefaultTypeInternal;
extern CoordinatedTaskStateInfoDefaultTypeInternal _CoordinatedTaskStateInfo_default_instance_;
class CoordinationServiceError;
struct CoordinationServiceErrorDefaultTypeInternal;
extern CoordinationServiceErrorDefaultTypeInternal _CoordinationServiceError_default_instance_;
class DeleteKeyValueRequest;
struct DeleteKeyValueRequestDefaultTypeInternal;
extern DeleteKeyValueRequestDefaultTypeInternal _DeleteKeyValueRequest_default_instance_;
class DeleteKeyValueResponse;
struct DeleteKeyValueResponseDefaultTypeInternal;
extern DeleteKeyValueResponseDefaultTypeInternal _DeleteKeyValueResponse_default_instance_;
class DeviceInfo;
struct DeviceInfoDefaultTypeInternal;
extern DeviceInfoDefaultTypeInternal _DeviceInfo_default_instance_;
class GetAliveTasksRequest;
struct GetAliveTasksRequestDefaultTypeInternal;
extern GetAliveTasksRequestDefaultTypeInternal _GetAliveTasksRequest_default_instance_;
class GetAliveTasksResponse;
struct GetAliveTasksResponseDefaultTypeInternal;
extern GetAliveTasksResponseDefaultTypeInternal _GetAliveTasksResponse_default_instance_;
class GetKeyValueDirRequest;
struct GetKeyValueDirRequestDefaultTypeInternal;
extern GetKeyValueDirRequestDefaultTypeInternal _GetKeyValueDirRequest_default_instance_;
class GetKeyValueDirResponse;
struct GetKeyValueDirResponseDefaultTypeInternal;
extern GetKeyValueDirResponseDefaultTypeInternal _GetKeyValueDirResponse_default_instance_;
class GetKeyValueRequest;
struct GetKeyValueRequestDefaultTypeInternal;
extern GetKeyValueRequestDefaultTypeInternal _GetKeyValueRequest_default_instance_;
class GetKeyValueResponse;
struct GetKeyValueResponseDefaultTypeInternal;
extern GetKeyValueResponseDefaultTypeInternal _GetKeyValueResponse_default_instance_;
class GetTaskStateRequest;
struct GetTaskStateRequestDefaultTypeInternal;
extern GetTaskStateRequestDefaultTypeInternal _GetTaskStateRequest_default_instance_;
class GetTaskStateResponse;
struct GetTaskStateResponseDefaultTypeInternal;
extern GetTaskStateResponseDefaultTypeInternal _GetTaskStateResponse_default_instance_;
class HeartbeatRequest;
struct HeartbeatRequestDefaultTypeInternal;
extern HeartbeatRequestDefaultTypeInternal _HeartbeatRequest_default_instance_;
class HeartbeatResponse;
struct HeartbeatResponseDefaultTypeInternal;
extern HeartbeatResponseDefaultTypeInternal _HeartbeatResponse_default_instance_;
class InsertKeyValueRequest;
struct InsertKeyValueRequestDefaultTypeInternal;
extern InsertKeyValueRequestDefaultTypeInternal _InsertKeyValueRequest_default_instance_;
class InsertKeyValueResponse;
struct InsertKeyValueResponseDefaultTypeInternal;
extern InsertKeyValueResponseDefaultTypeInternal _InsertKeyValueResponse_default_instance_;
class KeyValueEntry;
struct KeyValueEntryDefaultTypeInternal;
extern KeyValueEntryDefaultTypeInternal _KeyValueEntry_default_instance_;
class PollForErrorRequest;
struct PollForErrorRequestDefaultTypeInternal;
extern PollForErrorRequestDefaultTypeInternal _PollForErrorRequest_default_instance_;
class PollForErrorResponse;
struct PollForErrorResponseDefaultTypeInternal;
extern PollForErrorResponseDefaultTypeInternal _PollForErrorResponse_default_instance_;
class RegisterTaskRequest;
struct RegisterTaskRequestDefaultTypeInternal;
extern RegisterTaskRequestDefaultTypeInternal _RegisterTaskRequest_default_instance_;
class RegisterTaskResponse;
struct RegisterTaskResponseDefaultTypeInternal;
extern RegisterTaskResponseDefaultTypeInternal _RegisterTaskResponse_default_instance_;
class ReportErrorToServiceRequest;
struct ReportErrorToServiceRequestDefaultTypeInternal;
extern ReportErrorToServiceRequestDefaultTypeInternal _ReportErrorToServiceRequest_default_instance_;
class ReportErrorToServiceResponse;
struct ReportErrorToServiceResponseDefaultTypeInternal;
extern ReportErrorToServiceResponseDefaultTypeInternal _ReportErrorToServiceResponse_default_instance_;
class ReportErrorToTaskRequest;
struct ReportErrorToTaskRequestDefaultTypeInternal;
extern ReportErrorToTaskRequestDefaultTypeInternal _ReportErrorToTaskRequest_default_instance_;
class ReportErrorToTaskResponse;
struct ReportErrorToTaskResponseDefaultTypeInternal;
extern ReportErrorToTaskResponseDefaultTypeInternal _ReportErrorToTaskResponse_default_instance_;
class ResetTaskRequest;
struct ResetTaskRequestDefaultTypeInternal;
extern ResetTaskRequestDefaultTypeInternal _ResetTaskRequest_default_instance_;
class ResetTaskResponse;
struct ResetTaskResponseDefaultTypeInternal;
extern ResetTaskResponseDefaultTypeInternal _ResetTaskResponse_default_instance_;
class ShutdownTaskRequest;
struct ShutdownTaskRequestDefaultTypeInternal;
extern ShutdownTaskRequestDefaultTypeInternal _ShutdownTaskRequest_default_instance_;
class ShutdownTaskResponse;
struct ShutdownTaskResponseDefaultTypeInternal;
extern ShutdownTaskResponseDefaultTypeInternal _ShutdownTaskResponse_default_instance_;
class TryGetKeyValueRequest;
struct TryGetKeyValueRequestDefaultTypeInternal;
extern TryGetKeyValueRequestDefaultTypeInternal _TryGetKeyValueRequest_default_instance_;
class TryGetKeyValueResponse;
struct TryGetKeyValueResponseDefaultTypeInternal;
extern TryGetKeyValueResponseDefaultTypeInternal _TryGetKeyValueResponse_default_instance_;
class WaitForAllTasksRequest;
struct WaitForAllTasksRequestDefaultTypeInternal;
extern WaitForAllTasksRequestDefaultTypeInternal _WaitForAllTasksRequest_default_instance_;
class WaitForAllTasksResponse;
struct WaitForAllTasksResponseDefaultTypeInternal;
extern WaitForAllTasksResponseDefaultTypeInternal _WaitForAllTasksResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BarrierError* Arena::CreateMaybeMessage<::tensorflow::BarrierError>(Arena*);
template<> ::tensorflow::BarrierRequest* Arena::CreateMaybeMessage<::tensorflow::BarrierRequest>(Arena*);
template<> ::tensorflow::BarrierResponse* Arena::CreateMaybeMessage<::tensorflow::BarrierResponse>(Arena*);
template<> ::tensorflow::CancelBarrierRequest* Arena::CreateMaybeMessage<::tensorflow::CancelBarrierRequest>(Arena*);
template<> ::tensorflow::CancelBarrierResponse* Arena::CreateMaybeMessage<::tensorflow::CancelBarrierResponse>(Arena*);
template<> ::tensorflow::CoordinatedTask* Arena::CreateMaybeMessage<::tensorflow::CoordinatedTask>(Arena*);
template<> ::tensorflow::CoordinatedTaskStateInfo* Arena::CreateMaybeMessage<::tensorflow::CoordinatedTaskStateInfo>(Arena*);
template<> ::tensorflow::CoordinationServiceError* Arena::CreateMaybeMessage<::tensorflow::CoordinationServiceError>(Arena*);
template<> ::tensorflow::DeleteKeyValueRequest* Arena::CreateMaybeMessage<::tensorflow::DeleteKeyValueRequest>(Arena*);
template<> ::tensorflow::DeleteKeyValueResponse* Arena::CreateMaybeMessage<::tensorflow::DeleteKeyValueResponse>(Arena*);
template<> ::tensorflow::DeviceInfo* Arena::CreateMaybeMessage<::tensorflow::DeviceInfo>(Arena*);
template<> ::tensorflow::GetAliveTasksRequest* Arena::CreateMaybeMessage<::tensorflow::GetAliveTasksRequest>(Arena*);
template<> ::tensorflow::GetAliveTasksResponse* Arena::CreateMaybeMessage<::tensorflow::GetAliveTasksResponse>(Arena*);
template<> ::tensorflow::GetKeyValueDirRequest* Arena::CreateMaybeMessage<::tensorflow::GetKeyValueDirRequest>(Arena*);
template<> ::tensorflow::GetKeyValueDirResponse* Arena::CreateMaybeMessage<::tensorflow::GetKeyValueDirResponse>(Arena*);
template<> ::tensorflow::GetKeyValueRequest* Arena::CreateMaybeMessage<::tensorflow::GetKeyValueRequest>(Arena*);
template<> ::tensorflow::GetKeyValueResponse* Arena::CreateMaybeMessage<::tensorflow::GetKeyValueResponse>(Arena*);
template<> ::tensorflow::GetTaskStateRequest* Arena::CreateMaybeMessage<::tensorflow::GetTaskStateRequest>(Arena*);
template<> ::tensorflow::GetTaskStateResponse* Arena::CreateMaybeMessage<::tensorflow::GetTaskStateResponse>(Arena*);
template<> ::tensorflow::HeartbeatRequest* Arena::CreateMaybeMessage<::tensorflow::HeartbeatRequest>(Arena*);
template<> ::tensorflow::HeartbeatResponse* Arena::CreateMaybeMessage<::tensorflow::HeartbeatResponse>(Arena*);
template<> ::tensorflow::InsertKeyValueRequest* Arena::CreateMaybeMessage<::tensorflow::InsertKeyValueRequest>(Arena*);
template<> ::tensorflow::InsertKeyValueResponse* Arena::CreateMaybeMessage<::tensorflow::InsertKeyValueResponse>(Arena*);
template<> ::tensorflow::KeyValueEntry* Arena::CreateMaybeMessage<::tensorflow::KeyValueEntry>(Arena*);
template<> ::tensorflow::PollForErrorRequest* Arena::CreateMaybeMessage<::tensorflow::PollForErrorRequest>(Arena*);
template<> ::tensorflow::PollForErrorResponse* Arena::CreateMaybeMessage<::tensorflow::PollForErrorResponse>(Arena*);
template<> ::tensorflow::RegisterTaskRequest* Arena::CreateMaybeMessage<::tensorflow::RegisterTaskRequest>(Arena*);
template<> ::tensorflow::RegisterTaskResponse* Arena::CreateMaybeMessage<::tensorflow::RegisterTaskResponse>(Arena*);
template<> ::tensorflow::ReportErrorToServiceRequest* Arena::CreateMaybeMessage<::tensorflow::ReportErrorToServiceRequest>(Arena*);
template<> ::tensorflow::ReportErrorToServiceResponse* Arena::CreateMaybeMessage<::tensorflow::ReportErrorToServiceResponse>(Arena*);
template<> ::tensorflow::ReportErrorToTaskRequest* Arena::CreateMaybeMessage<::tensorflow::ReportErrorToTaskRequest>(Arena*);
template<> ::tensorflow::ReportErrorToTaskResponse* Arena::CreateMaybeMessage<::tensorflow::ReportErrorToTaskResponse>(Arena*);
template<> ::tensorflow::ResetTaskRequest* Arena::CreateMaybeMessage<::tensorflow::ResetTaskRequest>(Arena*);
template<> ::tensorflow::ResetTaskResponse* Arena::CreateMaybeMessage<::tensorflow::ResetTaskResponse>(Arena*);
template<> ::tensorflow::ShutdownTaskRequest* Arena::CreateMaybeMessage<::tensorflow::ShutdownTaskRequest>(Arena*);
template<> ::tensorflow::ShutdownTaskResponse* Arena::CreateMaybeMessage<::tensorflow::ShutdownTaskResponse>(Arena*);
template<> ::tensorflow::TryGetKeyValueRequest* Arena::CreateMaybeMessage<::tensorflow::TryGetKeyValueRequest>(Arena*);
template<> ::tensorflow::TryGetKeyValueResponse* Arena::CreateMaybeMessage<::tensorflow::TryGetKeyValueResponse>(Arena*);
template<> ::tensorflow::WaitForAllTasksRequest* Arena::CreateMaybeMessage<::tensorflow::WaitForAllTasksRequest>(Arena*);
template<> ::tensorflow::WaitForAllTasksResponse* Arena::CreateMaybeMessage<::tensorflow::WaitForAllTasksResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum CoordinatedTaskState : int {
  TASKSTATE_UNSPECIFIED = 0,
  TASKSTATE_UNINITIALIZED = 1,
  TASKSTATE_DISCONNECTED = 2,
  TASKSTATE_CONNECTED = 3,
  TASKSTATE_ERROR = 4,
  CoordinatedTaskState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CoordinatedTaskState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CoordinatedTaskState_IsValid(int value);
constexpr CoordinatedTaskState CoordinatedTaskState_MIN = TASKSTATE_UNSPECIFIED;
constexpr CoordinatedTaskState CoordinatedTaskState_MAX = TASKSTATE_ERROR;
constexpr int CoordinatedTaskState_ARRAYSIZE = CoordinatedTaskState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CoordinatedTaskState_descriptor();
template<typename T>
inline const std::string& CoordinatedTaskState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CoordinatedTaskState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CoordinatedTaskState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CoordinatedTaskState_descriptor(), enum_t_value);
}
inline bool CoordinatedTaskState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CoordinatedTaskState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CoordinatedTaskState>(
    CoordinatedTaskState_descriptor(), name, value);
}
// ===================================================================

class CoordinatedTask final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CoordinatedTask) */ {
 public:
  inline CoordinatedTask() : CoordinatedTask(nullptr) {}
  ~CoordinatedTask() override;
  explicit PROTOBUF_CONSTEXPR CoordinatedTask(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CoordinatedTask(const CoordinatedTask& from);
  CoordinatedTask(CoordinatedTask&& from) noexcept
    : CoordinatedTask() {
    *this = ::std::move(from);
  }

  inline CoordinatedTask& operator=(const CoordinatedTask& from) {
    CopyFrom(from);
    return *this;
  }
  inline CoordinatedTask& operator=(CoordinatedTask&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CoordinatedTask& default_instance() {
    return *internal_default_instance();
  }
  static inline const CoordinatedTask* internal_default_instance() {
    return reinterpret_cast<const CoordinatedTask*>(
               &_CoordinatedTask_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CoordinatedTask& a, CoordinatedTask& b) {
    a.Swap(&b);
  }
  inline void Swap(CoordinatedTask* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CoordinatedTask* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CoordinatedTask* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CoordinatedTask>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CoordinatedTask& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CoordinatedTask& from) {
    CoordinatedTask::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CoordinatedTask* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CoordinatedTask";
  }
  protected:
  explicit CoordinatedTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobNameFieldNumber = 1,
    kTaskIdFieldNumber = 2,
    kRecoverableFieldNumber = 3,
  };
  // string job_name = 1;
  void clear_job_name();
  const std::string& job_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_job_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_job_name();
  PROTOBUF_NODISCARD std::string* release_job_name();
  void set_allocated_job_name(std::string* job_name);
  private:
  const std::string& _internal_job_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_job_name(const std::string& value);
  std::string* _internal_mutable_job_name();
  public:

  // int32 task_id = 2;
  void clear_task_id();
  int32_t task_id() const;
  void set_task_id(int32_t value);
  private:
  int32_t _internal_task_id() const;
  void _internal_set_task_id(int32_t value);
  public:

  // bool recoverable = 3;
  void clear_recoverable();
  bool recoverable() const;
  void set_recoverable(bool value);
  private:
  bool _internal_recoverable() const;
  void _internal_set_recoverable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CoordinatedTask)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr job_name_;
    int32_t task_id_;
    bool recoverable_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CoordinationServiceError final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CoordinationServiceError) */ {
 public:
  inline CoordinationServiceError() : CoordinationServiceError(nullptr) {}
  ~CoordinationServiceError() override;
  explicit PROTOBUF_CONSTEXPR CoordinationServiceError(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CoordinationServiceError(const CoordinationServiceError& from);
  CoordinationServiceError(CoordinationServiceError&& from) noexcept
    : CoordinationServiceError() {
    *this = ::std::move(from);
  }

  inline CoordinationServiceError& operator=(const CoordinationServiceError& from) {
    CopyFrom(from);
    return *this;
  }
  inline CoordinationServiceError& operator=(CoordinationServiceError&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CoordinationServiceError& default_instance() {
    return *internal_default_instance();
  }
  static inline const CoordinationServiceError* internal_default_instance() {
    return reinterpret_cast<const CoordinationServiceError*>(
               &_CoordinationServiceError_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CoordinationServiceError& a, CoordinationServiceError& b) {
    a.Swap(&b);
  }
  inline void Swap(CoordinationServiceError* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CoordinationServiceError* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CoordinationServiceError* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CoordinationServiceError>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CoordinationServiceError& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CoordinationServiceError& from) {
    CoordinationServiceError::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CoordinationServiceError* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CoordinationServiceError";
  }
  protected:
  explicit CoordinationServiceError(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 4,
    kIsReportedErrorFieldNumber = 3,
  };
  // .tensorflow.CoordinatedTask source_task = 4;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // bool is_reported_error = 3;
  void clear_is_reported_error();
  bool is_reported_error() const;
  void set_is_reported_error(bool value);
  private:
  bool _internal_is_reported_error() const;
  void _internal_set_is_reported_error(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CoordinationServiceError)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    bool is_reported_error_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class BarrierError final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BarrierError) */ {
 public:
  inline BarrierError() : BarrierError(nullptr) {}
  ~BarrierError() override;
  explicit PROTOBUF_CONSTEXPR BarrierError(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BarrierError(const BarrierError& from);
  BarrierError(BarrierError&& from) noexcept
    : BarrierError() {
    *this = ::std::move(from);
  }

  inline BarrierError& operator=(const BarrierError& from) {
    CopyFrom(from);
    return *this;
  }
  inline BarrierError& operator=(BarrierError&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BarrierError& default_instance() {
    return *internal_default_instance();
  }
  static inline const BarrierError* internal_default_instance() {
    return reinterpret_cast<const BarrierError*>(
               &_BarrierError_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BarrierError& a, BarrierError& b) {
    a.Swap(&b);
  }
  inline void Swap(BarrierError* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BarrierError* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BarrierError* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BarrierError>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BarrierError& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BarrierError& from) {
    BarrierError::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BarrierError* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BarrierError";
  }
  protected:
  explicit BarrierError(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBarrierIdFieldNumber = 1,
    kCounterFieldNumber = 2,
  };
  // string barrier_id = 1;
  void clear_barrier_id();
  const std::string& barrier_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_barrier_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_barrier_id();
  PROTOBUF_NODISCARD std::string* release_barrier_id();
  void set_allocated_barrier_id(std::string* barrier_id);
  private:
  const std::string& _internal_barrier_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_barrier_id(const std::string& value);
  std::string* _internal_mutable_barrier_id();
  public:

  // int64 counter = 2;
  void clear_counter();
  int64_t counter() const;
  void set_counter(int64_t value);
  private:
  int64_t _internal_counter() const;
  void _internal_set_counter(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BarrierError)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr barrier_id_;
    int64_t counter_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CoordinatedTaskStateInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CoordinatedTaskStateInfo) */ {
 public:
  inline CoordinatedTaskStateInfo() : CoordinatedTaskStateInfo(nullptr) {}
  ~CoordinatedTaskStateInfo() override;
  explicit PROTOBUF_CONSTEXPR CoordinatedTaskStateInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CoordinatedTaskStateInfo(const CoordinatedTaskStateInfo& from);
  CoordinatedTaskStateInfo(CoordinatedTaskStateInfo&& from) noexcept
    : CoordinatedTaskStateInfo() {
    *this = ::std::move(from);
  }

  inline CoordinatedTaskStateInfo& operator=(const CoordinatedTaskStateInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CoordinatedTaskStateInfo& operator=(CoordinatedTaskStateInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CoordinatedTaskStateInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CoordinatedTaskStateInfo* internal_default_instance() {
    return reinterpret_cast<const CoordinatedTaskStateInfo*>(
               &_CoordinatedTaskStateInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CoordinatedTaskStateInfo& a, CoordinatedTaskStateInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CoordinatedTaskStateInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CoordinatedTaskStateInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CoordinatedTaskStateInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CoordinatedTaskStateInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CoordinatedTaskStateInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CoordinatedTaskStateInfo& from) {
    CoordinatedTaskStateInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CoordinatedTaskStateInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CoordinatedTaskStateInfo";
  }
  protected:
  explicit CoordinatedTaskStateInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 4,
    kTaskFieldNumber = 1,
    kErrorPayloadFieldNumber = 5,
    kStateFieldNumber = 2,
    kErrorCodeFieldNumber = 3,
  };
  // string error_message = 4;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // .tensorflow.CoordinatedTask task = 1;
  bool has_task() const;
  private:
  bool _internal_has_task() const;
  public:
  void clear_task();
  const ::tensorflow::CoordinatedTask& task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_task();
  ::tensorflow::CoordinatedTask* mutable_task();
  void set_allocated_task(::tensorflow::CoordinatedTask* task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_task();
  public:
  void unsafe_arena_set_allocated_task(
      ::tensorflow::CoordinatedTask* task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_task();

  // .tensorflow.CoordinationServiceError error_payload = 5;
  bool has_error_payload() const;
  private:
  bool _internal_has_error_payload() const;
  public:
  void clear_error_payload();
  const ::tensorflow::CoordinationServiceError& error_payload() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinationServiceError* release_error_payload();
  ::tensorflow::CoordinationServiceError* mutable_error_payload();
  void set_allocated_error_payload(::tensorflow::CoordinationServiceError* error_payload);
  private:
  const ::tensorflow::CoordinationServiceError& _internal_error_payload() const;
  ::tensorflow::CoordinationServiceError* _internal_mutable_error_payload();
  public:
  void unsafe_arena_set_allocated_error_payload(
      ::tensorflow::CoordinationServiceError* error_payload);
  ::tensorflow::CoordinationServiceError* unsafe_arena_release_error_payload();

  // .tensorflow.CoordinatedTaskState state = 2;
  void clear_state();
  ::tensorflow::CoordinatedTaskState state() const;
  void set_state(::tensorflow::CoordinatedTaskState value);
  private:
  ::tensorflow::CoordinatedTaskState _internal_state() const;
  void _internal_set_state(::tensorflow::CoordinatedTaskState value);
  public:

  // int32 error_code = 3;
  void clear_error_code();
  int32_t error_code() const;
  void set_error_code(int32_t value);
  private:
  int32_t _internal_error_code() const;
  void _internal_set_error_code(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CoordinatedTaskStateInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    ::tensorflow::CoordinatedTask* task_;
    ::tensorflow::CoordinationServiceError* error_payload_;
    int state_;
    int32_t error_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class DeviceInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceInfo) */ {
 public:
  inline DeviceInfo() : DeviceInfo(nullptr) {}
  ~DeviceInfo() override;
  explicit PROTOBUF_CONSTEXPR DeviceInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceInfo(const DeviceInfo& from);
  DeviceInfo(DeviceInfo&& from) noexcept
    : DeviceInfo() {
    *this = ::std::move(from);
  }

  inline DeviceInfo& operator=(const DeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceInfo& operator=(DeviceInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceInfo* internal_default_instance() {
    return reinterpret_cast<const DeviceInfo*>(
               &_DeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DeviceInfo& a, DeviceInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceInfo& from) {
    DeviceInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceInfo";
  }
  protected:
  explicit DeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
  };
  // repeated .google.protobuf.Any device = 1;
  int device_size() const;
  private:
  int _internal_device_size() const;
  public:
  void clear_device();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
      mutable_device();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_device(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_add_device();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Any& device(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* add_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
      device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any > device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterTaskRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterTaskRequest) */ {
 public:
  inline RegisterTaskRequest() : RegisterTaskRequest(nullptr) {}
  ~RegisterTaskRequest() override;
  explicit PROTOBUF_CONSTEXPR RegisterTaskRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterTaskRequest(const RegisterTaskRequest& from);
  RegisterTaskRequest(RegisterTaskRequest&& from) noexcept
    : RegisterTaskRequest() {
    *this = ::std::move(from);
  }

  inline RegisterTaskRequest& operator=(const RegisterTaskRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterTaskRequest& operator=(RegisterTaskRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterTaskRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterTaskRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterTaskRequest*>(
               &_RegisterTaskRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RegisterTaskRequest& a, RegisterTaskRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterTaskRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterTaskRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterTaskRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterTaskRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterTaskRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisterTaskRequest& from) {
    RegisterTaskRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterTaskRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterTaskRequest";
  }
  protected:
  explicit RegisterTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 5,
    kIncarnationFieldNumber = 3,
  };
  // .tensorflow.CoordinatedTask source_task = 5;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // fixed64 incarnation = 3;
  void clear_incarnation();
  uint64_t incarnation() const;
  void set_incarnation(uint64_t value);
  private:
  uint64_t _internal_incarnation() const;
  void _internal_set_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterTaskRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    uint64_t incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterTaskResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterTaskResponse) */ {
 public:
  inline RegisterTaskResponse() : RegisterTaskResponse(nullptr) {}
  ~RegisterTaskResponse() override;
  explicit PROTOBUF_CONSTEXPR RegisterTaskResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterTaskResponse(const RegisterTaskResponse& from);
  RegisterTaskResponse(RegisterTaskResponse&& from) noexcept
    : RegisterTaskResponse() {
    *this = ::std::move(from);
  }

  inline RegisterTaskResponse& operator=(const RegisterTaskResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterTaskResponse& operator=(RegisterTaskResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterTaskResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterTaskResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterTaskResponse*>(
               &_RegisterTaskResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RegisterTaskResponse& a, RegisterTaskResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterTaskResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterTaskResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterTaskResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterTaskResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterTaskResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisterTaskResponse& from) {
    RegisterTaskResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterTaskResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterTaskResponse";
  }
  protected:
  explicit RegisterTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLeaderIncarnationFieldNumber = 1,
  };
  // fixed64 leader_incarnation = 1;
  void clear_leader_incarnation();
  uint64_t leader_incarnation() const;
  void set_leader_incarnation(uint64_t value);
  private:
  uint64_t _internal_leader_incarnation() const;
  void _internal_set_leader_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterTaskResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t leader_incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class HeartbeatRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.HeartbeatRequest) */ {
 public:
  inline HeartbeatRequest() : HeartbeatRequest(nullptr) {}
  ~HeartbeatRequest() override;
  explicit PROTOBUF_CONSTEXPR HeartbeatRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HeartbeatRequest(const HeartbeatRequest& from);
  HeartbeatRequest(HeartbeatRequest&& from) noexcept
    : HeartbeatRequest() {
    *this = ::std::move(from);
  }

  inline HeartbeatRequest& operator=(const HeartbeatRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeartbeatRequest& operator=(HeartbeatRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HeartbeatRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const HeartbeatRequest* internal_default_instance() {
    return reinterpret_cast<const HeartbeatRequest*>(
               &_HeartbeatRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(HeartbeatRequest& a, HeartbeatRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(HeartbeatRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeartbeatRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HeartbeatRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HeartbeatRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HeartbeatRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HeartbeatRequest& from) {
    HeartbeatRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeartbeatRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.HeartbeatRequest";
  }
  protected:
  explicit HeartbeatRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 4,
    kIncarnationFieldNumber = 3,
  };
  // .tensorflow.CoordinatedTask source_task = 4;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // fixed64 incarnation = 3;
  void clear_incarnation();
  uint64_t incarnation() const;
  void set_incarnation(uint64_t value);
  private:
  uint64_t _internal_incarnation() const;
  void _internal_set_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.HeartbeatRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    uint64_t incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class HeartbeatResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.HeartbeatResponse) */ {
 public:
  inline HeartbeatResponse() : HeartbeatResponse(nullptr) {}
  ~HeartbeatResponse() override;
  explicit PROTOBUF_CONSTEXPR HeartbeatResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HeartbeatResponse(const HeartbeatResponse& from);
  HeartbeatResponse(HeartbeatResponse&& from) noexcept
    : HeartbeatResponse() {
    *this = ::std::move(from);
  }

  inline HeartbeatResponse& operator=(const HeartbeatResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeartbeatResponse& operator=(HeartbeatResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HeartbeatResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const HeartbeatResponse* internal_default_instance() {
    return reinterpret_cast<const HeartbeatResponse*>(
               &_HeartbeatResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(HeartbeatResponse& a, HeartbeatResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(HeartbeatResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeartbeatResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HeartbeatResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HeartbeatResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HeartbeatResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HeartbeatResponse& from) {
    HeartbeatResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HeartbeatResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.HeartbeatResponse";
  }
  protected:
  explicit HeartbeatResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLeaderIncarnationFieldNumber = 1,
  };
  // fixed64 leader_incarnation = 1;
  void clear_leader_incarnation();
  uint64_t leader_incarnation() const;
  void set_leader_incarnation(uint64_t value);
  private:
  uint64_t _internal_leader_incarnation() const;
  void _internal_set_leader_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.HeartbeatResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t leader_incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PollForErrorRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PollForErrorRequest) */ {
 public:
  inline PollForErrorRequest() : PollForErrorRequest(nullptr) {}
  ~PollForErrorRequest() override;
  explicit PROTOBUF_CONSTEXPR PollForErrorRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PollForErrorRequest(const PollForErrorRequest& from);
  PollForErrorRequest(PollForErrorRequest&& from) noexcept
    : PollForErrorRequest() {
    *this = ::std::move(from);
  }

  inline PollForErrorRequest& operator=(const PollForErrorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PollForErrorRequest& operator=(PollForErrorRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PollForErrorRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PollForErrorRequest* internal_default_instance() {
    return reinterpret_cast<const PollForErrorRequest*>(
               &_PollForErrorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(PollForErrorRequest& a, PollForErrorRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PollForErrorRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PollForErrorRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PollForErrorRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PollForErrorRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PollForErrorRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PollForErrorRequest& from) {
    PollForErrorRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PollForErrorRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PollForErrorRequest";
  }
  protected:
  explicit PollForErrorRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 1,
  };
  // .tensorflow.CoordinatedTask source_task = 1;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // @@protoc_insertion_point(class_scope:tensorflow.PollForErrorRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PollForErrorResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.PollForErrorResponse) */ {
 public:
  inline PollForErrorResponse() : PollForErrorResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR PollForErrorResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PollForErrorResponse(const PollForErrorResponse& from);
  PollForErrorResponse(PollForErrorResponse&& from) noexcept
    : PollForErrorResponse() {
    *this = ::std::move(from);
  }

  inline PollForErrorResponse& operator=(const PollForErrorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PollForErrorResponse& operator=(PollForErrorResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PollForErrorResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PollForErrorResponse* internal_default_instance() {
    return reinterpret_cast<const PollForErrorResponse*>(
               &_PollForErrorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(PollForErrorResponse& a, PollForErrorResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PollForErrorResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PollForErrorResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PollForErrorResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PollForErrorResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const PollForErrorResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const PollForErrorResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PollForErrorResponse";
  }
  protected:
  explicit PollForErrorResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.PollForErrorResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitForAllTasksRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WaitForAllTasksRequest) */ {
 public:
  inline WaitForAllTasksRequest() : WaitForAllTasksRequest(nullptr) {}
  ~WaitForAllTasksRequest() override;
  explicit PROTOBUF_CONSTEXPR WaitForAllTasksRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WaitForAllTasksRequest(const WaitForAllTasksRequest& from);
  WaitForAllTasksRequest(WaitForAllTasksRequest&& from) noexcept
    : WaitForAllTasksRequest() {
    *this = ::std::move(from);
  }

  inline WaitForAllTasksRequest& operator=(const WaitForAllTasksRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitForAllTasksRequest& operator=(WaitForAllTasksRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WaitForAllTasksRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const WaitForAllTasksRequest* internal_default_instance() {
    return reinterpret_cast<const WaitForAllTasksRequest*>(
               &_WaitForAllTasksRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(WaitForAllTasksRequest& a, WaitForAllTasksRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitForAllTasksRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WaitForAllTasksRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WaitForAllTasksRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WaitForAllTasksRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WaitForAllTasksRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WaitForAllTasksRequest& from) {
    WaitForAllTasksRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitForAllTasksRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WaitForAllTasksRequest";
  }
  protected:
  explicit WaitForAllTasksRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 5,
    kDeviceInfoFieldNumber = 6,
  };
  // .tensorflow.CoordinatedTask source_task = 5;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // .tensorflow.DeviceInfo device_info = 6;
  bool has_device_info() const;
  private:
  bool _internal_has_device_info() const;
  public:
  void clear_device_info();
  const ::tensorflow::DeviceInfo& device_info() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceInfo* release_device_info();
  ::tensorflow::DeviceInfo* mutable_device_info();
  void set_allocated_device_info(::tensorflow::DeviceInfo* device_info);
  private:
  const ::tensorflow::DeviceInfo& _internal_device_info() const;
  ::tensorflow::DeviceInfo* _internal_mutable_device_info();
  public:
  void unsafe_arena_set_allocated_device_info(
      ::tensorflow::DeviceInfo* device_info);
  ::tensorflow::DeviceInfo* unsafe_arena_release_device_info();

  // @@protoc_insertion_point(class_scope:tensorflow.WaitForAllTasksRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    ::tensorflow::DeviceInfo* device_info_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitForAllTasksResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WaitForAllTasksResponse) */ {
 public:
  inline WaitForAllTasksResponse() : WaitForAllTasksResponse(nullptr) {}
  ~WaitForAllTasksResponse() override;
  explicit PROTOBUF_CONSTEXPR WaitForAllTasksResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WaitForAllTasksResponse(const WaitForAllTasksResponse& from);
  WaitForAllTasksResponse(WaitForAllTasksResponse&& from) noexcept
    : WaitForAllTasksResponse() {
    *this = ::std::move(from);
  }

  inline WaitForAllTasksResponse& operator=(const WaitForAllTasksResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitForAllTasksResponse& operator=(WaitForAllTasksResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WaitForAllTasksResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const WaitForAllTasksResponse* internal_default_instance() {
    return reinterpret_cast<const WaitForAllTasksResponse*>(
               &_WaitForAllTasksResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(WaitForAllTasksResponse& a, WaitForAllTasksResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitForAllTasksResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WaitForAllTasksResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WaitForAllTasksResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WaitForAllTasksResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WaitForAllTasksResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WaitForAllTasksResponse& from) {
    WaitForAllTasksResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitForAllTasksResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WaitForAllTasksResponse";
  }
  protected:
  explicit WaitForAllTasksResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceInfoFieldNumber = 4,
    kLeaderIncarnationFieldNumber = 1,
  };
  // .tensorflow.DeviceInfo device_info = 4;
  bool has_device_info() const;
  private:
  bool _internal_has_device_info() const;
  public:
  void clear_device_info();
  const ::tensorflow::DeviceInfo& device_info() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceInfo* release_device_info();
  ::tensorflow::DeviceInfo* mutable_device_info();
  void set_allocated_device_info(::tensorflow::DeviceInfo* device_info);
  private:
  const ::tensorflow::DeviceInfo& _internal_device_info() const;
  ::tensorflow::DeviceInfo* _internal_mutable_device_info();
  public:
  void unsafe_arena_set_allocated_device_info(
      ::tensorflow::DeviceInfo* device_info);
  ::tensorflow::DeviceInfo* unsafe_arena_release_device_info();

  // fixed64 leader_incarnation = 1;
  void clear_leader_incarnation();
  uint64_t leader_incarnation() const;
  void set_leader_incarnation(uint64_t value);
  private:
  uint64_t _internal_leader_incarnation() const;
  void _internal_set_leader_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.WaitForAllTasksResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::DeviceInfo* device_info_;
    uint64_t leader_incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ShutdownTaskRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ShutdownTaskRequest) */ {
 public:
  inline ShutdownTaskRequest() : ShutdownTaskRequest(nullptr) {}
  ~ShutdownTaskRequest() override;
  explicit PROTOBUF_CONSTEXPR ShutdownTaskRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ShutdownTaskRequest(const ShutdownTaskRequest& from);
  ShutdownTaskRequest(ShutdownTaskRequest&& from) noexcept
    : ShutdownTaskRequest() {
    *this = ::std::move(from);
  }

  inline ShutdownTaskRequest& operator=(const ShutdownTaskRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShutdownTaskRequest& operator=(ShutdownTaskRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ShutdownTaskRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ShutdownTaskRequest* internal_default_instance() {
    return reinterpret_cast<const ShutdownTaskRequest*>(
               &_ShutdownTaskRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ShutdownTaskRequest& a, ShutdownTaskRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ShutdownTaskRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ShutdownTaskRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ShutdownTaskRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ShutdownTaskRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ShutdownTaskRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ShutdownTaskRequest& from) {
    ShutdownTaskRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ShutdownTaskRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ShutdownTaskRequest";
  }
  protected:
  explicit ShutdownTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 1,
  };
  // .tensorflow.CoordinatedTask source_task = 1;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // @@protoc_insertion_point(class_scope:tensorflow.ShutdownTaskRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ShutdownTaskResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ShutdownTaskResponse) */ {
 public:
  inline ShutdownTaskResponse() : ShutdownTaskResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ShutdownTaskResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ShutdownTaskResponse(const ShutdownTaskResponse& from);
  ShutdownTaskResponse(ShutdownTaskResponse&& from) noexcept
    : ShutdownTaskResponse() {
    *this = ::std::move(from);
  }

  inline ShutdownTaskResponse& operator=(const ShutdownTaskResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ShutdownTaskResponse& operator=(ShutdownTaskResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ShutdownTaskResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ShutdownTaskResponse* internal_default_instance() {
    return reinterpret_cast<const ShutdownTaskResponse*>(
               &_ShutdownTaskResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(ShutdownTaskResponse& a, ShutdownTaskResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ShutdownTaskResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ShutdownTaskResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ShutdownTaskResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ShutdownTaskResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ShutdownTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ShutdownTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ShutdownTaskResponse";
  }
  protected:
  explicit ShutdownTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ShutdownTaskResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ResetTaskRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetTaskRequest) */ {
 public:
  inline ResetTaskRequest() : ResetTaskRequest(nullptr) {}
  ~ResetTaskRequest() override;
  explicit PROTOBUF_CONSTEXPR ResetTaskRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResetTaskRequest(const ResetTaskRequest& from);
  ResetTaskRequest(ResetTaskRequest&& from) noexcept
    : ResetTaskRequest() {
    *this = ::std::move(from);
  }

  inline ResetTaskRequest& operator=(const ResetTaskRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetTaskRequest& operator=(ResetTaskRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResetTaskRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResetTaskRequest* internal_default_instance() {
    return reinterpret_cast<const ResetTaskRequest*>(
               &_ResetTaskRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ResetTaskRequest& a, ResetTaskRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetTaskRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetTaskRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResetTaskRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResetTaskRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResetTaskRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ResetTaskRequest& from) {
    ResetTaskRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetTaskRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetTaskRequest";
  }
  protected:
  explicit ResetTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 1,
  };
  // .tensorflow.CoordinatedTask source_task = 1;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // @@protoc_insertion_point(class_scope:tensorflow.ResetTaskRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::CoordinatedTask* source_task_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ResetTaskResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ResetTaskResponse) */ {
 public:
  inline ResetTaskResponse() : ResetTaskResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ResetTaskResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResetTaskResponse(const ResetTaskResponse& from);
  ResetTaskResponse(ResetTaskResponse&& from) noexcept
    : ResetTaskResponse() {
    *this = ::std::move(from);
  }

  inline ResetTaskResponse& operator=(const ResetTaskResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetTaskResponse& operator=(ResetTaskResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResetTaskResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResetTaskResponse* internal_default_instance() {
    return reinterpret_cast<const ResetTaskResponse*>(
               &_ResetTaskResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(ResetTaskResponse& a, ResetTaskResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetTaskResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetTaskResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResetTaskResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResetTaskResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ResetTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ResetTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetTaskResponse";
  }
  protected:
  explicit ResetTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ResetTaskResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReportErrorToTaskRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReportErrorToTaskRequest) */ {
 public:
  inline ReportErrorToTaskRequest() : ReportErrorToTaskRequest(nullptr) {}
  ~ReportErrorToTaskRequest() override;
  explicit PROTOBUF_CONSTEXPR ReportErrorToTaskRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReportErrorToTaskRequest(const ReportErrorToTaskRequest& from);
  ReportErrorToTaskRequest(ReportErrorToTaskRequest&& from) noexcept
    : ReportErrorToTaskRequest() {
    *this = ::std::move(from);
  }

  inline ReportErrorToTaskRequest& operator=(const ReportErrorToTaskRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReportErrorToTaskRequest& operator=(ReportErrorToTaskRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReportErrorToTaskRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReportErrorToTaskRequest* internal_default_instance() {
    return reinterpret_cast<const ReportErrorToTaskRequest*>(
               &_ReportErrorToTaskRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(ReportErrorToTaskRequest& a, ReportErrorToTaskRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReportErrorToTaskRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReportErrorToTaskRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReportErrorToTaskRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReportErrorToTaskRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReportErrorToTaskRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ReportErrorToTaskRequest& from) {
    ReportErrorToTaskRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReportErrorToTaskRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReportErrorToTaskRequest";
  }
  protected:
  explicit ReportErrorToTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 2,
    kErrorPayloadFieldNumber = 5,
    kErrorCodeFieldNumber = 1,
  };
  // string error_message = 2;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // .tensorflow.CoordinationServiceError error_payload = 5;
  bool has_error_payload() const;
  private:
  bool _internal_has_error_payload() const;
  public:
  void clear_error_payload();
  const ::tensorflow::CoordinationServiceError& error_payload() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinationServiceError* release_error_payload();
  ::tensorflow::CoordinationServiceError* mutable_error_payload();
  void set_allocated_error_payload(::tensorflow::CoordinationServiceError* error_payload);
  private:
  const ::tensorflow::CoordinationServiceError& _internal_error_payload() const;
  ::tensorflow::CoordinationServiceError* _internal_mutable_error_payload();
  public:
  void unsafe_arena_set_allocated_error_payload(
      ::tensorflow::CoordinationServiceError* error_payload);
  ::tensorflow::CoordinationServiceError* unsafe_arena_release_error_payload();

  // int32 error_code = 1;
  void clear_error_code();
  int32_t error_code() const;
  void set_error_code(int32_t value);
  private:
  int32_t _internal_error_code() const;
  void _internal_set_error_code(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ReportErrorToTaskRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    ::tensorflow::CoordinationServiceError* error_payload_;
    int32_t error_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReportErrorToTaskResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ReportErrorToTaskResponse) */ {
 public:
  inline ReportErrorToTaskResponse() : ReportErrorToTaskResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ReportErrorToTaskResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReportErrorToTaskResponse(const ReportErrorToTaskResponse& from);
  ReportErrorToTaskResponse(ReportErrorToTaskResponse&& from) noexcept
    : ReportErrorToTaskResponse() {
    *this = ::std::move(from);
  }

  inline ReportErrorToTaskResponse& operator=(const ReportErrorToTaskResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReportErrorToTaskResponse& operator=(ReportErrorToTaskResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReportErrorToTaskResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReportErrorToTaskResponse* internal_default_instance() {
    return reinterpret_cast<const ReportErrorToTaskResponse*>(
               &_ReportErrorToTaskResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ReportErrorToTaskResponse& a, ReportErrorToTaskResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ReportErrorToTaskResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReportErrorToTaskResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReportErrorToTaskResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReportErrorToTaskResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ReportErrorToTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ReportErrorToTaskResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReportErrorToTaskResponse";
  }
  protected:
  explicit ReportErrorToTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ReportErrorToTaskResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReportErrorToServiceRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReportErrorToServiceRequest) */ {
 public:
  inline ReportErrorToServiceRequest() : ReportErrorToServiceRequest(nullptr) {}
  ~ReportErrorToServiceRequest() override;
  explicit PROTOBUF_CONSTEXPR ReportErrorToServiceRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReportErrorToServiceRequest(const ReportErrorToServiceRequest& from);
  ReportErrorToServiceRequest(ReportErrorToServiceRequest&& from) noexcept
    : ReportErrorToServiceRequest() {
    *this = ::std::move(from);
  }

  inline ReportErrorToServiceRequest& operator=(const ReportErrorToServiceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReportErrorToServiceRequest& operator=(ReportErrorToServiceRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReportErrorToServiceRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReportErrorToServiceRequest* internal_default_instance() {
    return reinterpret_cast<const ReportErrorToServiceRequest*>(
               &_ReportErrorToServiceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ReportErrorToServiceRequest& a, ReportErrorToServiceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReportErrorToServiceRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReportErrorToServiceRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReportErrorToServiceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReportErrorToServiceRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReportErrorToServiceRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ReportErrorToServiceRequest& from) {
    ReportErrorToServiceRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReportErrorToServiceRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReportErrorToServiceRequest";
  }
  protected:
  explicit ReportErrorToServiceRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 2,
    kErrorOriginFieldNumber = 5,
    kErrorCodeFieldNumber = 1,
  };
  // string error_message = 2;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // .tensorflow.CoordinatedTask error_origin = 5;
  bool has_error_origin() const;
  private:
  bool _internal_has_error_origin() const;
  public:
  void clear_error_origin();
  const ::tensorflow::CoordinatedTask& error_origin() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_error_origin();
  ::tensorflow::CoordinatedTask* mutable_error_origin();
  void set_allocated_error_origin(::tensorflow::CoordinatedTask* error_origin);
  private:
  const ::tensorflow::CoordinatedTask& _internal_error_origin() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_error_origin();
  public:
  void unsafe_arena_set_allocated_error_origin(
      ::tensorflow::CoordinatedTask* error_origin);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_error_origin();

  // int32 error_code = 1;
  void clear_error_code();
  int32_t error_code() const;
  void set_error_code(int32_t value);
  private:
  int32_t _internal_error_code() const;
  void _internal_set_error_code(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ReportErrorToServiceRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    ::tensorflow::CoordinatedTask* error_origin_;
    int32_t error_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReportErrorToServiceResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ReportErrorToServiceResponse) */ {
 public:
  inline ReportErrorToServiceResponse() : ReportErrorToServiceResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ReportErrorToServiceResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReportErrorToServiceResponse(const ReportErrorToServiceResponse& from);
  ReportErrorToServiceResponse(ReportErrorToServiceResponse&& from) noexcept
    : ReportErrorToServiceResponse() {
    *this = ::std::move(from);
  }

  inline ReportErrorToServiceResponse& operator=(const ReportErrorToServiceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReportErrorToServiceResponse& operator=(ReportErrorToServiceResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReportErrorToServiceResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReportErrorToServiceResponse* internal_default_instance() {
    return reinterpret_cast<const ReportErrorToServiceResponse*>(
               &_ReportErrorToServiceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(ReportErrorToServiceResponse& a, ReportErrorToServiceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ReportErrorToServiceResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReportErrorToServiceResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReportErrorToServiceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReportErrorToServiceResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ReportErrorToServiceResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ReportErrorToServiceResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReportErrorToServiceResponse";
  }
  protected:
  explicit ReportErrorToServiceResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ReportErrorToServiceResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetTaskStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetTaskStateRequest) */ {
 public:
  inline GetTaskStateRequest() : GetTaskStateRequest(nullptr) {}
  ~GetTaskStateRequest() override;
  explicit PROTOBUF_CONSTEXPR GetTaskStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetTaskStateRequest(const GetTaskStateRequest& from);
  GetTaskStateRequest(GetTaskStateRequest&& from) noexcept
    : GetTaskStateRequest() {
    *this = ::std::move(from);
  }

  inline GetTaskStateRequest& operator=(const GetTaskStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetTaskStateRequest& operator=(GetTaskStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetTaskStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetTaskStateRequest* internal_default_instance() {
    return reinterpret_cast<const GetTaskStateRequest*>(
               &_GetTaskStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(GetTaskStateRequest& a, GetTaskStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetTaskStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetTaskStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetTaskStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetTaskStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetTaskStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetTaskStateRequest& from) {
    GetTaskStateRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetTaskStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetTaskStateRequest";
  }
  protected:
  explicit GetTaskStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceTaskFieldNumber = 1,
  };
  // repeated .tensorflow.CoordinatedTask source_task = 1;
  int source_task_size() const;
  private:
  int _internal_source_task_size() const;
  public:
  void clear_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
      mutable_source_task();
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task(int index) const;
  ::tensorflow::CoordinatedTask* _internal_add_source_task();
  public:
  const ::tensorflow::CoordinatedTask& source_task(int index) const;
  ::tensorflow::CoordinatedTask* add_source_task();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
      source_task() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetTaskStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask > source_task_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetTaskStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetTaskStateResponse) */ {
 public:
  inline GetTaskStateResponse() : GetTaskStateResponse(nullptr) {}
  ~GetTaskStateResponse() override;
  explicit PROTOBUF_CONSTEXPR GetTaskStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetTaskStateResponse(const GetTaskStateResponse& from);
  GetTaskStateResponse(GetTaskStateResponse&& from) noexcept
    : GetTaskStateResponse() {
    *this = ::std::move(from);
  }

  inline GetTaskStateResponse& operator=(const GetTaskStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetTaskStateResponse& operator=(GetTaskStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetTaskStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetTaskStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetTaskStateResponse*>(
               &_GetTaskStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(GetTaskStateResponse& a, GetTaskStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetTaskStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetTaskStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetTaskStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetTaskStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetTaskStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetTaskStateResponse& from) {
    GetTaskStateResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetTaskStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetTaskStateResponse";
  }
  protected:
  explicit GetTaskStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskStateFieldNumber = 1,
  };
  // repeated .tensorflow.CoordinatedTaskStateInfo task_state = 1;
  int task_state_size() const;
  private:
  int _internal_task_state_size() const;
  public:
  void clear_task_state();
  ::tensorflow::CoordinatedTaskStateInfo* mutable_task_state(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTaskStateInfo >*
      mutable_task_state();
  private:
  const ::tensorflow::CoordinatedTaskStateInfo& _internal_task_state(int index) const;
  ::tensorflow::CoordinatedTaskStateInfo* _internal_add_task_state();
  public:
  const ::tensorflow::CoordinatedTaskStateInfo& task_state(int index) const;
  ::tensorflow::CoordinatedTaskStateInfo* add_task_state();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTaskStateInfo >&
      task_state() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetTaskStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTaskStateInfo > task_state_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class KeyValueEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KeyValueEntry) */ {
 public:
  inline KeyValueEntry() : KeyValueEntry(nullptr) {}
  ~KeyValueEntry() override;
  explicit PROTOBUF_CONSTEXPR KeyValueEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KeyValueEntry(const KeyValueEntry& from);
  KeyValueEntry(KeyValueEntry&& from) noexcept
    : KeyValueEntry() {
    *this = ::std::move(from);
  }

  inline KeyValueEntry& operator=(const KeyValueEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeyValueEntry& operator=(KeyValueEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeyValueEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeyValueEntry* internal_default_instance() {
    return reinterpret_cast<const KeyValueEntry*>(
               &_KeyValueEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(KeyValueEntry& a, KeyValueEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(KeyValueEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeyValueEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeyValueEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KeyValueEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KeyValueEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KeyValueEntry& from) {
    KeyValueEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeyValueEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KeyValueEntry";
  }
  protected:
  explicit KeyValueEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // bytes value = 2;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.KeyValueEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class InsertKeyValueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.InsertKeyValueRequest) */ {
 public:
  inline InsertKeyValueRequest() : InsertKeyValueRequest(nullptr) {}
  ~InsertKeyValueRequest() override;
  explicit PROTOBUF_CONSTEXPR InsertKeyValueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InsertKeyValueRequest(const InsertKeyValueRequest& from);
  InsertKeyValueRequest(InsertKeyValueRequest&& from) noexcept
    : InsertKeyValueRequest() {
    *this = ::std::move(from);
  }

  inline InsertKeyValueRequest& operator=(const InsertKeyValueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline InsertKeyValueRequest& operator=(InsertKeyValueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InsertKeyValueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const InsertKeyValueRequest* internal_default_instance() {
    return reinterpret_cast<const InsertKeyValueRequest*>(
               &_InsertKeyValueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(InsertKeyValueRequest& a, InsertKeyValueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(InsertKeyValueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InsertKeyValueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InsertKeyValueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InsertKeyValueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InsertKeyValueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InsertKeyValueRequest& from) {
    InsertKeyValueRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InsertKeyValueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.InsertKeyValueRequest";
  }
  protected:
  explicit InsertKeyValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKvFieldNumber = 1,
    kAllowOverwriteFieldNumber = 2,
  };
  // .tensorflow.KeyValueEntry kv = 1;
  bool has_kv() const;
  private:
  bool _internal_has_kv() const;
  public:
  void clear_kv();
  const ::tensorflow::KeyValueEntry& kv() const;
  PROTOBUF_NODISCARD ::tensorflow::KeyValueEntry* release_kv();
  ::tensorflow::KeyValueEntry* mutable_kv();
  void set_allocated_kv(::tensorflow::KeyValueEntry* kv);
  private:
  const ::tensorflow::KeyValueEntry& _internal_kv() const;
  ::tensorflow::KeyValueEntry* _internal_mutable_kv();
  public:
  void unsafe_arena_set_allocated_kv(
      ::tensorflow::KeyValueEntry* kv);
  ::tensorflow::KeyValueEntry* unsafe_arena_release_kv();

  // bool allow_overwrite = 2;
  void clear_allow_overwrite();
  bool allow_overwrite() const;
  void set_allow_overwrite(bool value);
  private:
  bool _internal_allow_overwrite() const;
  void _internal_set_allow_overwrite(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.InsertKeyValueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::KeyValueEntry* kv_;
    bool allow_overwrite_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class InsertKeyValueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.InsertKeyValueResponse) */ {
 public:
  inline InsertKeyValueResponse() : InsertKeyValueResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR InsertKeyValueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InsertKeyValueResponse(const InsertKeyValueResponse& from);
  InsertKeyValueResponse(InsertKeyValueResponse&& from) noexcept
    : InsertKeyValueResponse() {
    *this = ::std::move(from);
  }

  inline InsertKeyValueResponse& operator=(const InsertKeyValueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline InsertKeyValueResponse& operator=(InsertKeyValueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InsertKeyValueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const InsertKeyValueResponse* internal_default_instance() {
    return reinterpret_cast<const InsertKeyValueResponse*>(
               &_InsertKeyValueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(InsertKeyValueResponse& a, InsertKeyValueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(InsertKeyValueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InsertKeyValueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InsertKeyValueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InsertKeyValueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const InsertKeyValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const InsertKeyValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.InsertKeyValueResponse";
  }
  protected:
  explicit InsertKeyValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.InsertKeyValueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetKeyValueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetKeyValueRequest) */ {
 public:
  inline GetKeyValueRequest() : GetKeyValueRequest(nullptr) {}
  ~GetKeyValueRequest() override;
  explicit PROTOBUF_CONSTEXPR GetKeyValueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetKeyValueRequest(const GetKeyValueRequest& from);
  GetKeyValueRequest(GetKeyValueRequest&& from) noexcept
    : GetKeyValueRequest() {
    *this = ::std::move(from);
  }

  inline GetKeyValueRequest& operator=(const GetKeyValueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetKeyValueRequest& operator=(GetKeyValueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetKeyValueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetKeyValueRequest* internal_default_instance() {
    return reinterpret_cast<const GetKeyValueRequest*>(
               &_GetKeyValueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(GetKeyValueRequest& a, GetKeyValueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetKeyValueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetKeyValueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetKeyValueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetKeyValueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetKeyValueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetKeyValueRequest& from) {
    GetKeyValueRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetKeyValueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetKeyValueRequest";
  }
  protected:
  explicit GetKeyValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GetKeyValueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetKeyValueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetKeyValueResponse) */ {
 public:
  inline GetKeyValueResponse() : GetKeyValueResponse(nullptr) {}
  ~GetKeyValueResponse() override;
  explicit PROTOBUF_CONSTEXPR GetKeyValueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetKeyValueResponse(const GetKeyValueResponse& from);
  GetKeyValueResponse(GetKeyValueResponse&& from) noexcept
    : GetKeyValueResponse() {
    *this = ::std::move(from);
  }

  inline GetKeyValueResponse& operator=(const GetKeyValueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetKeyValueResponse& operator=(GetKeyValueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetKeyValueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetKeyValueResponse* internal_default_instance() {
    return reinterpret_cast<const GetKeyValueResponse*>(
               &_GetKeyValueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(GetKeyValueResponse& a, GetKeyValueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetKeyValueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetKeyValueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetKeyValueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetKeyValueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetKeyValueResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetKeyValueResponse& from) {
    GetKeyValueResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetKeyValueResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetKeyValueResponse";
  }
  protected:
  explicit GetKeyValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKvFieldNumber = 1,
  };
  // .tensorflow.KeyValueEntry kv = 1;
  bool has_kv() const;
  private:
  bool _internal_has_kv() const;
  public:
  void clear_kv();
  const ::tensorflow::KeyValueEntry& kv() const;
  PROTOBUF_NODISCARD ::tensorflow::KeyValueEntry* release_kv();
  ::tensorflow::KeyValueEntry* mutable_kv();
  void set_allocated_kv(::tensorflow::KeyValueEntry* kv);
  private:
  const ::tensorflow::KeyValueEntry& _internal_kv() const;
  ::tensorflow::KeyValueEntry* _internal_mutable_kv();
  public:
  void unsafe_arena_set_allocated_kv(
      ::tensorflow::KeyValueEntry* kv);
  ::tensorflow::KeyValueEntry* unsafe_arena_release_kv();

  // @@protoc_insertion_point(class_scope:tensorflow.GetKeyValueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::KeyValueEntry* kv_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TryGetKeyValueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TryGetKeyValueRequest) */ {
 public:
  inline TryGetKeyValueRequest() : TryGetKeyValueRequest(nullptr) {}
  ~TryGetKeyValueRequest() override;
  explicit PROTOBUF_CONSTEXPR TryGetKeyValueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TryGetKeyValueRequest(const TryGetKeyValueRequest& from);
  TryGetKeyValueRequest(TryGetKeyValueRequest&& from) noexcept
    : TryGetKeyValueRequest() {
    *this = ::std::move(from);
  }

  inline TryGetKeyValueRequest& operator=(const TryGetKeyValueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TryGetKeyValueRequest& operator=(TryGetKeyValueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TryGetKeyValueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const TryGetKeyValueRequest* internal_default_instance() {
    return reinterpret_cast<const TryGetKeyValueRequest*>(
               &_TryGetKeyValueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(TryGetKeyValueRequest& a, TryGetKeyValueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TryGetKeyValueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TryGetKeyValueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TryGetKeyValueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TryGetKeyValueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TryGetKeyValueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TryGetKeyValueRequest& from) {
    TryGetKeyValueRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TryGetKeyValueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TryGetKeyValueRequest";
  }
  protected:
  explicit TryGetKeyValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TryGetKeyValueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TryGetKeyValueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TryGetKeyValueResponse) */ {
 public:
  inline TryGetKeyValueResponse() : TryGetKeyValueResponse(nullptr) {}
  ~TryGetKeyValueResponse() override;
  explicit PROTOBUF_CONSTEXPR TryGetKeyValueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TryGetKeyValueResponse(const TryGetKeyValueResponse& from);
  TryGetKeyValueResponse(TryGetKeyValueResponse&& from) noexcept
    : TryGetKeyValueResponse() {
    *this = ::std::move(from);
  }

  inline TryGetKeyValueResponse& operator=(const TryGetKeyValueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TryGetKeyValueResponse& operator=(TryGetKeyValueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TryGetKeyValueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TryGetKeyValueResponse* internal_default_instance() {
    return reinterpret_cast<const TryGetKeyValueResponse*>(
               &_TryGetKeyValueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(TryGetKeyValueResponse& a, TryGetKeyValueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TryGetKeyValueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TryGetKeyValueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TryGetKeyValueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TryGetKeyValueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TryGetKeyValueResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TryGetKeyValueResponse& from) {
    TryGetKeyValueResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TryGetKeyValueResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TryGetKeyValueResponse";
  }
  protected:
  explicit TryGetKeyValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKvFieldNumber = 1,
  };
  // .tensorflow.KeyValueEntry kv = 1;
  bool has_kv() const;
  private:
  bool _internal_has_kv() const;
  public:
  void clear_kv();
  const ::tensorflow::KeyValueEntry& kv() const;
  PROTOBUF_NODISCARD ::tensorflow::KeyValueEntry* release_kv();
  ::tensorflow::KeyValueEntry* mutable_kv();
  void set_allocated_kv(::tensorflow::KeyValueEntry* kv);
  private:
  const ::tensorflow::KeyValueEntry& _internal_kv() const;
  ::tensorflow::KeyValueEntry* _internal_mutable_kv();
  public:
  void unsafe_arena_set_allocated_kv(
      ::tensorflow::KeyValueEntry* kv);
  ::tensorflow::KeyValueEntry* unsafe_arena_release_kv();

  // @@protoc_insertion_point(class_scope:tensorflow.TryGetKeyValueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::KeyValueEntry* kv_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetKeyValueDirRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetKeyValueDirRequest) */ {
 public:
  inline GetKeyValueDirRequest() : GetKeyValueDirRequest(nullptr) {}
  ~GetKeyValueDirRequest() override;
  explicit PROTOBUF_CONSTEXPR GetKeyValueDirRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetKeyValueDirRequest(const GetKeyValueDirRequest& from);
  GetKeyValueDirRequest(GetKeyValueDirRequest&& from) noexcept
    : GetKeyValueDirRequest() {
    *this = ::std::move(from);
  }

  inline GetKeyValueDirRequest& operator=(const GetKeyValueDirRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetKeyValueDirRequest& operator=(GetKeyValueDirRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetKeyValueDirRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetKeyValueDirRequest* internal_default_instance() {
    return reinterpret_cast<const GetKeyValueDirRequest*>(
               &_GetKeyValueDirRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(GetKeyValueDirRequest& a, GetKeyValueDirRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetKeyValueDirRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetKeyValueDirRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetKeyValueDirRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetKeyValueDirRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetKeyValueDirRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetKeyValueDirRequest& from) {
    GetKeyValueDirRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetKeyValueDirRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetKeyValueDirRequest";
  }
  protected:
  explicit GetKeyValueDirRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDirectoryKeyFieldNumber = 1,
  };
  // string directory_key = 1;
  void clear_directory_key();
  const std::string& directory_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_directory_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_directory_key();
  PROTOBUF_NODISCARD std::string* release_directory_key();
  void set_allocated_directory_key(std::string* directory_key);
  private:
  const std::string& _internal_directory_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_directory_key(const std::string& value);
  std::string* _internal_mutable_directory_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GetKeyValueDirRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr directory_key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetKeyValueDirResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetKeyValueDirResponse) */ {
 public:
  inline GetKeyValueDirResponse() : GetKeyValueDirResponse(nullptr) {}
  ~GetKeyValueDirResponse() override;
  explicit PROTOBUF_CONSTEXPR GetKeyValueDirResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetKeyValueDirResponse(const GetKeyValueDirResponse& from);
  GetKeyValueDirResponse(GetKeyValueDirResponse&& from) noexcept
    : GetKeyValueDirResponse() {
    *this = ::std::move(from);
  }

  inline GetKeyValueDirResponse& operator=(const GetKeyValueDirResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetKeyValueDirResponse& operator=(GetKeyValueDirResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetKeyValueDirResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetKeyValueDirResponse* internal_default_instance() {
    return reinterpret_cast<const GetKeyValueDirResponse*>(
               &_GetKeyValueDirResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(GetKeyValueDirResponse& a, GetKeyValueDirResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetKeyValueDirResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetKeyValueDirResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetKeyValueDirResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetKeyValueDirResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetKeyValueDirResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetKeyValueDirResponse& from) {
    GetKeyValueDirResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetKeyValueDirResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetKeyValueDirResponse";
  }
  protected:
  explicit GetKeyValueDirResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKvFieldNumber = 2,
    kDirectoryKeyFieldNumber = 1,
  };
  // repeated .tensorflow.KeyValueEntry kv = 2;
  int kv_size() const;
  private:
  int _internal_kv_size() const;
  public:
  void clear_kv();
  ::tensorflow::KeyValueEntry* mutable_kv(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KeyValueEntry >*
      mutable_kv();
  private:
  const ::tensorflow::KeyValueEntry& _internal_kv(int index) const;
  ::tensorflow::KeyValueEntry* _internal_add_kv();
  public:
  const ::tensorflow::KeyValueEntry& kv(int index) const;
  ::tensorflow::KeyValueEntry* add_kv();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KeyValueEntry >&
      kv() const;

  // string directory_key = 1;
  void clear_directory_key();
  const std::string& directory_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_directory_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_directory_key();
  PROTOBUF_NODISCARD std::string* release_directory_key();
  void set_allocated_directory_key(std::string* directory_key);
  private:
  const std::string& _internal_directory_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_directory_key(const std::string& value);
  std::string* _internal_mutable_directory_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GetKeyValueDirResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KeyValueEntry > kv_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr directory_key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class DeleteKeyValueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteKeyValueRequest) */ {
 public:
  inline DeleteKeyValueRequest() : DeleteKeyValueRequest(nullptr) {}
  ~DeleteKeyValueRequest() override;
  explicit PROTOBUF_CONSTEXPR DeleteKeyValueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteKeyValueRequest(const DeleteKeyValueRequest& from);
  DeleteKeyValueRequest(DeleteKeyValueRequest&& from) noexcept
    : DeleteKeyValueRequest() {
    *this = ::std::move(from);
  }

  inline DeleteKeyValueRequest& operator=(const DeleteKeyValueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteKeyValueRequest& operator=(DeleteKeyValueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteKeyValueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteKeyValueRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteKeyValueRequest*>(
               &_DeleteKeyValueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(DeleteKeyValueRequest& a, DeleteKeyValueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteKeyValueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteKeyValueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteKeyValueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteKeyValueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteKeyValueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeleteKeyValueRequest& from) {
    DeleteKeyValueRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteKeyValueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteKeyValueRequest";
  }
  protected:
  explicit DeleteKeyValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kIsDirectoryFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // bool is_directory = 2;
  void clear_is_directory();
  bool is_directory() const;
  void set_is_directory(bool value);
  private:
  bool _internal_is_directory() const;
  void _internal_set_is_directory(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteKeyValueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    bool is_directory_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class DeleteKeyValueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.DeleteKeyValueResponse) */ {
 public:
  inline DeleteKeyValueResponse() : DeleteKeyValueResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR DeleteKeyValueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteKeyValueResponse(const DeleteKeyValueResponse& from);
  DeleteKeyValueResponse(DeleteKeyValueResponse&& from) noexcept
    : DeleteKeyValueResponse() {
    *this = ::std::move(from);
  }

  inline DeleteKeyValueResponse& operator=(const DeleteKeyValueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteKeyValueResponse& operator=(DeleteKeyValueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteKeyValueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteKeyValueResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteKeyValueResponse*>(
               &_DeleteKeyValueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(DeleteKeyValueResponse& a, DeleteKeyValueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteKeyValueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteKeyValueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteKeyValueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteKeyValueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const DeleteKeyValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const DeleteKeyValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteKeyValueResponse";
  }
  protected:
  explicit DeleteKeyValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteKeyValueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class BarrierRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BarrierRequest) */ {
 public:
  inline BarrierRequest() : BarrierRequest(nullptr) {}
  ~BarrierRequest() override;
  explicit PROTOBUF_CONSTEXPR BarrierRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BarrierRequest(const BarrierRequest& from);
  BarrierRequest(BarrierRequest&& from) noexcept
    : BarrierRequest() {
    *this = ::std::move(from);
  }

  inline BarrierRequest& operator=(const BarrierRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline BarrierRequest& operator=(BarrierRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BarrierRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const BarrierRequest* internal_default_instance() {
    return reinterpret_cast<const BarrierRequest*>(
               &_BarrierRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  friend void swap(BarrierRequest& a, BarrierRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(BarrierRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BarrierRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BarrierRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BarrierRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BarrierRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BarrierRequest& from) {
    BarrierRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BarrierRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BarrierRequest";
  }
  protected:
  explicit BarrierRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTasksFieldNumber = 3,
    kBarrierIdFieldNumber = 1,
    kSourceTaskFieldNumber = 4,
    kBarrierTimeoutInMsFieldNumber = 2,
    kCounterFieldNumber = 5,
  };
  // repeated .tensorflow.CoordinatedTask tasks = 3;
  int tasks_size() const;
  private:
  int _internal_tasks_size() const;
  public:
  void clear_tasks();
  ::tensorflow::CoordinatedTask* mutable_tasks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
      mutable_tasks();
  private:
  const ::tensorflow::CoordinatedTask& _internal_tasks(int index) const;
  ::tensorflow::CoordinatedTask* _internal_add_tasks();
  public:
  const ::tensorflow::CoordinatedTask& tasks(int index) const;
  ::tensorflow::CoordinatedTask* add_tasks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
      tasks() const;

  // string barrier_id = 1;
  void clear_barrier_id();
  const std::string& barrier_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_barrier_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_barrier_id();
  PROTOBUF_NODISCARD std::string* release_barrier_id();
  void set_allocated_barrier_id(std::string* barrier_id);
  private:
  const std::string& _internal_barrier_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_barrier_id(const std::string& value);
  std::string* _internal_mutable_barrier_id();
  public:

  // .tensorflow.CoordinatedTask source_task = 4;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // int64 barrier_timeout_in_ms = 2;
  void clear_barrier_timeout_in_ms();
  int64_t barrier_timeout_in_ms() const;
  void set_barrier_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_barrier_timeout_in_ms() const;
  void _internal_set_barrier_timeout_in_ms(int64_t value);
  public:

  // int64 counter = 5;
  void clear_counter();
  int64_t counter() const;
  void set_counter(int64_t value);
  private:
  int64_t _internal_counter() const;
  void _internal_set_counter(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BarrierRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask > tasks_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr barrier_id_;
    ::tensorflow::CoordinatedTask* source_task_;
    int64_t barrier_timeout_in_ms_;
    int64_t counter_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class BarrierResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BarrierResponse) */ {
 public:
  inline BarrierResponse() : BarrierResponse(nullptr) {}
  ~BarrierResponse() override;
  explicit PROTOBUF_CONSTEXPR BarrierResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BarrierResponse(const BarrierResponse& from);
  BarrierResponse(BarrierResponse&& from) noexcept
    : BarrierResponse() {
    *this = ::std::move(from);
  }

  inline BarrierResponse& operator=(const BarrierResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline BarrierResponse& operator=(BarrierResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BarrierResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const BarrierResponse* internal_default_instance() {
    return reinterpret_cast<const BarrierResponse*>(
               &_BarrierResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  friend void swap(BarrierResponse& a, BarrierResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(BarrierResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BarrierResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BarrierResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BarrierResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BarrierResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BarrierResponse& from) {
    BarrierResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BarrierResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BarrierResponse";
  }
  protected:
  explicit BarrierResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCounterFieldNumber = 1,
  };
  // int64 counter = 1;
  void clear_counter();
  int64_t counter() const;
  void set_counter(int64_t value);
  private:
  int64_t _internal_counter() const;
  void _internal_set_counter(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BarrierResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t counter_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetAliveTasksRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetAliveTasksRequest) */ {
 public:
  inline GetAliveTasksRequest() : GetAliveTasksRequest(nullptr) {}
  ~GetAliveTasksRequest() override;
  explicit PROTOBUF_CONSTEXPR GetAliveTasksRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetAliveTasksRequest(const GetAliveTasksRequest& from);
  GetAliveTasksRequest(GetAliveTasksRequest&& from) noexcept
    : GetAliveTasksRequest() {
    *this = ::std::move(from);
  }

  inline GetAliveTasksRequest& operator=(const GetAliveTasksRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetAliveTasksRequest& operator=(GetAliveTasksRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetAliveTasksRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetAliveTasksRequest* internal_default_instance() {
    return reinterpret_cast<const GetAliveTasksRequest*>(
               &_GetAliveTasksRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    36;

  friend void swap(GetAliveTasksRequest& a, GetAliveTasksRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetAliveTasksRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetAliveTasksRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetAliveTasksRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetAliveTasksRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetAliveTasksRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetAliveTasksRequest& from) {
    GetAliveTasksRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetAliveTasksRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetAliveTasksRequest";
  }
  protected:
  explicit GetAliveTasksRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTasksFieldNumber = 2,
    kRequestingTaskFieldNumber = 1,
  };
  // repeated .tensorflow.CoordinatedTask tasks = 2;
  int tasks_size() const;
  private:
  int _internal_tasks_size() const;
  public:
  void clear_tasks();
  ::tensorflow::CoordinatedTask* mutable_tasks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
      mutable_tasks();
  private:
  const ::tensorflow::CoordinatedTask& _internal_tasks(int index) const;
  ::tensorflow::CoordinatedTask* _internal_add_tasks();
  public:
  const ::tensorflow::CoordinatedTask& tasks(int index) const;
  ::tensorflow::CoordinatedTask* add_tasks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
      tasks() const;

  // .tensorflow.CoordinatedTask requesting_task = 1;
  bool has_requesting_task() const;
  private:
  bool _internal_has_requesting_task() const;
  public:
  void clear_requesting_task();
  const ::tensorflow::CoordinatedTask& requesting_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_requesting_task();
  ::tensorflow::CoordinatedTask* mutable_requesting_task();
  void set_allocated_requesting_task(::tensorflow::CoordinatedTask* requesting_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_requesting_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_requesting_task();
  public:
  void unsafe_arena_set_allocated_requesting_task(
      ::tensorflow::CoordinatedTask* requesting_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_requesting_task();

  // @@protoc_insertion_point(class_scope:tensorflow.GetAliveTasksRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask > tasks_;
    ::tensorflow::CoordinatedTask* requesting_task_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetAliveTasksResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetAliveTasksResponse) */ {
 public:
  inline GetAliveTasksResponse() : GetAliveTasksResponse(nullptr) {}
  ~GetAliveTasksResponse() override;
  explicit PROTOBUF_CONSTEXPR GetAliveTasksResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetAliveTasksResponse(const GetAliveTasksResponse& from);
  GetAliveTasksResponse(GetAliveTasksResponse&& from) noexcept
    : GetAliveTasksResponse() {
    *this = ::std::move(from);
  }

  inline GetAliveTasksResponse& operator=(const GetAliveTasksResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetAliveTasksResponse& operator=(GetAliveTasksResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetAliveTasksResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetAliveTasksResponse* internal_default_instance() {
    return reinterpret_cast<const GetAliveTasksResponse*>(
               &_GetAliveTasksResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    37;

  friend void swap(GetAliveTasksResponse& a, GetAliveTasksResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetAliveTasksResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetAliveTasksResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetAliveTasksResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetAliveTasksResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetAliveTasksResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetAliveTasksResponse& from) {
    GetAliveTasksResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetAliveTasksResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetAliveTasksResponse";
  }
  protected:
  explicit GetAliveTasksResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAliveTasksFieldNumber = 1,
  };
  // repeated .tensorflow.CoordinatedTask alive_tasks = 1;
  int alive_tasks_size() const;
  private:
  int _internal_alive_tasks_size() const;
  public:
  void clear_alive_tasks();
  ::tensorflow::CoordinatedTask* mutable_alive_tasks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
      mutable_alive_tasks();
  private:
  const ::tensorflow::CoordinatedTask& _internal_alive_tasks(int index) const;
  ::tensorflow::CoordinatedTask* _internal_add_alive_tasks();
  public:
  const ::tensorflow::CoordinatedTask& alive_tasks(int index) const;
  ::tensorflow::CoordinatedTask* add_alive_tasks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
      alive_tasks() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetAliveTasksResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask > alive_tasks_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CancelBarrierRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CancelBarrierRequest) */ {
 public:
  inline CancelBarrierRequest() : CancelBarrierRequest(nullptr) {}
  ~CancelBarrierRequest() override;
  explicit PROTOBUF_CONSTEXPR CancelBarrierRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CancelBarrierRequest(const CancelBarrierRequest& from);
  CancelBarrierRequest(CancelBarrierRequest&& from) noexcept
    : CancelBarrierRequest() {
    *this = ::std::move(from);
  }

  inline CancelBarrierRequest& operator=(const CancelBarrierRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CancelBarrierRequest& operator=(CancelBarrierRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CancelBarrierRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CancelBarrierRequest* internal_default_instance() {
    return reinterpret_cast<const CancelBarrierRequest*>(
               &_CancelBarrierRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    38;

  friend void swap(CancelBarrierRequest& a, CancelBarrierRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CancelBarrierRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CancelBarrierRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CancelBarrierRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CancelBarrierRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CancelBarrierRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CancelBarrierRequest& from) {
    CancelBarrierRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CancelBarrierRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CancelBarrierRequest";
  }
  protected:
  explicit CancelBarrierRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBarrierIdFieldNumber = 1,
    kSourceTaskFieldNumber = 2,
    kCounterFieldNumber = 3,
  };
  // string barrier_id = 1;
  void clear_barrier_id();
  const std::string& barrier_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_barrier_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_barrier_id();
  PROTOBUF_NODISCARD std::string* release_barrier_id();
  void set_allocated_barrier_id(std::string* barrier_id);
  private:
  const std::string& _internal_barrier_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_barrier_id(const std::string& value);
  std::string* _internal_mutable_barrier_id();
  public:

  // .tensorflow.CoordinatedTask source_task = 2;
  bool has_source_task() const;
  private:
  bool _internal_has_source_task() const;
  public:
  void clear_source_task();
  const ::tensorflow::CoordinatedTask& source_task() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinatedTask* release_source_task();
  ::tensorflow::CoordinatedTask* mutable_source_task();
  void set_allocated_source_task(::tensorflow::CoordinatedTask* source_task);
  private:
  const ::tensorflow::CoordinatedTask& _internal_source_task() const;
  ::tensorflow::CoordinatedTask* _internal_mutable_source_task();
  public:
  void unsafe_arena_set_allocated_source_task(
      ::tensorflow::CoordinatedTask* source_task);
  ::tensorflow::CoordinatedTask* unsafe_arena_release_source_task();

  // int64 counter = 3;
  void clear_counter();
  int64_t counter() const;
  void set_counter(int64_t value);
  private:
  int64_t _internal_counter() const;
  void _internal_set_counter(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CancelBarrierRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr barrier_id_;
    ::tensorflow::CoordinatedTask* source_task_;
    int64_t counter_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CancelBarrierResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.CancelBarrierResponse) */ {
 public:
  inline CancelBarrierResponse() : CancelBarrierResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CancelBarrierResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CancelBarrierResponse(const CancelBarrierResponse& from);
  CancelBarrierResponse(CancelBarrierResponse&& from) noexcept
    : CancelBarrierResponse() {
    *this = ::std::move(from);
  }

  inline CancelBarrierResponse& operator=(const CancelBarrierResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CancelBarrierResponse& operator=(CancelBarrierResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CancelBarrierResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CancelBarrierResponse* internal_default_instance() {
    return reinterpret_cast<const CancelBarrierResponse*>(
               &_CancelBarrierResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    39;

  friend void swap(CancelBarrierResponse& a, CancelBarrierResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CancelBarrierResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CancelBarrierResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CancelBarrierResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CancelBarrierResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CancelBarrierResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CancelBarrierResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CancelBarrierResponse";
  }
  protected:
  explicit CancelBarrierResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CancelBarrierResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CoordinatedTask

// string job_name = 1;
inline void CoordinatedTask::clear_job_name() {
  _impl_.job_name_.ClearToEmpty();
}
inline const std::string& CoordinatedTask::job_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTask.job_name)
  return _internal_job_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CoordinatedTask::set_job_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.job_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTask.job_name)
}
inline std::string* CoordinatedTask::mutable_job_name() {
  std::string* _s = _internal_mutable_job_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinatedTask.job_name)
  return _s;
}
inline const std::string& CoordinatedTask::_internal_job_name() const {
  return _impl_.job_name_.Get();
}
inline void CoordinatedTask::_internal_set_job_name(const std::string& value) {
  
  _impl_.job_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CoordinatedTask::_internal_mutable_job_name() {
  
  return _impl_.job_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CoordinatedTask::release_job_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinatedTask.job_name)
  return _impl_.job_name_.Release();
}
inline void CoordinatedTask::set_allocated_job_name(std::string* job_name) {
  if (job_name != nullptr) {
    
  } else {
    
  }
  _impl_.job_name_.SetAllocated(job_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.job_name_.IsDefault()) {
    _impl_.job_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinatedTask.job_name)
}

// int32 task_id = 2;
inline void CoordinatedTask::clear_task_id() {
  _impl_.task_id_ = 0;
}
inline int32_t CoordinatedTask::_internal_task_id() const {
  return _impl_.task_id_;
}
inline int32_t CoordinatedTask::task_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTask.task_id)
  return _internal_task_id();
}
inline void CoordinatedTask::_internal_set_task_id(int32_t value) {
  
  _impl_.task_id_ = value;
}
inline void CoordinatedTask::set_task_id(int32_t value) {
  _internal_set_task_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTask.task_id)
}

// bool recoverable = 3;
inline void CoordinatedTask::clear_recoverable() {
  _impl_.recoverable_ = false;
}
inline bool CoordinatedTask::_internal_recoverable() const {
  return _impl_.recoverable_;
}
inline bool CoordinatedTask::recoverable() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTask.recoverable)
  return _internal_recoverable();
}
inline void CoordinatedTask::_internal_set_recoverable(bool value) {
  
  _impl_.recoverable_ = value;
}
inline void CoordinatedTask::set_recoverable(bool value) {
  _internal_set_recoverable(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTask.recoverable)
}

// -------------------------------------------------------------------

// CoordinationServiceError

// bool is_reported_error = 3;
inline void CoordinationServiceError::clear_is_reported_error() {
  _impl_.is_reported_error_ = false;
}
inline bool CoordinationServiceError::_internal_is_reported_error() const {
  return _impl_.is_reported_error_;
}
inline bool CoordinationServiceError::is_reported_error() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceError.is_reported_error)
  return _internal_is_reported_error();
}
inline void CoordinationServiceError::_internal_set_is_reported_error(bool value) {
  
  _impl_.is_reported_error_ = value;
}
inline void CoordinationServiceError::set_is_reported_error(bool value) {
  _internal_set_is_reported_error(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinationServiceError.is_reported_error)
}

// .tensorflow.CoordinatedTask source_task = 4;
inline bool CoordinationServiceError::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool CoordinationServiceError::has_source_task() const {
  return _internal_has_source_task();
}
inline void CoordinationServiceError::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& CoordinationServiceError::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& CoordinationServiceError::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinationServiceError.source_task)
  return _internal_source_task();
}
inline void CoordinationServiceError::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CoordinationServiceError.source_task)
}
inline ::tensorflow::CoordinatedTask* CoordinationServiceError::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* CoordinationServiceError::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinationServiceError.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* CoordinationServiceError::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* CoordinationServiceError::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinationServiceError.source_task)
  return _msg;
}
inline void CoordinationServiceError::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinationServiceError.source_task)
}

// -------------------------------------------------------------------

// BarrierError

// string barrier_id = 1;
inline void BarrierError::clear_barrier_id() {
  _impl_.barrier_id_.ClearToEmpty();
}
inline const std::string& BarrierError::barrier_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierError.barrier_id)
  return _internal_barrier_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BarrierError::set_barrier_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.barrier_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.BarrierError.barrier_id)
}
inline std::string* BarrierError::mutable_barrier_id() {
  std::string* _s = _internal_mutable_barrier_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.BarrierError.barrier_id)
  return _s;
}
inline const std::string& BarrierError::_internal_barrier_id() const {
  return _impl_.barrier_id_.Get();
}
inline void BarrierError::_internal_set_barrier_id(const std::string& value) {
  
  _impl_.barrier_id_.Set(value, GetArenaForAllocation());
}
inline std::string* BarrierError::_internal_mutable_barrier_id() {
  
  return _impl_.barrier_id_.Mutable(GetArenaForAllocation());
}
inline std::string* BarrierError::release_barrier_id() {
  // @@protoc_insertion_point(field_release:tensorflow.BarrierError.barrier_id)
  return _impl_.barrier_id_.Release();
}
inline void BarrierError::set_allocated_barrier_id(std::string* barrier_id) {
  if (barrier_id != nullptr) {
    
  } else {
    
  }
  _impl_.barrier_id_.SetAllocated(barrier_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.barrier_id_.IsDefault()) {
    _impl_.barrier_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BarrierError.barrier_id)
}

// int64 counter = 2;
inline void BarrierError::clear_counter() {
  _impl_.counter_ = int64_t{0};
}
inline int64_t BarrierError::_internal_counter() const {
  return _impl_.counter_;
}
inline int64_t BarrierError::counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierError.counter)
  return _internal_counter();
}
inline void BarrierError::_internal_set_counter(int64_t value) {
  
  _impl_.counter_ = value;
}
inline void BarrierError::set_counter(int64_t value) {
  _internal_set_counter(value);
  // @@protoc_insertion_point(field_set:tensorflow.BarrierError.counter)
}

// -------------------------------------------------------------------

// CoordinatedTaskStateInfo

// .tensorflow.CoordinatedTask task = 1;
inline bool CoordinatedTaskStateInfo::_internal_has_task() const {
  return this != internal_default_instance() && _impl_.task_ != nullptr;
}
inline bool CoordinatedTaskStateInfo::has_task() const {
  return _internal_has_task();
}
inline void CoordinatedTaskStateInfo::clear_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.task_ != nullptr) {
    delete _impl_.task_;
  }
  _impl_.task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& CoordinatedTaskStateInfo::_internal_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& CoordinatedTaskStateInfo::task() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTaskStateInfo.task)
  return _internal_task();
}
inline void CoordinatedTaskStateInfo::unsafe_arena_set_allocated_task(
    ::tensorflow::CoordinatedTask* task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.task_);
  }
  _impl_.task_ = task;
  if (task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CoordinatedTaskStateInfo.task)
}
inline ::tensorflow::CoordinatedTask* CoordinatedTaskStateInfo::release_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.task_;
  _impl_.task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* CoordinatedTaskStateInfo::unsafe_arena_release_task() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinatedTaskStateInfo.task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.task_;
  _impl_.task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* CoordinatedTaskStateInfo::_internal_mutable_task() {
  
  if (_impl_.task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.task_ = p;
  }
  return _impl_.task_;
}
inline ::tensorflow::CoordinatedTask* CoordinatedTaskStateInfo::mutable_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinatedTaskStateInfo.task)
  return _msg;
}
inline void CoordinatedTaskStateInfo::set_allocated_task(::tensorflow::CoordinatedTask* task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.task_;
  }
  if (task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(task);
    if (message_arena != submessage_arena) {
      task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.task_ = task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinatedTaskStateInfo.task)
}

// .tensorflow.CoordinatedTaskState state = 2;
inline void CoordinatedTaskStateInfo::clear_state() {
  _impl_.state_ = 0;
}
inline ::tensorflow::CoordinatedTaskState CoordinatedTaskStateInfo::_internal_state() const {
  return static_cast< ::tensorflow::CoordinatedTaskState >(_impl_.state_);
}
inline ::tensorflow::CoordinatedTaskState CoordinatedTaskStateInfo::state() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTaskStateInfo.state)
  return _internal_state();
}
inline void CoordinatedTaskStateInfo::_internal_set_state(::tensorflow::CoordinatedTaskState value) {
  
  _impl_.state_ = value;
}
inline void CoordinatedTaskStateInfo::set_state(::tensorflow::CoordinatedTaskState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTaskStateInfo.state)
}

// int32 error_code = 3;
inline void CoordinatedTaskStateInfo::clear_error_code() {
  _impl_.error_code_ = 0;
}
inline int32_t CoordinatedTaskStateInfo::_internal_error_code() const {
  return _impl_.error_code_;
}
inline int32_t CoordinatedTaskStateInfo::error_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTaskStateInfo.error_code)
  return _internal_error_code();
}
inline void CoordinatedTaskStateInfo::_internal_set_error_code(int32_t value) {
  
  _impl_.error_code_ = value;
}
inline void CoordinatedTaskStateInfo::set_error_code(int32_t value) {
  _internal_set_error_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTaskStateInfo.error_code)
}

// string error_message = 4;
inline void CoordinatedTaskStateInfo::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& CoordinatedTaskStateInfo::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTaskStateInfo.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CoordinatedTaskStateInfo::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CoordinatedTaskStateInfo.error_message)
}
inline std::string* CoordinatedTaskStateInfo::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinatedTaskStateInfo.error_message)
  return _s;
}
inline const std::string& CoordinatedTaskStateInfo::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void CoordinatedTaskStateInfo::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* CoordinatedTaskStateInfo::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* CoordinatedTaskStateInfo::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinatedTaskStateInfo.error_message)
  return _impl_.error_message_.Release();
}
inline void CoordinatedTaskStateInfo::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinatedTaskStateInfo.error_message)
}

// .tensorflow.CoordinationServiceError error_payload = 5;
inline bool CoordinatedTaskStateInfo::_internal_has_error_payload() const {
  return this != internal_default_instance() && _impl_.error_payload_ != nullptr;
}
inline bool CoordinatedTaskStateInfo::has_error_payload() const {
  return _internal_has_error_payload();
}
inline void CoordinatedTaskStateInfo::clear_error_payload() {
  if (GetArenaForAllocation() == nullptr && _impl_.error_payload_ != nullptr) {
    delete _impl_.error_payload_;
  }
  _impl_.error_payload_ = nullptr;
}
inline const ::tensorflow::CoordinationServiceError& CoordinatedTaskStateInfo::_internal_error_payload() const {
  const ::tensorflow::CoordinationServiceError* p = _impl_.error_payload_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinationServiceError&>(
      ::tensorflow::_CoordinationServiceError_default_instance_);
}
inline const ::tensorflow::CoordinationServiceError& CoordinatedTaskStateInfo::error_payload() const {
  // @@protoc_insertion_point(field_get:tensorflow.CoordinatedTaskStateInfo.error_payload)
  return _internal_error_payload();
}
inline void CoordinatedTaskStateInfo::unsafe_arena_set_allocated_error_payload(
    ::tensorflow::CoordinationServiceError* error_payload) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.error_payload_);
  }
  _impl_.error_payload_ = error_payload;
  if (error_payload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CoordinatedTaskStateInfo.error_payload)
}
inline ::tensorflow::CoordinationServiceError* CoordinatedTaskStateInfo::release_error_payload() {
  
  ::tensorflow::CoordinationServiceError* temp = _impl_.error_payload_;
  _impl_.error_payload_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinationServiceError* CoordinatedTaskStateInfo::unsafe_arena_release_error_payload() {
  // @@protoc_insertion_point(field_release:tensorflow.CoordinatedTaskStateInfo.error_payload)
  
  ::tensorflow::CoordinationServiceError* temp = _impl_.error_payload_;
  _impl_.error_payload_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinationServiceError* CoordinatedTaskStateInfo::_internal_mutable_error_payload() {
  
  if (_impl_.error_payload_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinationServiceError>(GetArenaForAllocation());
    _impl_.error_payload_ = p;
  }
  return _impl_.error_payload_;
}
inline ::tensorflow::CoordinationServiceError* CoordinatedTaskStateInfo::mutable_error_payload() {
  ::tensorflow::CoordinationServiceError* _msg = _internal_mutable_error_payload();
  // @@protoc_insertion_point(field_mutable:tensorflow.CoordinatedTaskStateInfo.error_payload)
  return _msg;
}
inline void CoordinatedTaskStateInfo::set_allocated_error_payload(::tensorflow::CoordinationServiceError* error_payload) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.error_payload_;
  }
  if (error_payload) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(error_payload);
    if (message_arena != submessage_arena) {
      error_payload = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, error_payload, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.error_payload_ = error_payload;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CoordinatedTaskStateInfo.error_payload)
}

// -------------------------------------------------------------------

// DeviceInfo

// repeated .google.protobuf.Any device = 1;
inline int DeviceInfo::_internal_device_size() const {
  return _impl_.device_.size();
}
inline int DeviceInfo::device_size() const {
  return _internal_device_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* DeviceInfo::mutable_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceInfo.device)
  return _impl_.device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
DeviceInfo::mutable_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DeviceInfo.device)
  return &_impl_.device_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& DeviceInfo::_internal_device(int index) const {
  return _impl_.device_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& DeviceInfo::device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceInfo.device)
  return _internal_device(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Any* DeviceInfo::_internal_add_device() {
  return _impl_.device_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* DeviceInfo::add_device() {
  ::PROTOBUF_NAMESPACE_ID::Any* _add = _internal_add_device();
  // @@protoc_insertion_point(field_add:tensorflow.DeviceInfo.device)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
DeviceInfo::device() const {
  // @@protoc_insertion_point(field_list:tensorflow.DeviceInfo.device)
  return _impl_.device_;
}

// -------------------------------------------------------------------

// RegisterTaskRequest

// fixed64 incarnation = 3;
inline void RegisterTaskRequest::clear_incarnation() {
  _impl_.incarnation_ = uint64_t{0u};
}
inline uint64_t RegisterTaskRequest::_internal_incarnation() const {
  return _impl_.incarnation_;
}
inline uint64_t RegisterTaskRequest::incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterTaskRequest.incarnation)
  return _internal_incarnation();
}
inline void RegisterTaskRequest::_internal_set_incarnation(uint64_t value) {
  
  _impl_.incarnation_ = value;
}
inline void RegisterTaskRequest::set_incarnation(uint64_t value) {
  _internal_set_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.RegisterTaskRequest.incarnation)
}

// .tensorflow.CoordinatedTask source_task = 5;
inline bool RegisterTaskRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool RegisterTaskRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void RegisterTaskRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& RegisterTaskRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& RegisterTaskRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterTaskRequest.source_task)
  return _internal_source_task();
}
inline void RegisterTaskRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterTaskRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* RegisterTaskRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* RegisterTaskRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterTaskRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* RegisterTaskRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* RegisterTaskRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterTaskRequest.source_task)
  return _msg;
}
inline void RegisterTaskRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterTaskRequest.source_task)
}

// -------------------------------------------------------------------

// RegisterTaskResponse

// fixed64 leader_incarnation = 1;
inline void RegisterTaskResponse::clear_leader_incarnation() {
  _impl_.leader_incarnation_ = uint64_t{0u};
}
inline uint64_t RegisterTaskResponse::_internal_leader_incarnation() const {
  return _impl_.leader_incarnation_;
}
inline uint64_t RegisterTaskResponse::leader_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterTaskResponse.leader_incarnation)
  return _internal_leader_incarnation();
}
inline void RegisterTaskResponse::_internal_set_leader_incarnation(uint64_t value) {
  
  _impl_.leader_incarnation_ = value;
}
inline void RegisterTaskResponse::set_leader_incarnation(uint64_t value) {
  _internal_set_leader_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.RegisterTaskResponse.leader_incarnation)
}

// -------------------------------------------------------------------

// HeartbeatRequest

// fixed64 incarnation = 3;
inline void HeartbeatRequest::clear_incarnation() {
  _impl_.incarnation_ = uint64_t{0u};
}
inline uint64_t HeartbeatRequest::_internal_incarnation() const {
  return _impl_.incarnation_;
}
inline uint64_t HeartbeatRequest::incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.HeartbeatRequest.incarnation)
  return _internal_incarnation();
}
inline void HeartbeatRequest::_internal_set_incarnation(uint64_t value) {
  
  _impl_.incarnation_ = value;
}
inline void HeartbeatRequest::set_incarnation(uint64_t value) {
  _internal_set_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.HeartbeatRequest.incarnation)
}

// .tensorflow.CoordinatedTask source_task = 4;
inline bool HeartbeatRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool HeartbeatRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void HeartbeatRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& HeartbeatRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& HeartbeatRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.HeartbeatRequest.source_task)
  return _internal_source_task();
}
inline void HeartbeatRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.HeartbeatRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* HeartbeatRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* HeartbeatRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.HeartbeatRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* HeartbeatRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* HeartbeatRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.HeartbeatRequest.source_task)
  return _msg;
}
inline void HeartbeatRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.HeartbeatRequest.source_task)
}

// -------------------------------------------------------------------

// HeartbeatResponse

// fixed64 leader_incarnation = 1;
inline void HeartbeatResponse::clear_leader_incarnation() {
  _impl_.leader_incarnation_ = uint64_t{0u};
}
inline uint64_t HeartbeatResponse::_internal_leader_incarnation() const {
  return _impl_.leader_incarnation_;
}
inline uint64_t HeartbeatResponse::leader_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.HeartbeatResponse.leader_incarnation)
  return _internal_leader_incarnation();
}
inline void HeartbeatResponse::_internal_set_leader_incarnation(uint64_t value) {
  
  _impl_.leader_incarnation_ = value;
}
inline void HeartbeatResponse::set_leader_incarnation(uint64_t value) {
  _internal_set_leader_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.HeartbeatResponse.leader_incarnation)
}

// -------------------------------------------------------------------

// PollForErrorRequest

// .tensorflow.CoordinatedTask source_task = 1;
inline bool PollForErrorRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool PollForErrorRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void PollForErrorRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& PollForErrorRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& PollForErrorRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.PollForErrorRequest.source_task)
  return _internal_source_task();
}
inline void PollForErrorRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PollForErrorRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* PollForErrorRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* PollForErrorRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.PollForErrorRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* PollForErrorRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* PollForErrorRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.PollForErrorRequest.source_task)
  return _msg;
}
inline void PollForErrorRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PollForErrorRequest.source_task)
}

// -------------------------------------------------------------------

// PollForErrorResponse

// -------------------------------------------------------------------

// WaitForAllTasksRequest

// .tensorflow.CoordinatedTask source_task = 5;
inline bool WaitForAllTasksRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool WaitForAllTasksRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void WaitForAllTasksRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& WaitForAllTasksRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& WaitForAllTasksRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.WaitForAllTasksRequest.source_task)
  return _internal_source_task();
}
inline void WaitForAllTasksRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WaitForAllTasksRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* WaitForAllTasksRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* WaitForAllTasksRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.WaitForAllTasksRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* WaitForAllTasksRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* WaitForAllTasksRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.WaitForAllTasksRequest.source_task)
  return _msg;
}
inline void WaitForAllTasksRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WaitForAllTasksRequest.source_task)
}

// .tensorflow.DeviceInfo device_info = 6;
inline bool WaitForAllTasksRequest::_internal_has_device_info() const {
  return this != internal_default_instance() && _impl_.device_info_ != nullptr;
}
inline bool WaitForAllTasksRequest::has_device_info() const {
  return _internal_has_device_info();
}
inline void WaitForAllTasksRequest::clear_device_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.device_info_ != nullptr) {
    delete _impl_.device_info_;
  }
  _impl_.device_info_ = nullptr;
}
inline const ::tensorflow::DeviceInfo& WaitForAllTasksRequest::_internal_device_info() const {
  const ::tensorflow::DeviceInfo* p = _impl_.device_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceInfo&>(
      ::tensorflow::_DeviceInfo_default_instance_);
}
inline const ::tensorflow::DeviceInfo& WaitForAllTasksRequest::device_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.WaitForAllTasksRequest.device_info)
  return _internal_device_info();
}
inline void WaitForAllTasksRequest::unsafe_arena_set_allocated_device_info(
    ::tensorflow::DeviceInfo* device_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_info_);
  }
  _impl_.device_info_ = device_info;
  if (device_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WaitForAllTasksRequest.device_info)
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksRequest::release_device_info() {
  
  ::tensorflow::DeviceInfo* temp = _impl_.device_info_;
  _impl_.device_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksRequest::unsafe_arena_release_device_info() {
  // @@protoc_insertion_point(field_release:tensorflow.WaitForAllTasksRequest.device_info)
  
  ::tensorflow::DeviceInfo* temp = _impl_.device_info_;
  _impl_.device_info_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksRequest::_internal_mutable_device_info() {
  
  if (_impl_.device_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceInfo>(GetArenaForAllocation());
    _impl_.device_info_ = p;
  }
  return _impl_.device_info_;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksRequest::mutable_device_info() {
  ::tensorflow::DeviceInfo* _msg = _internal_mutable_device_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.WaitForAllTasksRequest.device_info)
  return _msg;
}
inline void WaitForAllTasksRequest::set_allocated_device_info(::tensorflow::DeviceInfo* device_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.device_info_;
  }
  if (device_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(device_info);
    if (message_arena != submessage_arena) {
      device_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_info_ = device_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WaitForAllTasksRequest.device_info)
}

// -------------------------------------------------------------------

// WaitForAllTasksResponse

// fixed64 leader_incarnation = 1;
inline void WaitForAllTasksResponse::clear_leader_incarnation() {
  _impl_.leader_incarnation_ = uint64_t{0u};
}
inline uint64_t WaitForAllTasksResponse::_internal_leader_incarnation() const {
  return _impl_.leader_incarnation_;
}
inline uint64_t WaitForAllTasksResponse::leader_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.WaitForAllTasksResponse.leader_incarnation)
  return _internal_leader_incarnation();
}
inline void WaitForAllTasksResponse::_internal_set_leader_incarnation(uint64_t value) {
  
  _impl_.leader_incarnation_ = value;
}
inline void WaitForAllTasksResponse::set_leader_incarnation(uint64_t value) {
  _internal_set_leader_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.WaitForAllTasksResponse.leader_incarnation)
}

// .tensorflow.DeviceInfo device_info = 4;
inline bool WaitForAllTasksResponse::_internal_has_device_info() const {
  return this != internal_default_instance() && _impl_.device_info_ != nullptr;
}
inline bool WaitForAllTasksResponse::has_device_info() const {
  return _internal_has_device_info();
}
inline void WaitForAllTasksResponse::clear_device_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.device_info_ != nullptr) {
    delete _impl_.device_info_;
  }
  _impl_.device_info_ = nullptr;
}
inline const ::tensorflow::DeviceInfo& WaitForAllTasksResponse::_internal_device_info() const {
  const ::tensorflow::DeviceInfo* p = _impl_.device_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceInfo&>(
      ::tensorflow::_DeviceInfo_default_instance_);
}
inline const ::tensorflow::DeviceInfo& WaitForAllTasksResponse::device_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.WaitForAllTasksResponse.device_info)
  return _internal_device_info();
}
inline void WaitForAllTasksResponse::unsafe_arena_set_allocated_device_info(
    ::tensorflow::DeviceInfo* device_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_info_);
  }
  _impl_.device_info_ = device_info;
  if (device_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WaitForAllTasksResponse.device_info)
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksResponse::release_device_info() {
  
  ::tensorflow::DeviceInfo* temp = _impl_.device_info_;
  _impl_.device_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksResponse::unsafe_arena_release_device_info() {
  // @@protoc_insertion_point(field_release:tensorflow.WaitForAllTasksResponse.device_info)
  
  ::tensorflow::DeviceInfo* temp = _impl_.device_info_;
  _impl_.device_info_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksResponse::_internal_mutable_device_info() {
  
  if (_impl_.device_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceInfo>(GetArenaForAllocation());
    _impl_.device_info_ = p;
  }
  return _impl_.device_info_;
}
inline ::tensorflow::DeviceInfo* WaitForAllTasksResponse::mutable_device_info() {
  ::tensorflow::DeviceInfo* _msg = _internal_mutable_device_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.WaitForAllTasksResponse.device_info)
  return _msg;
}
inline void WaitForAllTasksResponse::set_allocated_device_info(::tensorflow::DeviceInfo* device_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.device_info_;
  }
  if (device_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(device_info);
    if (message_arena != submessage_arena) {
      device_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_info_ = device_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WaitForAllTasksResponse.device_info)
}

// -------------------------------------------------------------------

// ShutdownTaskRequest

// .tensorflow.CoordinatedTask source_task = 1;
inline bool ShutdownTaskRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool ShutdownTaskRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void ShutdownTaskRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& ShutdownTaskRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& ShutdownTaskRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.ShutdownTaskRequest.source_task)
  return _internal_source_task();
}
inline void ShutdownTaskRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ShutdownTaskRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* ShutdownTaskRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* ShutdownTaskRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.ShutdownTaskRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* ShutdownTaskRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* ShutdownTaskRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.ShutdownTaskRequest.source_task)
  return _msg;
}
inline void ShutdownTaskRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ShutdownTaskRequest.source_task)
}

// -------------------------------------------------------------------

// ShutdownTaskResponse

// -------------------------------------------------------------------

// ResetTaskRequest

// .tensorflow.CoordinatedTask source_task = 1;
inline bool ResetTaskRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool ResetTaskRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void ResetTaskRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& ResetTaskRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& ResetTaskRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetTaskRequest.source_task)
  return _internal_source_task();
}
inline void ResetTaskRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ResetTaskRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* ResetTaskRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* ResetTaskRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.ResetTaskRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* ResetTaskRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* ResetTaskRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetTaskRequest.source_task)
  return _msg;
}
inline void ResetTaskRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResetTaskRequest.source_task)
}

// -------------------------------------------------------------------

// ResetTaskResponse

// -------------------------------------------------------------------

// ReportErrorToTaskRequest

// int32 error_code = 1;
inline void ReportErrorToTaskRequest::clear_error_code() {
  _impl_.error_code_ = 0;
}
inline int32_t ReportErrorToTaskRequest::_internal_error_code() const {
  return _impl_.error_code_;
}
inline int32_t ReportErrorToTaskRequest::error_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToTaskRequest.error_code)
  return _internal_error_code();
}
inline void ReportErrorToTaskRequest::_internal_set_error_code(int32_t value) {
  
  _impl_.error_code_ = value;
}
inline void ReportErrorToTaskRequest::set_error_code(int32_t value) {
  _internal_set_error_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.ReportErrorToTaskRequest.error_code)
}

// string error_message = 2;
inline void ReportErrorToTaskRequest::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& ReportErrorToTaskRequest::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToTaskRequest.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReportErrorToTaskRequest::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ReportErrorToTaskRequest.error_message)
}
inline std::string* ReportErrorToTaskRequest::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReportErrorToTaskRequest.error_message)
  return _s;
}
inline const std::string& ReportErrorToTaskRequest::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void ReportErrorToTaskRequest::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* ReportErrorToTaskRequest::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* ReportErrorToTaskRequest::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ReportErrorToTaskRequest.error_message)
  return _impl_.error_message_.Release();
}
inline void ReportErrorToTaskRequest::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReportErrorToTaskRequest.error_message)
}

// .tensorflow.CoordinationServiceError error_payload = 5;
inline bool ReportErrorToTaskRequest::_internal_has_error_payload() const {
  return this != internal_default_instance() && _impl_.error_payload_ != nullptr;
}
inline bool ReportErrorToTaskRequest::has_error_payload() const {
  return _internal_has_error_payload();
}
inline void ReportErrorToTaskRequest::clear_error_payload() {
  if (GetArenaForAllocation() == nullptr && _impl_.error_payload_ != nullptr) {
    delete _impl_.error_payload_;
  }
  _impl_.error_payload_ = nullptr;
}
inline const ::tensorflow::CoordinationServiceError& ReportErrorToTaskRequest::_internal_error_payload() const {
  const ::tensorflow::CoordinationServiceError* p = _impl_.error_payload_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinationServiceError&>(
      ::tensorflow::_CoordinationServiceError_default_instance_);
}
inline const ::tensorflow::CoordinationServiceError& ReportErrorToTaskRequest::error_payload() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToTaskRequest.error_payload)
  return _internal_error_payload();
}
inline void ReportErrorToTaskRequest::unsafe_arena_set_allocated_error_payload(
    ::tensorflow::CoordinationServiceError* error_payload) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.error_payload_);
  }
  _impl_.error_payload_ = error_payload;
  if (error_payload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReportErrorToTaskRequest.error_payload)
}
inline ::tensorflow::CoordinationServiceError* ReportErrorToTaskRequest::release_error_payload() {
  
  ::tensorflow::CoordinationServiceError* temp = _impl_.error_payload_;
  _impl_.error_payload_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinationServiceError* ReportErrorToTaskRequest::unsafe_arena_release_error_payload() {
  // @@protoc_insertion_point(field_release:tensorflow.ReportErrorToTaskRequest.error_payload)
  
  ::tensorflow::CoordinationServiceError* temp = _impl_.error_payload_;
  _impl_.error_payload_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinationServiceError* ReportErrorToTaskRequest::_internal_mutable_error_payload() {
  
  if (_impl_.error_payload_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinationServiceError>(GetArenaForAllocation());
    _impl_.error_payload_ = p;
  }
  return _impl_.error_payload_;
}
inline ::tensorflow::CoordinationServiceError* ReportErrorToTaskRequest::mutable_error_payload() {
  ::tensorflow::CoordinationServiceError* _msg = _internal_mutable_error_payload();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReportErrorToTaskRequest.error_payload)
  return _msg;
}
inline void ReportErrorToTaskRequest::set_allocated_error_payload(::tensorflow::CoordinationServiceError* error_payload) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.error_payload_;
  }
  if (error_payload) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(error_payload);
    if (message_arena != submessage_arena) {
      error_payload = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, error_payload, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.error_payload_ = error_payload;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReportErrorToTaskRequest.error_payload)
}

// -------------------------------------------------------------------

// ReportErrorToTaskResponse

// -------------------------------------------------------------------

// ReportErrorToServiceRequest

// int32 error_code = 1;
inline void ReportErrorToServiceRequest::clear_error_code() {
  _impl_.error_code_ = 0;
}
inline int32_t ReportErrorToServiceRequest::_internal_error_code() const {
  return _impl_.error_code_;
}
inline int32_t ReportErrorToServiceRequest::error_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToServiceRequest.error_code)
  return _internal_error_code();
}
inline void ReportErrorToServiceRequest::_internal_set_error_code(int32_t value) {
  
  _impl_.error_code_ = value;
}
inline void ReportErrorToServiceRequest::set_error_code(int32_t value) {
  _internal_set_error_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.ReportErrorToServiceRequest.error_code)
}

// string error_message = 2;
inline void ReportErrorToServiceRequest::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& ReportErrorToServiceRequest::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToServiceRequest.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReportErrorToServiceRequest::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ReportErrorToServiceRequest.error_message)
}
inline std::string* ReportErrorToServiceRequest::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReportErrorToServiceRequest.error_message)
  return _s;
}
inline const std::string& ReportErrorToServiceRequest::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void ReportErrorToServiceRequest::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* ReportErrorToServiceRequest::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* ReportErrorToServiceRequest::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ReportErrorToServiceRequest.error_message)
  return _impl_.error_message_.Release();
}
inline void ReportErrorToServiceRequest::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReportErrorToServiceRequest.error_message)
}

// .tensorflow.CoordinatedTask error_origin = 5;
inline bool ReportErrorToServiceRequest::_internal_has_error_origin() const {
  return this != internal_default_instance() && _impl_.error_origin_ != nullptr;
}
inline bool ReportErrorToServiceRequest::has_error_origin() const {
  return _internal_has_error_origin();
}
inline void ReportErrorToServiceRequest::clear_error_origin() {
  if (GetArenaForAllocation() == nullptr && _impl_.error_origin_ != nullptr) {
    delete _impl_.error_origin_;
  }
  _impl_.error_origin_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& ReportErrorToServiceRequest::_internal_error_origin() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.error_origin_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& ReportErrorToServiceRequest::error_origin() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReportErrorToServiceRequest.error_origin)
  return _internal_error_origin();
}
inline void ReportErrorToServiceRequest::unsafe_arena_set_allocated_error_origin(
    ::tensorflow::CoordinatedTask* error_origin) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.error_origin_);
  }
  _impl_.error_origin_ = error_origin;
  if (error_origin) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReportErrorToServiceRequest.error_origin)
}
inline ::tensorflow::CoordinatedTask* ReportErrorToServiceRequest::release_error_origin() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.error_origin_;
  _impl_.error_origin_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* ReportErrorToServiceRequest::unsafe_arena_release_error_origin() {
  // @@protoc_insertion_point(field_release:tensorflow.ReportErrorToServiceRequest.error_origin)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.error_origin_;
  _impl_.error_origin_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* ReportErrorToServiceRequest::_internal_mutable_error_origin() {
  
  if (_impl_.error_origin_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.error_origin_ = p;
  }
  return _impl_.error_origin_;
}
inline ::tensorflow::CoordinatedTask* ReportErrorToServiceRequest::mutable_error_origin() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_error_origin();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReportErrorToServiceRequest.error_origin)
  return _msg;
}
inline void ReportErrorToServiceRequest::set_allocated_error_origin(::tensorflow::CoordinatedTask* error_origin) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.error_origin_;
  }
  if (error_origin) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(error_origin);
    if (message_arena != submessage_arena) {
      error_origin = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, error_origin, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.error_origin_ = error_origin;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReportErrorToServiceRequest.error_origin)
}

// -------------------------------------------------------------------

// ReportErrorToServiceResponse

// -------------------------------------------------------------------

// GetTaskStateRequest

// repeated .tensorflow.CoordinatedTask source_task = 1;
inline int GetTaskStateRequest::_internal_source_task_size() const {
  return _impl_.source_task_.size();
}
inline int GetTaskStateRequest::source_task_size() const {
  return _internal_source_task_size();
}
inline void GetTaskStateRequest::clear_source_task() {
  _impl_.source_task_.Clear();
}
inline ::tensorflow::CoordinatedTask* GetTaskStateRequest::mutable_source_task(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetTaskStateRequest.source_task)
  return _impl_.source_task_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
GetTaskStateRequest::mutable_source_task() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetTaskStateRequest.source_task)
  return &_impl_.source_task_;
}
inline const ::tensorflow::CoordinatedTask& GetTaskStateRequest::_internal_source_task(int index) const {
  return _impl_.source_task_.Get(index);
}
inline const ::tensorflow::CoordinatedTask& GetTaskStateRequest::source_task(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetTaskStateRequest.source_task)
  return _internal_source_task(index);
}
inline ::tensorflow::CoordinatedTask* GetTaskStateRequest::_internal_add_source_task() {
  return _impl_.source_task_.Add();
}
inline ::tensorflow::CoordinatedTask* GetTaskStateRequest::add_source_task() {
  ::tensorflow::CoordinatedTask* _add = _internal_add_source_task();
  // @@protoc_insertion_point(field_add:tensorflow.GetTaskStateRequest.source_task)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
GetTaskStateRequest::source_task() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetTaskStateRequest.source_task)
  return _impl_.source_task_;
}

// -------------------------------------------------------------------

// GetTaskStateResponse

// repeated .tensorflow.CoordinatedTaskStateInfo task_state = 1;
inline int GetTaskStateResponse::_internal_task_state_size() const {
  return _impl_.task_state_.size();
}
inline int GetTaskStateResponse::task_state_size() const {
  return _internal_task_state_size();
}
inline void GetTaskStateResponse::clear_task_state() {
  _impl_.task_state_.Clear();
}
inline ::tensorflow::CoordinatedTaskStateInfo* GetTaskStateResponse::mutable_task_state(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetTaskStateResponse.task_state)
  return _impl_.task_state_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTaskStateInfo >*
GetTaskStateResponse::mutable_task_state() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetTaskStateResponse.task_state)
  return &_impl_.task_state_;
}
inline const ::tensorflow::CoordinatedTaskStateInfo& GetTaskStateResponse::_internal_task_state(int index) const {
  return _impl_.task_state_.Get(index);
}
inline const ::tensorflow::CoordinatedTaskStateInfo& GetTaskStateResponse::task_state(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetTaskStateResponse.task_state)
  return _internal_task_state(index);
}
inline ::tensorflow::CoordinatedTaskStateInfo* GetTaskStateResponse::_internal_add_task_state() {
  return _impl_.task_state_.Add();
}
inline ::tensorflow::CoordinatedTaskStateInfo* GetTaskStateResponse::add_task_state() {
  ::tensorflow::CoordinatedTaskStateInfo* _add = _internal_add_task_state();
  // @@protoc_insertion_point(field_add:tensorflow.GetTaskStateResponse.task_state)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTaskStateInfo >&
GetTaskStateResponse::task_state() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetTaskStateResponse.task_state)
  return _impl_.task_state_;
}

// -------------------------------------------------------------------

// KeyValueEntry

// string key = 1;
inline void KeyValueEntry::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& KeyValueEntry::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.KeyValueEntry.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KeyValueEntry::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KeyValueEntry.key)
}
inline std::string* KeyValueEntry::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.KeyValueEntry.key)
  return _s;
}
inline const std::string& KeyValueEntry::_internal_key() const {
  return _impl_.key_.Get();
}
inline void KeyValueEntry::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* KeyValueEntry::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* KeyValueEntry::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.KeyValueEntry.key)
  return _impl_.key_.Release();
}
inline void KeyValueEntry::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KeyValueEntry.key)
}

// bytes value = 2;
inline void KeyValueEntry::clear_value() {
  _impl_.value_.ClearToEmpty();
}
inline const std::string& KeyValueEntry::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.KeyValueEntry.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KeyValueEntry::set_value(ArgT0&& arg0, ArgT... args) {
 
 _impl_.value_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KeyValueEntry.value)
}
inline std::string* KeyValueEntry::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.KeyValueEntry.value)
  return _s;
}
inline const std::string& KeyValueEntry::_internal_value() const {
  return _impl_.value_.Get();
}
inline void KeyValueEntry::_internal_set_value(const std::string& value) {
  
  _impl_.value_.Set(value, GetArenaForAllocation());
}
inline std::string* KeyValueEntry::_internal_mutable_value() {
  
  return _impl_.value_.Mutable(GetArenaForAllocation());
}
inline std::string* KeyValueEntry::release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.KeyValueEntry.value)
  return _impl_.value_.Release();
}
inline void KeyValueEntry::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  _impl_.value_.SetAllocated(value, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.value_.IsDefault()) {
    _impl_.value_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KeyValueEntry.value)
}

// -------------------------------------------------------------------

// InsertKeyValueRequest

// .tensorflow.KeyValueEntry kv = 1;
inline bool InsertKeyValueRequest::_internal_has_kv() const {
  return this != internal_default_instance() && _impl_.kv_ != nullptr;
}
inline bool InsertKeyValueRequest::has_kv() const {
  return _internal_has_kv();
}
inline void InsertKeyValueRequest::clear_kv() {
  if (GetArenaForAllocation() == nullptr && _impl_.kv_ != nullptr) {
    delete _impl_.kv_;
  }
  _impl_.kv_ = nullptr;
}
inline const ::tensorflow::KeyValueEntry& InsertKeyValueRequest::_internal_kv() const {
  const ::tensorflow::KeyValueEntry* p = _impl_.kv_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::KeyValueEntry&>(
      ::tensorflow::_KeyValueEntry_default_instance_);
}
inline const ::tensorflow::KeyValueEntry& InsertKeyValueRequest::kv() const {
  // @@protoc_insertion_point(field_get:tensorflow.InsertKeyValueRequest.kv)
  return _internal_kv();
}
inline void InsertKeyValueRequest::unsafe_arena_set_allocated_kv(
    ::tensorflow::KeyValueEntry* kv) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.kv_);
  }
  _impl_.kv_ = kv;
  if (kv) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.InsertKeyValueRequest.kv)
}
inline ::tensorflow::KeyValueEntry* InsertKeyValueRequest::release_kv() {
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::KeyValueEntry* InsertKeyValueRequest::unsafe_arena_release_kv() {
  // @@protoc_insertion_point(field_release:tensorflow.InsertKeyValueRequest.kv)
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
  return temp;
}
inline ::tensorflow::KeyValueEntry* InsertKeyValueRequest::_internal_mutable_kv() {
  
  if (_impl_.kv_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::KeyValueEntry>(GetArenaForAllocation());
    _impl_.kv_ = p;
  }
  return _impl_.kv_;
}
inline ::tensorflow::KeyValueEntry* InsertKeyValueRequest::mutable_kv() {
  ::tensorflow::KeyValueEntry* _msg = _internal_mutable_kv();
  // @@protoc_insertion_point(field_mutable:tensorflow.InsertKeyValueRequest.kv)
  return _msg;
}
inline void InsertKeyValueRequest::set_allocated_kv(::tensorflow::KeyValueEntry* kv) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.kv_;
  }
  if (kv) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(kv);
    if (message_arena != submessage_arena) {
      kv = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, kv, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.kv_ = kv;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.InsertKeyValueRequest.kv)
}

// bool allow_overwrite = 2;
inline void InsertKeyValueRequest::clear_allow_overwrite() {
  _impl_.allow_overwrite_ = false;
}
inline bool InsertKeyValueRequest::_internal_allow_overwrite() const {
  return _impl_.allow_overwrite_;
}
inline bool InsertKeyValueRequest::allow_overwrite() const {
  // @@protoc_insertion_point(field_get:tensorflow.InsertKeyValueRequest.allow_overwrite)
  return _internal_allow_overwrite();
}
inline void InsertKeyValueRequest::_internal_set_allow_overwrite(bool value) {
  
  _impl_.allow_overwrite_ = value;
}
inline void InsertKeyValueRequest::set_allow_overwrite(bool value) {
  _internal_set_allow_overwrite(value);
  // @@protoc_insertion_point(field_set:tensorflow.InsertKeyValueRequest.allow_overwrite)
}

// -------------------------------------------------------------------

// InsertKeyValueResponse

// -------------------------------------------------------------------

// GetKeyValueRequest

// string key = 1;
inline void GetKeyValueRequest::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& GetKeyValueRequest::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.GetKeyValueRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetKeyValueRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GetKeyValueRequest.key)
}
inline std::string* GetKeyValueRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.GetKeyValueRequest.key)
  return _s;
}
inline const std::string& GetKeyValueRequest::_internal_key() const {
  return _impl_.key_.Get();
}
inline void GetKeyValueRequest::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* GetKeyValueRequest::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* GetKeyValueRequest::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.GetKeyValueRequest.key)
  return _impl_.key_.Release();
}
inline void GetKeyValueRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GetKeyValueRequest.key)
}

// -------------------------------------------------------------------

// GetKeyValueResponse

// .tensorflow.KeyValueEntry kv = 1;
inline bool GetKeyValueResponse::_internal_has_kv() const {
  return this != internal_default_instance() && _impl_.kv_ != nullptr;
}
inline bool GetKeyValueResponse::has_kv() const {
  return _internal_has_kv();
}
inline void GetKeyValueResponse::clear_kv() {
  if (GetArenaForAllocation() == nullptr && _impl_.kv_ != nullptr) {
    delete _impl_.kv_;
  }
  _impl_.kv_ = nullptr;
}
inline const ::tensorflow::KeyValueEntry& GetKeyValueResponse::_internal_kv() const {
  const ::tensorflow::KeyValueEntry* p = _impl_.kv_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::KeyValueEntry&>(
      ::tensorflow::_KeyValueEntry_default_instance_);
}
inline const ::tensorflow::KeyValueEntry& GetKeyValueResponse::kv() const {
  // @@protoc_insertion_point(field_get:tensorflow.GetKeyValueResponse.kv)
  return _internal_kv();
}
inline void GetKeyValueResponse::unsafe_arena_set_allocated_kv(
    ::tensorflow::KeyValueEntry* kv) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.kv_);
  }
  _impl_.kv_ = kv;
  if (kv) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GetKeyValueResponse.kv)
}
inline ::tensorflow::KeyValueEntry* GetKeyValueResponse::release_kv() {
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::KeyValueEntry* GetKeyValueResponse::unsafe_arena_release_kv() {
  // @@protoc_insertion_point(field_release:tensorflow.GetKeyValueResponse.kv)
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
  return temp;
}
inline ::tensorflow::KeyValueEntry* GetKeyValueResponse::_internal_mutable_kv() {
  
  if (_impl_.kv_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::KeyValueEntry>(GetArenaForAllocation());
    _impl_.kv_ = p;
  }
  return _impl_.kv_;
}
inline ::tensorflow::KeyValueEntry* GetKeyValueResponse::mutable_kv() {
  ::tensorflow::KeyValueEntry* _msg = _internal_mutable_kv();
  // @@protoc_insertion_point(field_mutable:tensorflow.GetKeyValueResponse.kv)
  return _msg;
}
inline void GetKeyValueResponse::set_allocated_kv(::tensorflow::KeyValueEntry* kv) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.kv_;
  }
  if (kv) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(kv);
    if (message_arena != submessage_arena) {
      kv = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, kv, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.kv_ = kv;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GetKeyValueResponse.kv)
}

// -------------------------------------------------------------------

// TryGetKeyValueRequest

// string key = 1;
inline void TryGetKeyValueRequest::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& TryGetKeyValueRequest::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.TryGetKeyValueRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TryGetKeyValueRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TryGetKeyValueRequest.key)
}
inline std::string* TryGetKeyValueRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.TryGetKeyValueRequest.key)
  return _s;
}
inline const std::string& TryGetKeyValueRequest::_internal_key() const {
  return _impl_.key_.Get();
}
inline void TryGetKeyValueRequest::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* TryGetKeyValueRequest::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* TryGetKeyValueRequest::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.TryGetKeyValueRequest.key)
  return _impl_.key_.Release();
}
inline void TryGetKeyValueRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TryGetKeyValueRequest.key)
}

// -------------------------------------------------------------------

// TryGetKeyValueResponse

// .tensorflow.KeyValueEntry kv = 1;
inline bool TryGetKeyValueResponse::_internal_has_kv() const {
  return this != internal_default_instance() && _impl_.kv_ != nullptr;
}
inline bool TryGetKeyValueResponse::has_kv() const {
  return _internal_has_kv();
}
inline void TryGetKeyValueResponse::clear_kv() {
  if (GetArenaForAllocation() == nullptr && _impl_.kv_ != nullptr) {
    delete _impl_.kv_;
  }
  _impl_.kv_ = nullptr;
}
inline const ::tensorflow::KeyValueEntry& TryGetKeyValueResponse::_internal_kv() const {
  const ::tensorflow::KeyValueEntry* p = _impl_.kv_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::KeyValueEntry&>(
      ::tensorflow::_KeyValueEntry_default_instance_);
}
inline const ::tensorflow::KeyValueEntry& TryGetKeyValueResponse::kv() const {
  // @@protoc_insertion_point(field_get:tensorflow.TryGetKeyValueResponse.kv)
  return _internal_kv();
}
inline void TryGetKeyValueResponse::unsafe_arena_set_allocated_kv(
    ::tensorflow::KeyValueEntry* kv) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.kv_);
  }
  _impl_.kv_ = kv;
  if (kv) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TryGetKeyValueResponse.kv)
}
inline ::tensorflow::KeyValueEntry* TryGetKeyValueResponse::release_kv() {
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::KeyValueEntry* TryGetKeyValueResponse::unsafe_arena_release_kv() {
  // @@protoc_insertion_point(field_release:tensorflow.TryGetKeyValueResponse.kv)
  
  ::tensorflow::KeyValueEntry* temp = _impl_.kv_;
  _impl_.kv_ = nullptr;
  return temp;
}
inline ::tensorflow::KeyValueEntry* TryGetKeyValueResponse::_internal_mutable_kv() {
  
  if (_impl_.kv_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::KeyValueEntry>(GetArenaForAllocation());
    _impl_.kv_ = p;
  }
  return _impl_.kv_;
}
inline ::tensorflow::KeyValueEntry* TryGetKeyValueResponse::mutable_kv() {
  ::tensorflow::KeyValueEntry* _msg = _internal_mutable_kv();
  // @@protoc_insertion_point(field_mutable:tensorflow.TryGetKeyValueResponse.kv)
  return _msg;
}
inline void TryGetKeyValueResponse::set_allocated_kv(::tensorflow::KeyValueEntry* kv) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.kv_;
  }
  if (kv) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(kv);
    if (message_arena != submessage_arena) {
      kv = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, kv, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.kv_ = kv;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TryGetKeyValueResponse.kv)
}

// -------------------------------------------------------------------

// GetKeyValueDirRequest

// string directory_key = 1;
inline void GetKeyValueDirRequest::clear_directory_key() {
  _impl_.directory_key_.ClearToEmpty();
}
inline const std::string& GetKeyValueDirRequest::directory_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.GetKeyValueDirRequest.directory_key)
  return _internal_directory_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetKeyValueDirRequest::set_directory_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.directory_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GetKeyValueDirRequest.directory_key)
}
inline std::string* GetKeyValueDirRequest::mutable_directory_key() {
  std::string* _s = _internal_mutable_directory_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.GetKeyValueDirRequest.directory_key)
  return _s;
}
inline const std::string& GetKeyValueDirRequest::_internal_directory_key() const {
  return _impl_.directory_key_.Get();
}
inline void GetKeyValueDirRequest::_internal_set_directory_key(const std::string& value) {
  
  _impl_.directory_key_.Set(value, GetArenaForAllocation());
}
inline std::string* GetKeyValueDirRequest::_internal_mutable_directory_key() {
  
  return _impl_.directory_key_.Mutable(GetArenaForAllocation());
}
inline std::string* GetKeyValueDirRequest::release_directory_key() {
  // @@protoc_insertion_point(field_release:tensorflow.GetKeyValueDirRequest.directory_key)
  return _impl_.directory_key_.Release();
}
inline void GetKeyValueDirRequest::set_allocated_directory_key(std::string* directory_key) {
  if (directory_key != nullptr) {
    
  } else {
    
  }
  _impl_.directory_key_.SetAllocated(directory_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.directory_key_.IsDefault()) {
    _impl_.directory_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GetKeyValueDirRequest.directory_key)
}

// -------------------------------------------------------------------

// GetKeyValueDirResponse

// string directory_key = 1;
inline void GetKeyValueDirResponse::clear_directory_key() {
  _impl_.directory_key_.ClearToEmpty();
}
inline const std::string& GetKeyValueDirResponse::directory_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.GetKeyValueDirResponse.directory_key)
  return _internal_directory_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetKeyValueDirResponse::set_directory_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.directory_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GetKeyValueDirResponse.directory_key)
}
inline std::string* GetKeyValueDirResponse::mutable_directory_key() {
  std::string* _s = _internal_mutable_directory_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.GetKeyValueDirResponse.directory_key)
  return _s;
}
inline const std::string& GetKeyValueDirResponse::_internal_directory_key() const {
  return _impl_.directory_key_.Get();
}
inline void GetKeyValueDirResponse::_internal_set_directory_key(const std::string& value) {
  
  _impl_.directory_key_.Set(value, GetArenaForAllocation());
}
inline std::string* GetKeyValueDirResponse::_internal_mutable_directory_key() {
  
  return _impl_.directory_key_.Mutable(GetArenaForAllocation());
}
inline std::string* GetKeyValueDirResponse::release_directory_key() {
  // @@protoc_insertion_point(field_release:tensorflow.GetKeyValueDirResponse.directory_key)
  return _impl_.directory_key_.Release();
}
inline void GetKeyValueDirResponse::set_allocated_directory_key(std::string* directory_key) {
  if (directory_key != nullptr) {
    
  } else {
    
  }
  _impl_.directory_key_.SetAllocated(directory_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.directory_key_.IsDefault()) {
    _impl_.directory_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GetKeyValueDirResponse.directory_key)
}

// repeated .tensorflow.KeyValueEntry kv = 2;
inline int GetKeyValueDirResponse::_internal_kv_size() const {
  return _impl_.kv_.size();
}
inline int GetKeyValueDirResponse::kv_size() const {
  return _internal_kv_size();
}
inline void GetKeyValueDirResponse::clear_kv() {
  _impl_.kv_.Clear();
}
inline ::tensorflow::KeyValueEntry* GetKeyValueDirResponse::mutable_kv(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetKeyValueDirResponse.kv)
  return _impl_.kv_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KeyValueEntry >*
GetKeyValueDirResponse::mutable_kv() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetKeyValueDirResponse.kv)
  return &_impl_.kv_;
}
inline const ::tensorflow::KeyValueEntry& GetKeyValueDirResponse::_internal_kv(int index) const {
  return _impl_.kv_.Get(index);
}
inline const ::tensorflow::KeyValueEntry& GetKeyValueDirResponse::kv(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetKeyValueDirResponse.kv)
  return _internal_kv(index);
}
inline ::tensorflow::KeyValueEntry* GetKeyValueDirResponse::_internal_add_kv() {
  return _impl_.kv_.Add();
}
inline ::tensorflow::KeyValueEntry* GetKeyValueDirResponse::add_kv() {
  ::tensorflow::KeyValueEntry* _add = _internal_add_kv();
  // @@protoc_insertion_point(field_add:tensorflow.GetKeyValueDirResponse.kv)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KeyValueEntry >&
GetKeyValueDirResponse::kv() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetKeyValueDirResponse.kv)
  return _impl_.kv_;
}

// -------------------------------------------------------------------

// DeleteKeyValueRequest

// string key = 1;
inline void DeleteKeyValueRequest::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& DeleteKeyValueRequest::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeleteKeyValueRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteKeyValueRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeleteKeyValueRequest.key)
}
inline std::string* DeleteKeyValueRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeleteKeyValueRequest.key)
  return _s;
}
inline const std::string& DeleteKeyValueRequest::_internal_key() const {
  return _impl_.key_.Get();
}
inline void DeleteKeyValueRequest::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* DeleteKeyValueRequest::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* DeleteKeyValueRequest::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.DeleteKeyValueRequest.key)
  return _impl_.key_.Release();
}
inline void DeleteKeyValueRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeleteKeyValueRequest.key)
}

// bool is_directory = 2;
inline void DeleteKeyValueRequest::clear_is_directory() {
  _impl_.is_directory_ = false;
}
inline bool DeleteKeyValueRequest::_internal_is_directory() const {
  return _impl_.is_directory_;
}
inline bool DeleteKeyValueRequest::is_directory() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeleteKeyValueRequest.is_directory)
  return _internal_is_directory();
}
inline void DeleteKeyValueRequest::_internal_set_is_directory(bool value) {
  
  _impl_.is_directory_ = value;
}
inline void DeleteKeyValueRequest::set_is_directory(bool value) {
  _internal_set_is_directory(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeleteKeyValueRequest.is_directory)
}

// -------------------------------------------------------------------

// DeleteKeyValueResponse

// -------------------------------------------------------------------

// BarrierRequest

// string barrier_id = 1;
inline void BarrierRequest::clear_barrier_id() {
  _impl_.barrier_id_.ClearToEmpty();
}
inline const std::string& BarrierRequest::barrier_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierRequest.barrier_id)
  return _internal_barrier_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BarrierRequest::set_barrier_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.barrier_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.BarrierRequest.barrier_id)
}
inline std::string* BarrierRequest::mutable_barrier_id() {
  std::string* _s = _internal_mutable_barrier_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.BarrierRequest.barrier_id)
  return _s;
}
inline const std::string& BarrierRequest::_internal_barrier_id() const {
  return _impl_.barrier_id_.Get();
}
inline void BarrierRequest::_internal_set_barrier_id(const std::string& value) {
  
  _impl_.barrier_id_.Set(value, GetArenaForAllocation());
}
inline std::string* BarrierRequest::_internal_mutable_barrier_id() {
  
  return _impl_.barrier_id_.Mutable(GetArenaForAllocation());
}
inline std::string* BarrierRequest::release_barrier_id() {
  // @@protoc_insertion_point(field_release:tensorflow.BarrierRequest.barrier_id)
  return _impl_.barrier_id_.Release();
}
inline void BarrierRequest::set_allocated_barrier_id(std::string* barrier_id) {
  if (barrier_id != nullptr) {
    
  } else {
    
  }
  _impl_.barrier_id_.SetAllocated(barrier_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.barrier_id_.IsDefault()) {
    _impl_.barrier_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BarrierRequest.barrier_id)
}

// int64 barrier_timeout_in_ms = 2;
inline void BarrierRequest::clear_barrier_timeout_in_ms() {
  _impl_.barrier_timeout_in_ms_ = int64_t{0};
}
inline int64_t BarrierRequest::_internal_barrier_timeout_in_ms() const {
  return _impl_.barrier_timeout_in_ms_;
}
inline int64_t BarrierRequest::barrier_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierRequest.barrier_timeout_in_ms)
  return _internal_barrier_timeout_in_ms();
}
inline void BarrierRequest::_internal_set_barrier_timeout_in_ms(int64_t value) {
  
  _impl_.barrier_timeout_in_ms_ = value;
}
inline void BarrierRequest::set_barrier_timeout_in_ms(int64_t value) {
  _internal_set_barrier_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.BarrierRequest.barrier_timeout_in_ms)
}

// repeated .tensorflow.CoordinatedTask tasks = 3;
inline int BarrierRequest::_internal_tasks_size() const {
  return _impl_.tasks_.size();
}
inline int BarrierRequest::tasks_size() const {
  return _internal_tasks_size();
}
inline void BarrierRequest::clear_tasks() {
  _impl_.tasks_.Clear();
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::mutable_tasks(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BarrierRequest.tasks)
  return _impl_.tasks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
BarrierRequest::mutable_tasks() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BarrierRequest.tasks)
  return &_impl_.tasks_;
}
inline const ::tensorflow::CoordinatedTask& BarrierRequest::_internal_tasks(int index) const {
  return _impl_.tasks_.Get(index);
}
inline const ::tensorflow::CoordinatedTask& BarrierRequest::tasks(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierRequest.tasks)
  return _internal_tasks(index);
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::_internal_add_tasks() {
  return _impl_.tasks_.Add();
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::add_tasks() {
  ::tensorflow::CoordinatedTask* _add = _internal_add_tasks();
  // @@protoc_insertion_point(field_add:tensorflow.BarrierRequest.tasks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
BarrierRequest::tasks() const {
  // @@protoc_insertion_point(field_list:tensorflow.BarrierRequest.tasks)
  return _impl_.tasks_;
}

// .tensorflow.CoordinatedTask source_task = 4;
inline bool BarrierRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool BarrierRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void BarrierRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& BarrierRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& BarrierRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierRequest.source_task)
  return _internal_source_task();
}
inline void BarrierRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BarrierRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.BarrierRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* BarrierRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.BarrierRequest.source_task)
  return _msg;
}
inline void BarrierRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BarrierRequest.source_task)
}

// int64 counter = 5;
inline void BarrierRequest::clear_counter() {
  _impl_.counter_ = int64_t{0};
}
inline int64_t BarrierRequest::_internal_counter() const {
  return _impl_.counter_;
}
inline int64_t BarrierRequest::counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierRequest.counter)
  return _internal_counter();
}
inline void BarrierRequest::_internal_set_counter(int64_t value) {
  
  _impl_.counter_ = value;
}
inline void BarrierRequest::set_counter(int64_t value) {
  _internal_set_counter(value);
  // @@protoc_insertion_point(field_set:tensorflow.BarrierRequest.counter)
}

// -------------------------------------------------------------------

// BarrierResponse

// int64 counter = 1;
inline void BarrierResponse::clear_counter() {
  _impl_.counter_ = int64_t{0};
}
inline int64_t BarrierResponse::_internal_counter() const {
  return _impl_.counter_;
}
inline int64_t BarrierResponse::counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.BarrierResponse.counter)
  return _internal_counter();
}
inline void BarrierResponse::_internal_set_counter(int64_t value) {
  
  _impl_.counter_ = value;
}
inline void BarrierResponse::set_counter(int64_t value) {
  _internal_set_counter(value);
  // @@protoc_insertion_point(field_set:tensorflow.BarrierResponse.counter)
}

// -------------------------------------------------------------------

// GetAliveTasksRequest

// .tensorflow.CoordinatedTask requesting_task = 1;
inline bool GetAliveTasksRequest::_internal_has_requesting_task() const {
  return this != internal_default_instance() && _impl_.requesting_task_ != nullptr;
}
inline bool GetAliveTasksRequest::has_requesting_task() const {
  return _internal_has_requesting_task();
}
inline void GetAliveTasksRequest::clear_requesting_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.requesting_task_ != nullptr) {
    delete _impl_.requesting_task_;
  }
  _impl_.requesting_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksRequest::_internal_requesting_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.requesting_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksRequest::requesting_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.GetAliveTasksRequest.requesting_task)
  return _internal_requesting_task();
}
inline void GetAliveTasksRequest::unsafe_arena_set_allocated_requesting_task(
    ::tensorflow::CoordinatedTask* requesting_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.requesting_task_);
  }
  _impl_.requesting_task_ = requesting_task;
  if (requesting_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GetAliveTasksRequest.requesting_task)
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::release_requesting_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.requesting_task_;
  _impl_.requesting_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::unsafe_arena_release_requesting_task() {
  // @@protoc_insertion_point(field_release:tensorflow.GetAliveTasksRequest.requesting_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.requesting_task_;
  _impl_.requesting_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::_internal_mutable_requesting_task() {
  
  if (_impl_.requesting_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.requesting_task_ = p;
  }
  return _impl_.requesting_task_;
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::mutable_requesting_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_requesting_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.GetAliveTasksRequest.requesting_task)
  return _msg;
}
inline void GetAliveTasksRequest::set_allocated_requesting_task(::tensorflow::CoordinatedTask* requesting_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.requesting_task_;
  }
  if (requesting_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(requesting_task);
    if (message_arena != submessage_arena) {
      requesting_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, requesting_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.requesting_task_ = requesting_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GetAliveTasksRequest.requesting_task)
}

// repeated .tensorflow.CoordinatedTask tasks = 2;
inline int GetAliveTasksRequest::_internal_tasks_size() const {
  return _impl_.tasks_.size();
}
inline int GetAliveTasksRequest::tasks_size() const {
  return _internal_tasks_size();
}
inline void GetAliveTasksRequest::clear_tasks() {
  _impl_.tasks_.Clear();
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::mutable_tasks(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetAliveTasksRequest.tasks)
  return _impl_.tasks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
GetAliveTasksRequest::mutable_tasks() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetAliveTasksRequest.tasks)
  return &_impl_.tasks_;
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksRequest::_internal_tasks(int index) const {
  return _impl_.tasks_.Get(index);
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksRequest::tasks(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetAliveTasksRequest.tasks)
  return _internal_tasks(index);
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::_internal_add_tasks() {
  return _impl_.tasks_.Add();
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksRequest::add_tasks() {
  ::tensorflow::CoordinatedTask* _add = _internal_add_tasks();
  // @@protoc_insertion_point(field_add:tensorflow.GetAliveTasksRequest.tasks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
GetAliveTasksRequest::tasks() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetAliveTasksRequest.tasks)
  return _impl_.tasks_;
}

// -------------------------------------------------------------------

// GetAliveTasksResponse

// repeated .tensorflow.CoordinatedTask alive_tasks = 1;
inline int GetAliveTasksResponse::_internal_alive_tasks_size() const {
  return _impl_.alive_tasks_.size();
}
inline int GetAliveTasksResponse::alive_tasks_size() const {
  return _internal_alive_tasks_size();
}
inline void GetAliveTasksResponse::clear_alive_tasks() {
  _impl_.alive_tasks_.Clear();
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksResponse::mutable_alive_tasks(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetAliveTasksResponse.alive_tasks)
  return _impl_.alive_tasks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >*
GetAliveTasksResponse::mutable_alive_tasks() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetAliveTasksResponse.alive_tasks)
  return &_impl_.alive_tasks_;
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksResponse::_internal_alive_tasks(int index) const {
  return _impl_.alive_tasks_.Get(index);
}
inline const ::tensorflow::CoordinatedTask& GetAliveTasksResponse::alive_tasks(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetAliveTasksResponse.alive_tasks)
  return _internal_alive_tasks(index);
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksResponse::_internal_add_alive_tasks() {
  return _impl_.alive_tasks_.Add();
}
inline ::tensorflow::CoordinatedTask* GetAliveTasksResponse::add_alive_tasks() {
  ::tensorflow::CoordinatedTask* _add = _internal_add_alive_tasks();
  // @@protoc_insertion_point(field_add:tensorflow.GetAliveTasksResponse.alive_tasks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CoordinatedTask >&
GetAliveTasksResponse::alive_tasks() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetAliveTasksResponse.alive_tasks)
  return _impl_.alive_tasks_;
}

// -------------------------------------------------------------------

// CancelBarrierRequest

// string barrier_id = 1;
inline void CancelBarrierRequest::clear_barrier_id() {
  _impl_.barrier_id_.ClearToEmpty();
}
inline const std::string& CancelBarrierRequest::barrier_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CancelBarrierRequest.barrier_id)
  return _internal_barrier_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CancelBarrierRequest::set_barrier_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.barrier_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CancelBarrierRequest.barrier_id)
}
inline std::string* CancelBarrierRequest::mutable_barrier_id() {
  std::string* _s = _internal_mutable_barrier_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.CancelBarrierRequest.barrier_id)
  return _s;
}
inline const std::string& CancelBarrierRequest::_internal_barrier_id() const {
  return _impl_.barrier_id_.Get();
}
inline void CancelBarrierRequest::_internal_set_barrier_id(const std::string& value) {
  
  _impl_.barrier_id_.Set(value, GetArenaForAllocation());
}
inline std::string* CancelBarrierRequest::_internal_mutable_barrier_id() {
  
  return _impl_.barrier_id_.Mutable(GetArenaForAllocation());
}
inline std::string* CancelBarrierRequest::release_barrier_id() {
  // @@protoc_insertion_point(field_release:tensorflow.CancelBarrierRequest.barrier_id)
  return _impl_.barrier_id_.Release();
}
inline void CancelBarrierRequest::set_allocated_barrier_id(std::string* barrier_id) {
  if (barrier_id != nullptr) {
    
  } else {
    
  }
  _impl_.barrier_id_.SetAllocated(barrier_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.barrier_id_.IsDefault()) {
    _impl_.barrier_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CancelBarrierRequest.barrier_id)
}

// int64 counter = 3;
inline void CancelBarrierRequest::clear_counter() {
  _impl_.counter_ = int64_t{0};
}
inline int64_t CancelBarrierRequest::_internal_counter() const {
  return _impl_.counter_;
}
inline int64_t CancelBarrierRequest::counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.CancelBarrierRequest.counter)
  return _internal_counter();
}
inline void CancelBarrierRequest::_internal_set_counter(int64_t value) {
  
  _impl_.counter_ = value;
}
inline void CancelBarrierRequest::set_counter(int64_t value) {
  _internal_set_counter(value);
  // @@protoc_insertion_point(field_set:tensorflow.CancelBarrierRequest.counter)
}

// .tensorflow.CoordinatedTask source_task = 2;
inline bool CancelBarrierRequest::_internal_has_source_task() const {
  return this != internal_default_instance() && _impl_.source_task_ != nullptr;
}
inline bool CancelBarrierRequest::has_source_task() const {
  return _internal_has_source_task();
}
inline void CancelBarrierRequest::clear_source_task() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_task_ != nullptr) {
    delete _impl_.source_task_;
  }
  _impl_.source_task_ = nullptr;
}
inline const ::tensorflow::CoordinatedTask& CancelBarrierRequest::_internal_source_task() const {
  const ::tensorflow::CoordinatedTask* p = _impl_.source_task_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinatedTask&>(
      ::tensorflow::_CoordinatedTask_default_instance_);
}
inline const ::tensorflow::CoordinatedTask& CancelBarrierRequest::source_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.CancelBarrierRequest.source_task)
  return _internal_source_task();
}
inline void CancelBarrierRequest::unsafe_arena_set_allocated_source_task(
    ::tensorflow::CoordinatedTask* source_task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_task_);
  }
  _impl_.source_task_ = source_task;
  if (source_task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CancelBarrierRequest.source_task)
}
inline ::tensorflow::CoordinatedTask* CancelBarrierRequest::release_source_task() {
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinatedTask* CancelBarrierRequest::unsafe_arena_release_source_task() {
  // @@protoc_insertion_point(field_release:tensorflow.CancelBarrierRequest.source_task)
  
  ::tensorflow::CoordinatedTask* temp = _impl_.source_task_;
  _impl_.source_task_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinatedTask* CancelBarrierRequest::_internal_mutable_source_task() {
  
  if (_impl_.source_task_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinatedTask>(GetArenaForAllocation());
    _impl_.source_task_ = p;
  }
  return _impl_.source_task_;
}
inline ::tensorflow::CoordinatedTask* CancelBarrierRequest::mutable_source_task() {
  ::tensorflow::CoordinatedTask* _msg = _internal_mutable_source_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.CancelBarrierRequest.source_task)
  return _msg;
}
inline void CancelBarrierRequest::set_allocated_source_task(::tensorflow::CoordinatedTask* source_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_task_;
  }
  if (source_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_task);
    if (message_arena != submessage_arena) {
      source_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_task, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_task_ = source_task;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CancelBarrierRequest.source_task)
}

// -------------------------------------------------------------------

// CancelBarrierResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::CoordinatedTaskState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::CoordinatedTaskState>() {
  return ::tensorflow::CoordinatedTaskState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto
