#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
500彩票网解析器测试
专门测试500彩票网的数据解析

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
from bs4 import BeautifulSoup
import re
import json
from datetime import datetime

def test_500_ssq_parsing():
    """测试500彩票网双色球数据解析"""
    print("🔍 测试500彩票网双色球数据解析...")
    
    url = "https://datachart.500.com/ssq/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.500.com/'
    }
    
    try:
        print(f"📡 请求URL: {url}")
        response = requests.get(url, headers=headers, timeout=15)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 设置编码
            response.encoding = 'gb2312'
            html_content = response.text
            
            print(f"📄 页面大小: {len(html_content)} 字符")
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找开奖数据
            lottery_data = []
            
            # 方法1: 查找包含开奖号码的表格
            print("\n🔍 方法1: 查找表格数据...")
            tables = soup.find_all('table')
            print(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                table_text = table.get_text()
                if '期号' in table_text or '开奖号码' in table_text or '红球' in table_text:
                    print(f"  表格 {i+1} 包含开奖相关内容")
                    
                    # 查找表格行
                    rows = table.find_all('tr')
                    print(f"    共 {len(rows)} 行")
                    
                    for j, row in enumerate(rows[:10]):  # 只检查前10行
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 6:  # 至少6列才可能包含完整数据
                            row_text = [cell.get_text().strip() for cell in cells]
                            print(f"    行 {j+1}: {row_text}")
                            
                            # 尝试解析开奖号码
                            parsed_data = parse_lottery_row(row_text)
                            if parsed_data:
                                lottery_data.append(parsed_data)
                                print(f"      ✅ 解析成功: {parsed_data}")
            
            # 方法2: 查找特定的CSS类或ID
            print("\n🔍 方法2: 查找特定元素...")
            
            # 常见的彩票数据元素
            selectors = [
                '.ball_red',
                '.ball_blue', 
                '.red_ball',
                '.blue_ball',
                '.lottery_ball',
                '.number',
                '.result',
                '.award',
                '[class*="ball"]',
                '[class*="number"]',
                '[class*="result"]'
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"  找到 {len(elements)} 个 {selector} 元素")
                    for elem in elements[:5]:  # 只显示前5个
                        print(f"    {elem.get_text().strip()}")
            
            # 方法3: 正则表达式查找号码模式
            print("\n🔍 方法3: 正则表达式查找...")
            
            # 查找双色球号码模式 (6个红球 + 1个蓝球)
            patterns = [
                r'(\d{2})\s+(\d{2})\s+(\d{2})\s+(\d{2})\s+(\d{2})\s+(\d{2})\s+(\d{2})',  # 空格分隔
                r'(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2})',  # 逗号分隔
                r'(\d{2})[\s,]+(\d{2})[\s,]+(\d{2})[\s,]+(\d{2})[\s,]+(\d{2})[\s,]+(\d{2})[\s,]+(\d{2})',  # 混合分隔
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content)
                if matches:
                    print(f"  找到 {len(matches)} 个号码匹配")
                    for match in matches[:3]:  # 只显示前3个
                        red_balls = [int(x) for x in match[:6]]
                        blue_ball = int(match[6])
                        if all(1 <= ball <= 33 for ball in red_balls) and 1 <= blue_ball <= 16:
                            print(f"    有效号码: {red_balls} + {blue_ball}")
                            lottery_data.append({
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'source': '正则匹配'
                            })
            
            # 方法4: 查找JavaScript数据
            print("\n🔍 方法4: 查找JavaScript数据...")
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    script_content = script.string
                    if 'ssq' in script_content.lower() or '双色球' in script_content:
                        print(f"  找到相关JavaScript代码片段:")
                        print(f"    {script_content[:200]}...")
                        
                        # 尝试提取JSON数据
                        json_matches = re.findall(r'\{[^{}]*\}', script_content)
                        for json_match in json_matches[:3]:
                            try:
                                data = json.loads(json_match)
                                print(f"    JSON数据: {data}")
                            except:
                                pass
            
            print(f"\n📊 总共解析到 {len(lottery_data)} 条开奖数据")
            
            if lottery_data:
                print("✅ 成功解析的数据:")
                for i, data in enumerate(lottery_data[:5]):  # 只显示前5条
                    print(f"  {i+1}. {data}")
                return True, lottery_data
            else:
                print("❌ 未能解析到有效的开奖数据")
                
                # 保存HTML用于调试
                with open('debug_500.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("💾 已保存HTML到 debug_500.html 用于调试")
                
                return False, []
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False, []
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def parse_lottery_row(row_data):
    """解析表格行数据"""
    try:
        # 尝试不同的列布局
        if len(row_data) >= 8:
            # 检查是否包含期号
            issue_pattern = r'20\d{5}'
            date_pattern = r'20\d{2}-\d{2}-\d{2}'
            
            issue = None
            date = None
            numbers = []
            
            for cell in row_data:
                if re.match(issue_pattern, cell):
                    issue = cell
                elif re.match(date_pattern, cell):
                    date = cell
                elif re.match(r'^\d{1,2}$', cell):
                    num = int(cell)
                    if 1 <= num <= 33:  # 可能是红球或蓝球
                        numbers.append(num)
            
            # 如果找到了6个红球和1个蓝球
            if len(numbers) >= 7:
                red_balls = numbers[:6]
                blue_ball = numbers[6]
                
                # 验证号码范围
                if (all(1 <= ball <= 33 for ball in red_balls) and 
                    1 <= blue_ball <= 16 and 
                    len(set(red_balls)) == 6):  # 红球不重复
                    
                    return {
                        'issue': issue,
                        'date': date,
                        'red_balls': red_balls,
                        'blue_ball': blue_ball,
                        'source': '表格解析'
                    }
    except:
        pass
    
    return None

def main():
    """主函数"""
    print("🎯 500彩票网解析器测试")
    print("=" * 50)
    
    success, data = test_500_ssq_parsing()
    
    if success:
        print(f"\n🎉 解析成功！找到 {len(data)} 条数据")
        print("接下来可以基于这个解析逻辑开发爬虫")
    else:
        print("\n😞 解析失败，需要进一步分析HTML结构")
        print("请检查 debug_500.html 文件来分析页面结构")

if __name__ == "__main__":
    main()
