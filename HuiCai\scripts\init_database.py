#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
支持SQLite本地部署和PostgreSQL生产环境

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import sys
import sqlite3
import asyncio
from pathlib import Path
from datetime import datetime, date, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.local_deployment.local_config_manager import LocalConfigManager
except ImportError:
    from src.management_layer.config_manager import ConfigManager as LocalConfigManager


def init_sqlite_database(db_path: str):
    """初始化SQLite数据库"""
    try:
        print(f"📁 初始化SQLite数据库: {db_path}")
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 启用外键约束
        cursor.execute('PRAGMA foreign_keys = ON')
        
        # 设置WAL模式以提高并发性能
        cursor.execute('PRAGMA journal_mode = WAL')
        
        # 设置同步模式
        cursor.execute('PRAGMA synchronous = NORMAL')
        
        print("📋 创建数据表...")
        
        # 创建彩票开奖表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_draws (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                draw_number TEXT NOT NULL,
                draw_date DATE NOT NULL,
                numbers TEXT NOT NULL,
                extra_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, draw_number)
            )
        ''')
        
        # 创建预测结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prediction_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                prediction_date DATE NOT NULL,
                model_type TEXT NOT NULL,
                predicted_numbers TEXT NOT NULL,
                confidence REAL,
                actual_numbers TEXT,
                accuracy REAL,
                match_count INTEGER,
                reward_amount REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX(lottery_type, prediction_date)
            )
        ''')
        
        # 创建模型训练记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_training_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                model_type TEXT NOT NULL,
                training_date TIMESTAMP NOT NULL,
                training_samples INTEGER,
                validation_samples INTEGER,
                training_accuracy REAL,
                validation_accuracy REAL,
                training_loss REAL,
                validation_loss REAL,
                training_time REAL,
                model_parameters TEXT,
                model_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建系统日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                module TEXT NOT NULL,
                function_name TEXT,
                message TEXT NOT NULL,
                exception_info TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX(level, timestamp)
            )
        ''')
        
        # 创建爬虫状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawler_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL UNIQUE,
                last_crawl_time TIMESTAMP,
                last_draw_number TEXT,
                last_draw_date DATE,
                crawl_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                error_count INTEGER DEFAULT 0,
                last_error TEXT,
                status TEXT DEFAULT 'idle',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建模型性能统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                model_type TEXT NOT NULL,
                date DATE NOT NULL,
                total_predictions INTEGER DEFAULT 0,
                correct_predictions INTEGER DEFAULT 0,
                accuracy_rate REAL DEFAULT 0.0,
                avg_confidence REAL DEFAULT 0.0,
                best_match_count INTEGER DEFAULT 0,
                total_reward REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, model_type, date)
            )
        ''')
        
        # 创建用户设置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT NOT NULL UNIQUE,
                setting_value TEXT NOT NULL,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        print("📊 创建索引...")
        
        # 创建索引以提高查询性能
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_lottery_draws_type_date ON lottery_draws(lottery_type, draw_date DESC)',
            'CREATE INDEX IF NOT EXISTS idx_lottery_draws_number ON lottery_draws(lottery_type, draw_number)',
            'CREATE INDEX IF NOT EXISTS idx_prediction_results_type_date ON prediction_results(lottery_type, prediction_date DESC)',
            'CREATE INDEX IF NOT EXISTS idx_prediction_results_model ON prediction_results(model_type, prediction_date DESC)',
            'CREATE INDEX IF NOT EXISTS idx_model_training_type ON model_training_records(lottery_type, model_type, training_date DESC)',
            'CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp DESC)',
            'CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level, timestamp DESC)',
            'CREATE INDEX IF NOT EXISTS idx_crawler_status_type ON crawler_status(lottery_type)',
            'CREATE INDEX IF NOT EXISTS idx_model_performance_date ON model_performance_stats(date DESC)',
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("⚙️ 插入初始数据...")
        
        # 插入默认用户设置
        default_settings = [
            ('theme', 'light', 'string', '界面主题'),
            ('language', 'zh-CN', 'string', '界面语言'),
            ('auto_predict', 'true', 'boolean', '自动预测开关'),
            ('notification_enabled', 'true', 'boolean', '通知开关'),
            ('max_prediction_history', '1000', 'integer', '最大预测历史记录数'),
            ('model_auto_retrain', 'true', 'boolean', '模型自动重训练'),
            ('data_backup_enabled', 'true', 'boolean', '数据备份开关'),
        ]
        
        for key, value, type_, desc in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO user_settings (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', (key, value, type_, desc))
        
        # 初始化爬虫状态
        lottery_types = ['shuangseqiu', 'daletou', 'fucai3d']
        for lottery_type in lottery_types:
            cursor.execute('''
                INSERT OR IGNORE INTO crawler_status (lottery_type, status)
                VALUES (?, 'idle')
            ''', (lottery_type,))
        
        conn.commit()
        conn.close()
        
        print("✅ SQLite数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ SQLite数据库初始化失败: {e}")
        return False


def generate_sample_data(db_path: str, lottery_type: str = 'shuangseqiu', count: int = 50):
    """生成示例数据用于测试"""
    try:
        print(f"🎲 生成 {lottery_type} 示例数据 ({count} 条)...")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 生成历史开奖数据
        base_date = date.today() - timedelta(days=count * 3)
        
        for i in range(count):
            draw_date = base_date + timedelta(days=i * 3)
            draw_number = f"2025{i+1:03d}"
            
            if lottery_type == 'shuangseqiu':
                # 双色球: 6个红球(1-33) + 1个蓝球(1-16)
                red_balls = sorted(random.sample(range(1, 34), 6))
                blue_ball = random.randint(1, 16)
                numbers = red_balls + [blue_ball]
            elif lottery_type == 'daletou':
                # 大乐透: 5个前区(1-35) + 2个后区(1-12)
                front_balls = sorted(random.sample(range(1, 36), 5))
                back_balls = sorted(random.sample(range(1, 13), 2))
                numbers = front_balls + back_balls
            elif lottery_type == 'fucai3d':
                # 福彩3D: 3个数字(0-9)
                numbers = [random.randint(0, 9) for _ in range(3)]
            else:
                numbers = [random.randint(1, 33) for _ in range(6)]
            
            numbers_str = str(numbers)
            
            cursor.execute('''
                INSERT OR IGNORE INTO lottery_draws 
                (lottery_type, draw_number, draw_date, numbers)
                VALUES (?, ?, ?, ?)
            ''', (lottery_type, draw_number, draw_date, numbers_str))
        
        # 生成一些预测结果示例
        for i in range(10):
            pred_date = date.today() - timedelta(days=i)
            model_types = ['incremental_learning', 'chinese_algorithms', 'lstm']
            
            for model_type in model_types:
                if lottery_type == 'shuangseqiu':
                    pred_numbers = sorted(random.sample(range(1, 34), 6)) + [random.randint(1, 16)]
                elif lottery_type == 'daletou':
                    pred_numbers = sorted(random.sample(range(1, 36), 5)) + sorted(random.sample(range(1, 13), 2))
                else:
                    pred_numbers = [random.randint(0, 9) for _ in range(3)]
                
                confidence = random.uniform(0.3, 0.9)
                
                cursor.execute('''
                    INSERT OR IGNORE INTO prediction_results 
                    (lottery_type, prediction_date, model_type, predicted_numbers, confidence)
                    VALUES (?, ?, ?, ?, ?)
                ''', (lottery_type, pred_date, model_type, str(pred_numbers), confidence))
        
        conn.commit()
        conn.close()
        
        print(f"✅ {lottery_type} 示例数据生成完成")
        return True
        
    except Exception as e:
        print(f"❌ 示例数据生成失败: {e}")
        return False


def check_database_health(db_path: str):
    """检查数据库健康状态"""
    try:
        print("🔍 检查数据库健康状态...")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute('''
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ''')
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'lottery_draws', 'prediction_results', 'model_training_records',
            'system_logs', 'crawler_status', 'model_performance_stats', 'user_settings'
        ]
        
        missing_tables = set(expected_tables) - set(tables)
        if missing_tables:
            print(f"⚠️ 缺少表: {missing_tables}")
        else:
            print("✅ 所有必需的表都存在")
        
        # 检查数据量
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            count = cursor.fetchone()[0]
            print(f"📊 {table}: {count} 条记录")
        
        # 检查数据库大小
        cursor.execute('PRAGMA page_count')
        page_count = cursor.fetchone()[0]
        cursor.execute('PRAGMA page_size')
        page_size = cursor.fetchone()[0]
        db_size_mb = (page_count * page_size) / (1024 * 1024)
        print(f"💾 数据库大小: {db_size_mb:.2f} MB")
        
        conn.close()
        
        print("✅ 数据库健康检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库健康检查失败: {e}")
        return False


async def main():
    """主函数"""
    print("="*60)
    print("HuiCai 慧彩系统 - 数据库初始化")
    print("="*60)
    
    try:
        # 初始化配置管理器
        config_manager = LocalConfigManager()
        await config_manager.load_config()
        
        # 获取数据库配置
        db_config = config_manager.get('database', {})
        db_type = db_config.get('type', 'sqlite')
        
        if db_type == 'sqlite':
            db_path = db_config.get('path', 'data/huicai.db')
            
            # 初始化SQLite数据库
            if init_sqlite_database(db_path):
                print("✅ 数据库初始化成功")
                
                # 询问是否生成示例数据
                response = input("\n是否生成示例数据用于测试? (y/N): ").strip().lower()
                if response in ['y', 'yes']:
                    generate_sample_data(db_path, 'shuangseqiu', 30)
                    generate_sample_data(db_path, 'daletou', 20)
                    generate_sample_data(db_path, 'fucai3d', 40)
                
                # 检查数据库健康状态
                check_database_health(db_path)
                
            else:
                print("❌ 数据库初始化失败")
                return False
        
        else:
            print(f"⚠️ 暂不支持 {db_type} 数据库的自动初始化")
            print("请手动创建数据库和表结构")
            return False
        
        print("\n" + "="*60)
        print("✅ 数据库初始化完成！")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化过程出错: {e}")
        return False


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行初始化
    success = asyncio.run(main())
    
    if not success:
        sys.exit(1)
