#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细API测试脚本
深入测试可用的数据源和HTML解析

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
import json
import time
from bs4 import BeautifulSoup
import re

def test_apiopen_variations():
    """测试API开放平台的不同参数组合"""
    print("🔍 深入测试API开放平台...")
    
    base_url = "https://api.apiopen.top/api/lottery"
    
    # 尝试不同的参数组合
    param_combinations = [
        {},  # 无参数
        {'type': 'ssq'},  # 只有类型
        {'type': 'dlt'},  # 大乐透
        {'lottery': 'ssq'},  # 不同的参数名
        {'name': 'ssq'},
        {'game': 'ssq'},
        {'id': 'ssq'},
        {'code': 'ssq'},
        {'type': 'ssq', 'num': 5},
        {'type': 'ssq', 'limit': 5},
        {'type': 'ssq', 'size': 5},
        {'type': 'ssq', 'page': 1},
        {'type': 'ssq', 'latest': 1},
        {'type': 'ssq', 'recent': 5},
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Referer': 'https://api.apiopen.top/'
    }
    
    for i, params in enumerate(param_combinations):
        print(f"\n  尝试参数组合 {i+1}: {params}")
        try:
            response = requests.get(base_url, params=params, headers=headers, timeout=10)
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"    响应: {json.dumps(result, ensure_ascii=False)}")
                    
                    # 检查是否有有效数据
                    if result.get('code') == 200 or 'data' in result or 'result' in result:
                        print(f"    ✅ 可能找到有效数据！")
                        return True, params, result
                except:
                    print(f"    非JSON响应: {response.text[:200]}")
            
            time.sleep(0.5)  # 避免请求过快
            
        except Exception as e:
            print(f"    异常: {e}")
    
    return False, None, None

def test_html_parsing():
    """测试HTML页面解析"""
    print("\n🔍 测试HTML页面解析...")
    
    # 测试可能包含彩票数据的HTML页面
    html_sources = [
        {
            'name': '500彩票网双色球',
            'url': 'https://datachart.500.com/ssq/',
            'encoding': 'gb2312'
        },
        {
            'name': '500彩票网历史数据',
            'url': 'https://datachart.500.com/ssq/history/newinc/history.php?start=24001&end=24010',
            'encoding': 'gb2312'
        },
        {
            'name': '新浪彩票双色球',
            'url': 'https://lottery.sina.com.cn/ssq/',
            'encoding': 'utf-8'
        },
        {
            'name': '腾讯彩票双色球',
            'url': 'https://caipiao.qq.com/ssq/',
            'encoding': 'utf-8'
        },
        {
            'name': '彩票365',
            'url': 'https://www.cp365.com/lottery/ssq/',
            'encoding': 'utf-8'
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    successful_sources = []
    
    for source in html_sources:
        print(f"\n  测试 {source['name']}")
        print(f"    URL: {source['url']}")
        
        try:
            response = requests.get(source['url'], headers=headers, timeout=15)
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 设置正确的编码
                if source['encoding']:
                    response.encoding = source['encoding']
                
                html_content = response.text
                
                # 检查是否包含彩票相关内容
                lottery_keywords = ['双色球', 'ssq', '红球', '蓝球', '开奖', '期号']
                found_keywords = [kw for kw in lottery_keywords if kw in html_content]
                
                if found_keywords:
                    print(f"    ✅ 包含彩票关键词: {found_keywords}")
                    
                    # 尝试解析HTML
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # 查找可能包含开奖数据的元素
                    potential_data = []
                    
                    # 查找表格
                    tables = soup.find_all('table')
                    for table in tables[:3]:  # 只检查前3个表格
                        text = table.get_text()
                        if any(kw in text for kw in ['期号', '开奖', '红球', '蓝球']):
                            potential_data.append(f"表格数据: {text[:100]}...")
                    
                    # 查找包含数字的div
                    divs = soup.find_all('div', class_=re.compile(r'(ball|number|result|award)'))
                    for div in divs[:5]:  # 只检查前5个
                        text = div.get_text().strip()
                        if re.search(r'\d{2}', text):  # 包含两位数字
                            potential_data.append(f"数字元素: {text}")
                    
                    if potential_data:
                        print(f"    📊 找到潜在数据:")
                        for data in potential_data[:3]:  # 只显示前3个
                            print(f"      {data}")
                        
                        successful_sources.append({
                            'name': source['name'],
                            'url': source['url'],
                            'encoding': source['encoding'],
                            'data_samples': potential_data
                        })
                    else:
                        print(f"    ❌ 未找到结构化数据")
                else:
                    print(f"    ❌ 不包含彩票关键词")
            
            time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            print(f"    异常: {e}")
    
    return successful_sources

def test_alternative_apis():
    """测试其他可能的API"""
    print("\n🔍 测试其他可能的API...")
    
    # 尝试一些可能存在的API端点
    alternative_apis = [
        'https://api.lottery.com/v1/ssq',
        'https://api.lottery.com/ssq',
        'https://lottery-api.com/ssq',
        'https://caipiao-api.com/ssq',
        'https://kaijiang-api.com/ssq',
        'https://api.caipiao.com/ssq',
        'https://api.kaijiang.com/ssq',
        'https://lottery.api.com/ssq',
        'https://ssq.api.com/latest',
        'https://api.ssq.com/latest'
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json'
    }
    
    working_apis = []
    
    for api_url in alternative_apis:
        print(f"  测试: {api_url}")
        try:
            response = requests.get(api_url, headers=headers, timeout=5)
            if response.status_code == 200:
                print(f"    ✅ 响应成功: {response.status_code}")
                try:
                    data = response.json()
                    print(f"    JSON数据: {json.dumps(data, ensure_ascii=False)[:100]}...")
                    working_apis.append(api_url)
                except:
                    print(f"    HTML响应: {response.text[:100]}...")
            else:
                print(f"    状态码: {response.status_code}")
        except:
            print(f"    连接失败")
        
        time.sleep(0.3)
    
    return working_apis

def main():
    """主函数"""
    print("🎯 详细API测试和HTML解析")
    print("=" * 50)
    
    # 测试API开放平台的不同参数
    api_success, api_params, api_data = test_apiopen_variations()
    
    # 测试HTML页面解析
    html_sources = test_html_parsing()
    
    # 测试其他API
    alt_apis = test_alternative_apis()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    if api_success:
        print(f"✅ API开放平台可用参数: {api_params}")
        print(f"   返回数据: {api_data}")
    
    if html_sources:
        print(f"✅ 可解析HTML源: {len(html_sources)}个")
        for source in html_sources:
            print(f"   - {source['name']}: {source['url']}")
    
    if alt_apis:
        print(f"✅ 其他可用API: {len(alt_apis)}个")
        for api in alt_apis:
            print(f"   - {api}")
    
    if not any([api_success, html_sources, alt_apis]):
        print("❌ 未找到可用的数据源")
        print("建议尝试:")
        print("  1. 使用代理服务器")
        print("  2. 申请官方API密钥")
        print("  3. 寻找其他第三方数据源")
        print("  4. 考虑使用Selenium进行动态页面抓取")

if __name__ == "__main__":
    main()
