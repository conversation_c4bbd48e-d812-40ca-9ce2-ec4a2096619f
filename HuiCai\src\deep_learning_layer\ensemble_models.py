#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型集成策略
集成多种深度学习模型和传统算法的预测结果

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, date
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report
import pickle
import os


class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, config_manager, logger: logging.Logger, lottery_type: str):
        """
        初始化集成预测器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
            lottery_type: 彩票类型
        """
        self.config_manager = config_manager
        self.logger = logger
        self.lottery_type = lottery_type
        
        # 集成配置
        self.ensemble_config = config_manager.get('deep_learning', {}).get('ensemble', {})
        
        # 集成策略
        self.ensemble_method = self.ensemble_config.get('method', 'weighted_voting')
        self.weight_update_method = self.ensemble_config.get('weight_update', 'performance_based')
        
        # 模型权重
        self.model_weights = {
            'incremental_learning': 0.2,
            'chinese_algorithms': 0.15,
            'lstm': 0.25,
            'transformer': 0.25,
            'reinforcement_learning': 0.15
        }
        
        # 性能历史
        self.performance_history = {}
        
        # 集成历史
        self.ensemble_history = []
        
        # 模型保存路径
        self.model_save_path = config_manager.get('learning.model_save_path', 'data/models')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.info(f"集成预测器初始化完成: {lottery_type}")
    
    def update_model_weights(self, model_performances: Dict[str, float]):
        """
        更新模型权重
        
        Args:
            model_performances: 各模型的性能指标
        """
        try:
            if self.weight_update_method == 'performance_based':
                # 基于性能的权重更新
                total_performance = sum(model_performances.values())
                if total_performance > 0:
                    for model_name, performance in model_performances.items():
                        if model_name in self.model_weights:
                            # 性能越高，权重越大
                            self.model_weights[model_name] = performance / total_performance
                
                # 归一化权重
                total_weight = sum(self.model_weights.values())
                if total_weight > 0:
                    for model_name in self.model_weights:
                        self.model_weights[model_name] /= total_weight
            
            elif self.weight_update_method == 'adaptive':
                # 自适应权重更新
                self._adaptive_weight_update(model_performances)
            
            self.logger.info(f"模型权重已更新: {self.model_weights}")
            
        except Exception as e:
            self.logger.error(f"更新模型权重失败: {e}")
    
    def _adaptive_weight_update(self, model_performances: Dict[str, float]):
        """自适应权重更新"""
        try:
            # 记录性能历史
            for model_name, performance in model_performances.items():
                if model_name not in self.performance_history:
                    self.performance_history[model_name] = []
                self.performance_history[model_name].append(performance)
                
                # 只保留最近的性能记录
                if len(self.performance_history[model_name]) > 10:
                    self.performance_history[model_name] = self.performance_history[model_name][-10:]
            
            # 基于性能趋势调整权重
            for model_name in self.model_weights:
                if model_name in self.performance_history and len(self.performance_history[model_name]) >= 2:
                    recent_performances = self.performance_history[model_name]
                    
                    # 计算性能趋势
                    trend = np.mean(recent_performances[-3:]) - np.mean(recent_performances[-6:-3]) if len(recent_performances) >= 6 else 0
                    
                    # 根据趋势调整权重
                    if trend > 0:  # 性能上升
                        self.model_weights[model_name] *= 1.1
                    elif trend < 0:  # 性能下降
                        self.model_weights[model_name] *= 0.9
            
            # 归一化权重
            total_weight = sum(self.model_weights.values())
            if total_weight > 0:
                for model_name in self.model_weights:
                    self.model_weights[model_name] /= total_weight
                    
        except Exception as e:
            self.logger.error(f"自适应权重更新失败: {e}")
    
    def weighted_voting_ensemble(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """
        加权投票集成
        
        Args:
            predictions: 各模型的预测结果
            
        Returns:
            Dict: 集成预测结果
        """
        try:
            ensemble_result = {
                'lottery_type': self.lottery_type,
                'prediction_date': datetime.now(),
                'ensemble_method': 'weighted_voting',
                'model_weights': self.model_weights.copy(),
                'individual_predictions': predictions,
                'ensemble_prediction': {},
                'confidence_score': 0.0
            }
            
            # 收集所有预测的号码
            all_predicted_numbers = {}
            confidence_scores = {}
            
            for model_name, prediction in predictions.items():
                if not prediction or 'predictions' not in prediction:
                    continue
                
                weight = self.model_weights.get(model_name, 0)
                if weight == 0:
                    continue
                
                # 提取预测号码
                if isinstance(prediction['predictions'], list) and prediction['predictions']:
                    pred_data = prediction['predictions'][0]
                    
                    if 'predicted_numbers' in pred_data:
                        numbers = pred_data['predicted_numbers']
                    elif 'predicted_number' in pred_data:
                        numbers = [pred_data['predicted_number']]
                    else:
                        continue
                    
                    confidence = pred_data.get('confidence', 0.5)
                    
                    # 加权投票
                    for number in numbers:
                        if number not in all_predicted_numbers:
                            all_predicted_numbers[number] = 0
                        all_predicted_numbers[number] += weight * confidence
                    
                    confidence_scores[model_name] = confidence * weight
            
            if not all_predicted_numbers:
                return ensemble_result
            
            # 选择得票最高的号码
            sorted_numbers = sorted(all_predicted_numbers.items(), key=lambda x: x[1], reverse=True)
            
            # 根据彩票类型选择合适数量的号码
            if self.lottery_type == 'shuangseqiu':
                # 双色球：6个红球 + 1个蓝球
                selected_numbers = [num for num, _ in sorted_numbers[:7]]
            elif self.lottery_type == 'daletou':
                # 大乐透：5个前区 + 2个后区
                selected_numbers = [num for num, _ in sorted_numbers[:7]]
            elif self.lottery_type == 'fucai3d':
                # 福彩3D：3个数字
                selected_numbers = [num for num, _ in sorted_numbers[:3]]
            else:
                selected_numbers = [num for num, _ in sorted_numbers[:7]]
            
            # 计算集成置信度
            total_confidence = sum(confidence_scores.values())
            
            ensemble_result['ensemble_prediction'] = {
                'predicted_numbers': selected_numbers,
                'number_scores': dict(sorted_numbers[:len(selected_numbers)]),
                'confidence': total_confidence
            }
            ensemble_result['confidence_score'] = total_confidence
            
            return ensemble_result
            
        except Exception as e:
            self.logger.error(f"加权投票集成失败: {e}")
            return {}
    
    def stacking_ensemble(self, predictions: Dict[str, Any], meta_learner_type: str = 'logistic') -> Dict[str, Any]:
        """
        Stacking集成
        
        Args:
            predictions: 各模型的预测结果
            meta_learner_type: 元学习器类型
            
        Returns:
            Dict: 集成预测结果
        """
        try:
            # 这里简化实现，实际应用中需要训练元学习器
            self.logger.info("Stacking集成（简化版本）")
            
            # 收集预测特征
            features = []
            for model_name, prediction in predictions.items():
                if not prediction or 'predictions' not in prediction:
                    continue
                
                if isinstance(prediction['predictions'], list) and prediction['predictions']:
                    pred_data = prediction['predictions'][0]
                    confidence = pred_data.get('confidence', 0.5)
                    features.append(confidence)
            
            if not features:
                return {}
            
            # 简化的元学习器决策
            meta_confidence = np.mean(features)
            
            # 使用加权投票作为fallback
            ensemble_result = self.weighted_voting_ensemble(predictions)
            ensemble_result['ensemble_method'] = 'stacking'
            ensemble_result['meta_confidence'] = meta_confidence
            
            return ensemble_result
            
        except Exception as e:
            self.logger.error(f"Stacking集成失败: {e}")
            return {}
    
    def dynamic_ensemble(self, predictions: Dict[str, Any], recent_performance: Dict[str, float]) -> Dict[str, Any]:
        """
        动态集成
        
        Args:
            predictions: 各模型的预测结果
            recent_performance: 最近的性能表现
            
        Returns:
            Dict: 集成预测结果
        """
        try:
            # 动态调整权重
            self.update_model_weights(recent_performance)
            
            # 基于当前性能选择最佳集成策略
            avg_performance = np.mean(list(recent_performance.values()))
            
            if avg_performance > 0.7:
                # 性能较好时使用加权投票
                return self.weighted_voting_ensemble(predictions)
            else:
                # 性能一般时使用Stacking
                return self.stacking_ensemble(predictions)
                
        except Exception as e:
            self.logger.error(f"动态集成失败: {e}")
            return {}
    
    def ensemble_predict(self, predictions: Dict[str, Any], 
                        recent_performance: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        集成预测
        
        Args:
            predictions: 各模型的预测结果
            recent_performance: 最近的性能表现
            
        Returns:
            Dict: 集成预测结果
        """
        try:
            self.logger.info(f"开始集成预测: {self.ensemble_method}")
            
            if self.ensemble_method == 'weighted_voting':
                result = self.weighted_voting_ensemble(predictions)
            elif self.ensemble_method == 'stacking':
                result = self.stacking_ensemble(predictions)
            elif self.ensemble_method == 'dynamic':
                if recent_performance:
                    result = self.dynamic_ensemble(predictions, recent_performance)
                else:
                    result = self.weighted_voting_ensemble(predictions)
            else:
                # 默认使用加权投票
                result = self.weighted_voting_ensemble(predictions)
            
            # 记录集成历史
            if result:
                self.ensemble_history.append({
                    'timestamp': datetime.now(),
                    'method': self.ensemble_method,
                    'model_count': len(predictions),
                    'confidence': result.get('confidence_score', 0),
                    'weights': self.model_weights.copy()
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            return {}
    
    def evaluate_ensemble_performance(self, predictions: Dict[str, Any], 
                                    actual_numbers: List[int]) -> Dict[str, Any]:
        """
        评估集成性能
        
        Args:
            predictions: 预测结果
            actual_numbers: 实际开奖号码
            
        Returns:
            Dict: 性能评估结果
        """
        try:
            if 'ensemble_prediction' not in predictions:
                return {}
            
            predicted_numbers = predictions['ensemble_prediction'].get('predicted_numbers', [])
            
            # 计算匹配度
            matches = len(set(predicted_numbers) & set(actual_numbers))
            total_predicted = len(predicted_numbers)
            total_actual = len(actual_numbers)
            
            # 精确率和召回率
            precision = matches / total_predicted if total_predicted > 0 else 0
            recall = matches / total_actual if total_actual > 0 else 0
            
            # F1分数
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            # 完全匹配
            exact_match = set(predicted_numbers) == set(actual_numbers)
            
            evaluation_result = {
                'timestamp': datetime.now(),
                'lottery_type': self.lottery_type,
                'matches': matches,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'exact_match': exact_match,
                'predicted_numbers': predicted_numbers,
                'actual_numbers': actual_numbers,
                'ensemble_confidence': predictions.get('confidence_score', 0)
            }
            
            return evaluation_result
            
        except Exception as e:
            self.logger.error(f"评估集成性能失败: {e}")
            return {}
    
    def get_ensemble_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        try:
            if not self.ensemble_history:
                return {}
            
            recent_history = self.ensemble_history[-10:]  # 最近10次
            
            stats = {
                'total_predictions': len(self.ensemble_history),
                'recent_predictions': len(recent_history),
                'average_confidence': np.mean([h['confidence'] for h in recent_history]),
                'current_weights': self.model_weights.copy(),
                'ensemble_method': self.ensemble_method,
                'performance_history_length': {
                    model: len(history) for model, history in self.performance_history.items()
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取集成统计失败: {e}")
            return {}
    
    def save_ensemble_state(self):
        """保存集成状态"""
        try:
            ensemble_state = {
                'model_weights': self.model_weights,
                'performance_history': self.performance_history,
                'ensemble_history': self.ensemble_history,
                'ensemble_method': self.ensemble_method,
                'weight_update_method': self.weight_update_method
            }
            
            state_path = os.path.join(self.model_save_path, f'{self.lottery_type}_ensemble_state.pkl')
            with open(state_path, 'wb') as f:
                pickle.dump(ensemble_state, f)
            
            self.logger.info("集成状态保存完成")
            
        except Exception as e:
            self.logger.error(f"保存集成状态失败: {e}")
    
    def load_ensemble_state(self) -> bool:
        """加载集成状态"""
        try:
            state_path = os.path.join(self.model_save_path, f'{self.lottery_type}_ensemble_state.pkl')
            
            if not os.path.exists(state_path):
                return False
            
            with open(state_path, 'rb') as f:
                ensemble_state = pickle.load(f)
            
            self.model_weights = ensemble_state.get('model_weights', self.model_weights)
            self.performance_history = ensemble_state.get('performance_history', {})
            self.ensemble_history = ensemble_state.get('ensemble_history', [])
            self.ensemble_method = ensemble_state.get('ensemble_method', self.ensemble_method)
            self.weight_update_method = ensemble_state.get('weight_update_method', self.weight_update_method)
            
            self.logger.info("集成状态加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载集成状态失败: {e}")
            return False
