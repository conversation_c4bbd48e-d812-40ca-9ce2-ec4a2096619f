import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Layout, theme } from 'antd';
import { Provider } from 'react-redux';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store/store';
import AppLayout from './components/Layout/AppLayout';
import Dashboard from './pages/Dashboard/Dashboard';
import PredictionPage from './pages/Prediction/PredictionPage';
import DataManagement from './pages/DataManagement/DataManagement';
import ModelManagement from './pages/ModelManagement/ModelManagement';
import SystemSettings from './pages/SystemSettings/SystemSettings';
import './App.css';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 6,
          },
        }}
      >
        <Router>
          <div className="App">
            <AppLayout>
              <Content style={{ margin: '24px 16px', padding: 24, minHeight: 280 }}>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/prediction" element={<PredictionPage />} />
                  <Route path="/data" element={<DataManagement />} />
                  <Route path="/models" element={<ModelManagement />} />
                  <Route path="/settings" element={<SystemSettings />} />
                </Routes>
              </Content>
            </AppLayout>
          </div>
        </Router>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
