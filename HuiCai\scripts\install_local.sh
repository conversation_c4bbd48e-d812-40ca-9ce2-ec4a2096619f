#!/bin/bash
# HuiCai 慧彩系统本地安装脚本
# 支持 Linux/macOS 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_info "检测到 Linux 系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到 macOS 系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查内存
    if [[ "$OS" == "linux" ]]; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    else
        MEMORY_GB=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    fi
    
    if [ "$MEMORY_GB" -lt 8 ]; then
        log_warning "内存不足8GB，可能影响性能"
    else
        log_success "内存检查通过: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$DISK_SPACE" -lt 10 ]; then
        log_error "磁盘空间不足10GB"
        exit 1
    else
        log_success "磁盘空间检查通过: ${DISK_SPACE}GB"
    fi
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        log_error "Python未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}')
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || [ "$PYTHON_MINOR" -lt 8 ]; then
        log_error "Python版本过低: $PYTHON_VERSION，需要3.8+"
        exit 1
    else
        log_success "Python版本检查通过: $PYTHON_VERSION"
    fi
}

# 检查Node.js环境
check_nodejs() {
    log_info "检查Node.js环境..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | sed 's/v//')
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d. -f1)
        
        if [ "$NODE_MAJOR" -lt 16 ]; then
            log_warning "Node.js版本过低: $NODE_VERSION，推荐16+"
        else
            log_success "Node.js版本检查通过: $NODE_VERSION"
        fi
    else
        log_warning "Node.js未安装，将跳过Web界面安装"
        SKIP_WEB=true
    fi
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        log_success "npm版本: $NPM_VERSION"
    else
        log_warning "npm未安装，将跳过Web界面安装"
        SKIP_WEB=true
    fi
}

# 创建虚拟环境
create_venv() {
    log_info "创建Python虚拟环境..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_success "虚拟环境已激活"
    
    # 升级pip
    pip install --upgrade pip
    log_success "pip已升级到最新版本"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖包..."
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 安装基础依赖
    pip install -r requirements.txt
    log_success "Python依赖安装完成"
    
    # 安装可选的深度学习依赖
    log_info "检查深度学习依赖..."
    if pip list | grep -q tensorflow; then
        log_success "TensorFlow已安装"
    else
        log_warning "TensorFlow未安装，深度学习功能可能受限"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 创建数据目录
    mkdir -p data/models
    mkdir -p data/logs
    mkdir -p data/cache
    
    # 运行数据库初始化脚本
    if [ -f "scripts/init_database.py" ]; then
        $PYTHON_CMD scripts/init_database.py
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在，将在首次运行时自动初始化"
    fi
}

# 安装Web界面依赖
install_web_deps() {
    if [ "$SKIP_WEB" = true ]; then
        log_warning "跳过Web界面安装"
        return
    fi
    
    log_info "安装Web界面依赖..."
    
    if [ -d "web" ]; then
        cd web
        npm install
        log_success "Web依赖安装完成"
        cd ..
    else
        log_warning "web目录不存在，将在第四阶段创建"
    fi
}

# 创建启动脚本
create_start_script() {
    log_info "创建启动脚本..."
    
    cat > start_huicai.sh << 'EOF'
#!/bin/bash
# HuiCai 慧彩系统启动脚本

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}"
echo "  _   _       _  _____      _ "
echo " | | | |     (_)/  __ \    (_)"
echo " | |_| |_   _ _| /  \/ __ _ _ "
echo " |  _  | | | | | |    / _\` | |"
echo " | | | | |_| | | \__/\ (_| | |"
echo " \_| |_/\__,_|_|\____/\__,_|_|"
echo ""
echo "    慧彩智能体系统 v4.0"
echo "    本地部署版本"
echo -e "${NC}"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 install_local.sh"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 启动后端服务
echo "🚀 启动后端服务..."
python main.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if ps -p $BACKEND_PID > /dev/null; then
    echo -e "${GREEN}✅ 后端服务启动成功 (PID: $BACKEND_PID)${NC}"
else
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 启动Web界面（如果存在）
if [ -d "web" ] && command -v npm &> /dev/null; then
    echo "🌐 启动Web界面..."
    cd web
    npm start &
    WEB_PID=$!
    cd ..
    echo -e "${GREEN}✅ Web界面启动成功 (PID: $WEB_PID)${NC}"
fi

echo ""
echo "🎉 HuiCai系统启动完成！"
echo ""
echo "📱 CLI界面: 当前终端"
echo "🌐 Web界面: http://localhost:3000 (如果已安装)"
echo "📚 API文档: http://localhost:8000/docs (如果已启用)"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止系统..."; kill $BACKEND_PID 2>/dev/null; kill $WEB_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait
EOF

    chmod +x start_huicai.sh
    log_success "启动脚本创建完成"
}

# 创建停止脚本
create_stop_script() {
    log_info "创建停止脚本..."
    
    cat > stop_huicai.sh << 'EOF'
#!/bin/bash
# HuiCai 慧彩系统停止脚本

echo "🛑 正在停止HuiCai系统..."

# 停止Python进程
pkill -f "python main.py"

# 停止Node.js进程
pkill -f "npm start"

# 停止可能的其他相关进程
pkill -f "uvicorn"
pkill -f "huicai"

echo "✅ HuiCai系统已停止"
EOF

    chmod +x stop_huicai.sh
    log_success "停止脚本创建完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    if [ ! -f "config/local_config.yaml" ]; then
        mkdir -p config
        cat > config/local_config.yaml << 'EOF'
# HuiCai 本地部署配置文件

# 数据库配置 (使用SQLite)
database:
  type: sqlite
  path: data/huicai.db
  backup_enabled: true
  auto_vacuum: true

# 缓存配置 (使用内存缓存)
cache:
  type: memory
  max_size: 512MB
  ttl: 3600

# Web服务配置
web:
  host: 127.0.0.1
  port: 8000
  debug: false
  auto_reload: false

# 深度学习配置
deep_learning:
  enabled: true
  device: cpu  # 本地部署默认使用CPU
  model_cache: true
  batch_size: 16  # 降低批次大小以适应本地环境

# 日志配置
logging:
  level: INFO
  file_enabled: true
  console_enabled: true
  max_file_size: 10MB
  backup_count: 5

# 本地优化配置
local_optimization:
  memory_limit: 4GB
  cpu_cores: auto
  cache_enabled: true
  compression_enabled: true
EOF
        log_success "本地配置文件创建完成"
    else
        log_info "配置文件已存在"
    fi
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    # 测试Python模块导入
    $PYTHON_CMD -c "
import sys
sys.path.append('src')
try:
    from management_layer.config_manager import ConfigManager
    from management_layer.log_manager import LogManager
    print('✅ 核心模块导入成功')
except ImportError as e:
    print(f'❌ 模块导入失败: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        log_success "系统测试通过"
    else
        log_error "系统测试失败"
        exit 1
    fi
}

# 主安装流程
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  HuiCai 慧彩智能体系统 - 本地安装程序"
    echo "=================================================="
    echo -e "${NC}"
    
    # 检查是否在项目根目录
    if [ ! -f "main.py" ]; then
        log_error "请在HuiCai项目根目录下运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_system
    check_python
    check_nodejs
    create_venv
    install_python_deps
    init_database
    install_web_deps
    create_start_script
    create_stop_script
    create_config
    run_tests
    
    echo ""
    echo -e "${GREEN}🎉 HuiCai系统本地安装完成！${NC}"
    echo ""
    echo "📋 安装摘要:"
    echo "  ✅ Python环境: $PYTHON_VERSION"
    echo "  ✅ 虚拟环境: venv/"
    echo "  ✅ 依赖包: 已安装"
    echo "  ✅ 数据库: SQLite"
    echo "  ✅ 配置文件: config/local_config.yaml"
    echo ""
    echo "🚀 启动系统:"
    echo "  ./start_huicai.sh"
    echo ""
    echo "🛑 停止系统:"
    echo "  ./stop_huicai.sh"
    echo ""
    echo "📖 更多信息请查看: 第四阶段本地部署方案.md"
}

# 运行主程序
main "$@"
