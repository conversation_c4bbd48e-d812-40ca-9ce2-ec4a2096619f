#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地化数据库管理器
管理四个专用数据库：双色球数据、大乐透数据、双色球学习优化、大乐透学习优化

Author: HuiCai Team
Date: 2025-01-15
"""

import sqlite3
import json
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import logging
import threading
from contextlib import contextmanager


class LocalDatabaseManager:
    """本地数据库管理器"""
    
    def __init__(self, db_dir: str = "databases"):
        self.db_dir = Path(db_dir)
        self.db_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 数据库文件路径
        self.databases = {
            'shuangseqiu_data': self.db_dir / 'shuangseqiu_data.db',
            'daletou_data': self.db_dir / 'daletou_data.db',
            'shuangseqiu_learning': self.db_dir / 'shuangseqiu_learning.db',
            'daletou_learning': self.db_dir / 'daletou_learning.db'
        }
        
        # 连接池
        self._connections = {}
        self._locks = {name: threading.Lock() for name in self.databases.keys()}
        
        # 初始化所有数据库
        self._initialize_databases()
    
    def _initialize_databases(self):
        """初始化所有数据库"""
        self.logger.info("初始化本地数据库...")
        
        # 初始化双色球数据库
        self._init_shuangseqiu_data_db()
        
        # 初始化大乐透数据库
        self._init_daletou_data_db()
        
        # 初始化双色球学习优化数据库
        self._init_shuangseqiu_learning_db()
        
        # 初始化大乐透学习优化数据库
        self._init_daletou_learning_db()
        
        self.logger.info("所有数据库初始化完成")
    
    @contextmanager
    def get_connection(self, db_name: str):
        """获取数据库连接（上下文管理器）"""
        if db_name not in self.databases:
            raise ValueError(f"未知的数据库: {db_name}")
        
        with self._locks[db_name]:
            conn = sqlite3.connect(
                self.databases[db_name],
                timeout=30.0,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row
            try:
                yield conn
            finally:
                conn.close()
    
    def _init_shuangseqiu_data_db(self):
        """初始化双色球数据数据库"""
        with self.get_connection('shuangseqiu_data') as conn:
            cursor = conn.cursor()
            
            # 开奖数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS draw_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT UNIQUE NOT NULL,
                    date TEXT NOT NULL,
                    red_ball_1 INTEGER NOT NULL,
                    red_ball_2 INTEGER NOT NULL,
                    red_ball_3 INTEGER NOT NULL,
                    red_ball_4 INTEGER NOT NULL,
                    red_ball_5 INTEGER NOT NULL,
                    red_ball_6 INTEGER NOT NULL,
                    blue_ball INTEGER NOT NULL,
                    sales_amount BIGINT DEFAULT 0,
                    pool_amount BIGINT DEFAULT 0,
                    source TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 统计分析表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stat_type TEXT NOT NULL,
                    stat_key TEXT NOT NULL,
                    stat_value TEXT NOT NULL,
                    period_start TEXT,
                    period_end TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stat_type, stat_key, period_start, period_end)
                )
            ''')
            
            # 预测记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prediction_id TEXT UNIQUE NOT NULL,
                    algorithm TEXT NOT NULL,
                    red_ball_1 INTEGER NOT NULL,
                    red_ball_2 INTEGER NOT NULL,
                    red_ball_3 INTEGER NOT NULL,
                    red_ball_4 INTEGER NOT NULL,
                    red_ball_5 INTEGER NOT NULL,
                    red_ball_6 INTEGER NOT NULL,
                    blue_ball INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    target_issue TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    result_status TEXT DEFAULT 'pending'
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_results_date ON draw_results(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_results_issue ON draw_results(issue)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_statistics_type ON statistics(stat_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_predictions_algorithm ON predictions(algorithm)')
            
            conn.commit()
    
    def _init_daletou_data_db(self):
        """初始化大乐透数据数据库"""
        with self.get_connection('daletou_data') as conn:
            cursor = conn.cursor()
            
            # 开奖数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS draw_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT UNIQUE NOT NULL,
                    date TEXT NOT NULL,
                    front_ball_1 INTEGER NOT NULL,
                    front_ball_2 INTEGER NOT NULL,
                    front_ball_3 INTEGER NOT NULL,
                    front_ball_4 INTEGER NOT NULL,
                    front_ball_5 INTEGER NOT NULL,
                    back_ball_1 INTEGER NOT NULL,
                    back_ball_2 INTEGER NOT NULL,
                    sales_amount BIGINT DEFAULT 0,
                    pool_amount BIGINT DEFAULT 0,
                    source TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 统计分析表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stat_type TEXT NOT NULL,
                    stat_key TEXT NOT NULL,
                    stat_value TEXT NOT NULL,
                    period_start TEXT,
                    period_end TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stat_type, stat_key, period_start, period_end)
                )
            ''')
            
            # 预测记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prediction_id TEXT UNIQUE NOT NULL,
                    algorithm TEXT NOT NULL,
                    front_ball_1 INTEGER NOT NULL,
                    front_ball_2 INTEGER NOT NULL,
                    front_ball_3 INTEGER NOT NULL,
                    front_ball_4 INTEGER NOT NULL,
                    front_ball_5 INTEGER NOT NULL,
                    back_ball_1 INTEGER NOT NULL,
                    back_ball_2 INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    target_issue TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    result_status TEXT DEFAULT 'pending'
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_results_date ON draw_results(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_results_issue ON draw_results(issue)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_statistics_type ON statistics(stat_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_predictions_algorithm ON predictions(algorithm)')
            
            conn.commit()
    
    def _init_shuangseqiu_learning_db(self):
        """初始化双色球学习优化数据库"""
        with self.get_connection('shuangseqiu_learning') as conn:
            cursor = conn.cursor()
            
            # 模型参数表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_parameters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    parameter_name TEXT NOT NULL,
                    parameter_value TEXT NOT NULL,
                    parameter_type TEXT NOT NULL,
                    version INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(model_name, parameter_name, version)
                )
            ''')
            
            # 学习历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    learning_type TEXT NOT NULL,
                    input_data TEXT NOT NULL,
                    output_data TEXT NOT NULL,
                    accuracy REAL,
                    loss REAL,
                    epoch INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 优化策略表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS optimization_strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT UNIQUE NOT NULL,
                    strategy_config TEXT NOT NULL,
                    performance_score REAL DEFAULT 0.0,
                    usage_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 知识库表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    knowledge_type TEXT NOT NULL,
                    knowledge_key TEXT NOT NULL,
                    knowledge_value TEXT NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    source TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(knowledge_type, knowledge_key)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_parameters_name ON model_parameters(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_learning_history_model ON learning_history(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_optimization_strategies_score ON optimization_strategies(performance_score)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_base_type ON knowledge_base(knowledge_type)')
            
            conn.commit()
    
    def _init_daletou_learning_db(self):
        """初始化大乐透学习优化数据库"""
        with self.get_connection('daletou_learning') as conn:
            cursor = conn.cursor()
            
            # 模型参数表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_parameters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    parameter_name TEXT NOT NULL,
                    parameter_value TEXT NOT NULL,
                    parameter_type TEXT NOT NULL,
                    version INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(model_name, parameter_name, version)
                )
            ''')
            
            # 学习历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    learning_type TEXT NOT NULL,
                    input_data TEXT NOT NULL,
                    output_data TEXT NOT NULL,
                    accuracy REAL,
                    loss REAL,
                    epoch INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 优化策略表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS optimization_strategies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT UNIQUE NOT NULL,
                    strategy_config TEXT NOT NULL,
                    performance_score REAL DEFAULT 0.0,
                    usage_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 知识库表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    knowledge_type TEXT NOT NULL,
                    knowledge_key TEXT NOT NULL,
                    knowledge_value TEXT NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    source TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(knowledge_type, knowledge_key)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_parameters_name ON model_parameters(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_learning_history_model ON learning_history(model_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_optimization_strategies_score ON optimization_strategies(performance_score)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_knowledge_base_type ON knowledge_base(knowledge_type)')
            
            conn.commit()
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        info = {}
        
        for db_name, db_path in self.databases.items():
            try:
                with self.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    
                    # 获取表信息
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    # 获取每个表的记录数
                    table_counts = {}
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        table_counts[table] = cursor.fetchone()[0]
                    
                    # 获取数据库文件大小
                    file_size = db_path.stat().st_size if db_path.exists() else 0
                    
                    info[db_name] = {
                        'path': str(db_path),
                        'size_bytes': file_size,
                        'size_mb': round(file_size / 1024 / 1024, 2),
                        'tables': tables,
                        'table_counts': table_counts,
                        'total_records': sum(table_counts.values())
                    }
            
            except Exception as e:
                self.logger.error(f"获取数据库 {db_name} 信息失败: {e}")
                info[db_name] = {'error': str(e)}
        
        return info
    
    def backup_database(self, db_name: str, backup_path: Optional[str] = None) -> str:
        """备份数据库"""
        if db_name not in self.databases:
            raise ValueError(f"未知的数据库: {db_name}")
        
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.db_dir / f"{db_name}_backup_{timestamp}.db"
        
        source_path = self.databases[db_name]
        
        with self.get_connection(db_name) as source_conn:
            backup_conn = sqlite3.connect(backup_path)
            source_conn.backup(backup_conn)
            backup_conn.close()
        
        self.logger.info(f"数据库 {db_name} 已备份到 {backup_path}")
        return str(backup_path)
    
    def restore_database(self, db_name: str, backup_path: str):
        """恢复数据库"""
        if db_name not in self.databases:
            raise ValueError(f"未知的数据库: {db_name}")
        
        if not Path(backup_path).exists():
            raise FileNotFoundError(f"备份文件不存在: {backup_path}")
        
        target_path = self.databases[db_name]
        
        # 备份当前数据库
        current_backup = self.backup_database(db_name)
        
        try:
            # 恢复数据库
            backup_conn = sqlite3.connect(backup_path)
            with self.get_connection(db_name) as target_conn:
                backup_conn.backup(target_conn)
            backup_conn.close()
            
            self.logger.info(f"数据库 {db_name} 已从 {backup_path} 恢复")
            
        except Exception as e:
            # 恢复失败，回滚到之前的备份
            self.logger.error(f"恢复数据库失败: {e}")
            self.restore_database(db_name, current_backup)
            raise
    
    def optimize_databases(self):
        """优化所有数据库"""
        self.logger.info("开始优化数据库...")
        
        for db_name in self.databases.keys():
            try:
                with self.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    
                    # 执行VACUUM清理
                    cursor.execute("VACUUM")
                    
                    # 重建索引
                    cursor.execute("REINDEX")
                    
                    # 分析表统计信息
                    cursor.execute("ANALYZE")
                    
                    conn.commit()
                
                self.logger.info(f"数据库 {db_name} 优化完成")
                
            except Exception as e:
                self.logger.error(f"优化数据库 {db_name} 失败: {e}")
        
        self.logger.info("数据库优化完成")
    
    def close_all_connections(self):
        """关闭所有连接"""
        for conn in self._connections.values():
            if conn:
                conn.close()
        self._connections.clear()
        self.logger.info("所有数据库连接已关闭")


    def execute_query(self, db_name: str, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """执行查询"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

    def execute_update(self, db_name: str, query: str, params: tuple = ()) -> int:
        """执行更新操作"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount

    def execute_many(self, db_name: str, query: str, params_list: List[tuple]) -> int:
        """批量执行操作"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)

    db_manager = LocalDatabaseManager()

    # 显示数据库信息
    info = db_manager.get_database_info()
    print("数据库信息:")
    for db_name, db_info in info.items():
        print(f"  {db_name}: {db_info}")

    # 优化数据库
    db_manager.optimize_databases()

    db_manager.close_all_connections()
