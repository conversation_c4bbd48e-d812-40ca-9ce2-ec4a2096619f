#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化器
提供模型压缩、推理加速、内存优化等功能

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import gc
import psutil
import numpy as np
import pickle
import gzip
import threading
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor


class ModelCompressor:
    """模型压缩器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.compression_methods = {
            'quantization': self._quantize_model,
            'pruning': self._prune_model,
            'distillation': self._distill_model,
            'compression': self._compress_model_file
        }
    
    def compress_model(self, model_path: str, method: str = 'quantization', 
                      compression_ratio: float = 0.5) -> Dict[str, Any]:
        """
        压缩模型
        
        Args:
            model_path: 模型文件路径
            method: 压缩方法
            compression_ratio: 压缩比例
            
        Returns:
            Dict: 压缩结果
        """
        try:
            self.logger.info(f"开始压缩模型: {model_path}, 方法: {method}")
            
            if method not in self.compression_methods:
                raise ValueError(f"不支持的压缩方法: {method}")
            
            # 获取原始模型大小
            original_size = os.path.getsize(model_path)
            
            # 执行压缩
            compressed_path = self.compression_methods[method](
                model_path, compression_ratio
            )
            
            # 获取压缩后大小
            compressed_size = os.path.getsize(compressed_path)
            
            result = {
                'original_path': model_path,
                'compressed_path': compressed_path,
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': compressed_size / original_size,
                'size_reduction': (original_size - compressed_size) / original_size,
                'method': method,
                'timestamp': datetime.now()
            }
            
            self.logger.info(f"模型压缩完成: 压缩比 {result['compression_ratio']:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"模型压缩失败: {e}")
            raise
    
    def _quantize_model(self, model_path: str, ratio: float) -> str:
        """量化压缩"""
        try:
            # 加载模型
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # 简化的量化：将float64转为float32
            if hasattr(model_data, 'astype'):
                model_data = model_data.astype(np.float32)
            elif isinstance(model_data, dict):
                for key, value in model_data.items():
                    if isinstance(value, np.ndarray):
                        model_data[key] = value.astype(np.float32)
            
            # 保存量化后的模型
            compressed_path = model_path.replace('.pkl', '_quantized.pkl')
            with open(compressed_path, 'wb') as f:
                pickle.dump(model_data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            return compressed_path
            
        except Exception as e:
            self.logger.error(f"量化压缩失败: {e}")
            raise
    
    def _prune_model(self, model_path: str, ratio: float) -> str:
        """剪枝压缩"""
        try:
            # 简化的剪枝实现
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # 如果是numpy数组，进行简单的阈值剪枝
            if isinstance(model_data, np.ndarray):
                threshold = np.percentile(np.abs(model_data), ratio * 100)
                model_data[np.abs(model_data) < threshold] = 0
            
            compressed_path = model_path.replace('.pkl', '_pruned.pkl')
            with open(compressed_path, 'wb') as f:
                pickle.dump(model_data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            return compressed_path
            
        except Exception as e:
            self.logger.error(f"剪枝压缩失败: {e}")
            raise
    
    def _distill_model(self, model_path: str, ratio: float) -> str:
        """知识蒸馏压缩"""
        # 简化实现：直接复制模型
        compressed_path = model_path.replace('.pkl', '_distilled.pkl')
        
        import shutil
        shutil.copy2(model_path, compressed_path)
        
        return compressed_path
    
    def _compress_model_file(self, model_path: str, ratio: float) -> str:
        """文件级压缩"""
        try:
            compressed_path = model_path + '.gz'
            
            with open(model_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            return compressed_path
            
        except Exception as e:
            self.logger.error(f"文件压缩失败: {e}")
            raise


class InferenceAccelerator:
    """推理加速器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.cache = {}
        self.cache_lock = threading.Lock()
        self.max_cache_size = 1000
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    def accelerate_prediction(self, prediction_func, *args, **kwargs) -> Any:
        """
        加速预测
        
        Args:
            prediction_func: 预测函数
            *args, **kwargs: 预测函数参数
            
        Returns:
            Any: 预测结果
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(prediction_func.__name__, args, kwargs)
            
            # 检查缓存
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                self.logger.debug(f"使用缓存结果: {cache_key}")
                return cached_result
            
            # 执行预测
            start_time = datetime.now()
            result = prediction_func(*args, **kwargs)
            end_time = datetime.now()
            
            # 缓存结果
            self._put_to_cache(cache_key, result)
            
            # 记录性能
            duration = (end_time - start_time).total_seconds()
            self.logger.debug(f"预测完成: {prediction_func.__name__}, 耗时: {duration:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"加速预测失败: {e}")
            raise
    
    async def async_accelerate_prediction(self, prediction_func, *args, **kwargs) -> Any:
        """异步加速预测"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.thread_pool, 
            self.accelerate_prediction, 
            prediction_func, 
            *args, 
            **kwargs
        )
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        try:
            import hashlib
            
            # 将参数转换为字符串
            args_str = str(args)
            kwargs_str = str(sorted(kwargs.items()))
            
            # 生成哈希
            content = f"{func_name}_{args_str}_{kwargs_str}"
            return hashlib.md5(content.encode()).hexdigest()
            
        except Exception:
            return f"{func_name}_{len(args)}_{len(kwargs)}"
    
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取结果"""
        with self.cache_lock:
            return self.cache.get(key)
    
    def _put_to_cache(self, key: str, value: Any):
        """将结果放入缓存"""
        with self.cache_lock:
            if len(self.cache) >= self.max_cache_size:
                # 简单的LRU：删除第一个元素
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            self.cache[key] = value
    
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.cache.clear()
        self.logger.info("推理缓存已清空")


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.memory_threshold = 80  # 内存使用率阈值（百分比）
        self.monitoring = False
        self.monitor_thread = None
    
    def optimize_memory(self) -> Dict[str, Any]:
        """优化内存使用"""
        try:
            self.logger.info("开始内存优化")
            
            # 获取优化前的内存状态
            before_memory = self._get_memory_info()
            
            # 执行垃圾回收
            collected = gc.collect()
            
            # 清理numpy缓存
            self._clear_numpy_cache()
            
            # 获取优化后的内存状态
            after_memory = self._get_memory_info()
            
            result = {
                'before_memory': before_memory,
                'after_memory': after_memory,
                'memory_freed': before_memory['used'] - after_memory['used'],
                'objects_collected': collected,
                'timestamp': datetime.now()
            }
            
            self.logger.info(f"内存优化完成: 释放 {result['memory_freed']:.2f}MB")
            return result
            
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
            raise
    
    def start_memory_monitoring(self):
        """启动内存监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._memory_monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.logger.info("内存监控已启动")
    
    def stop_memory_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("内存监控已停止")
    
    def _memory_monitor_loop(self):
        """内存监控循环"""
        import time
        
        while self.monitoring:
            try:
                memory_info = self._get_memory_info()
                usage_percent = memory_info['percent']
                
                if usage_percent > self.memory_threshold:
                    self.logger.warning(f"内存使用率过高: {usage_percent:.1f}%")
                    self.optimize_memory()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"内存监控错误: {e}")
                time.sleep(60)
    
    def _get_memory_info(self) -> Dict[str, float]:
        """获取内存信息"""
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / (1024**3),  # GB
            'available': memory.available / (1024**3),  # GB
            'used': memory.used / (1024**3),  # GB
            'percent': memory.percent
        }
    
    def _clear_numpy_cache(self):
        """清理numpy缓存"""
        try:
            # 清理numpy的内部缓存
            if hasattr(np, '_NoValue'):
                np._NoValue._clear_cache()
        except Exception:
            pass


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        self.config_manager = config_manager
        self.logger = logger
        
        # 初始化子组件
        self.model_compressor = ModelCompressor(logger)
        self.inference_accelerator = InferenceAccelerator(logger)
        self.memory_optimizer = MemoryOptimizer(logger)
        
        # 性能统计
        self.performance_stats = {
            'compression_count': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'memory_optimizations': 0,
            'total_time_saved': 0.0
        }
        
        self.logger.info("性能优化器初始化完成")
    
    async def optimize_system(self) -> Dict[str, Any]:
        """系统全面优化"""
        try:
            self.logger.info("开始系统全面优化")
            
            optimization_results = {}
            
            # 1. 内存优化
            memory_result = self.memory_optimizer.optimize_memory()
            optimization_results['memory'] = memory_result
            
            # 2. 清理推理缓存
            self.inference_accelerator.clear_cache()
            optimization_results['cache_cleared'] = True
            
            # 3. 模型文件压缩（如果需要）
            model_dir = Path(self.config_manager.get('learning.model_save_path', 'data/models'))
            if model_dir.exists():
                compression_results = await self._compress_models_in_directory(model_dir)
                optimization_results['model_compression'] = compression_results
            
            # 4. 启动内存监控
            self.memory_optimizer.start_memory_monitoring()
            optimization_results['memory_monitoring'] = True
            
            # 更新统计
            self.performance_stats['memory_optimizations'] += 1
            
            self.logger.info("系统全面优化完成")
            return optimization_results
            
        except Exception as e:
            self.logger.error(f"系统优化失败: {e}")
            raise
    
    async def _compress_models_in_directory(self, model_dir: Path) -> List[Dict[str, Any]]:
        """压缩目录中的模型文件"""
        results = []
        
        try:
            for model_file in model_dir.glob("*.pkl"):
                if model_file.stat().st_size > 10 * 1024 * 1024:  # 大于10MB的文件
                    try:
                        result = self.model_compressor.compress_model(
                            str(model_file), 
                            method='quantization'
                        )
                        results.append(result)
                        self.performance_stats['compression_count'] += 1
                    except Exception as e:
                        self.logger.warning(f"压缩模型文件失败 {model_file}: {e}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量压缩模型失败: {e}")
            return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        memory_info = self.memory_optimizer._get_memory_info()
        
        return {
            'performance_stats': self.performance_stats,
            'memory_info': memory_info,
            'cache_size': len(self.inference_accelerator.cache),
            'memory_monitoring': self.memory_optimizer.monitoring,
            'timestamp': datetime.now()
        }
    
    async def close(self):
        """关闭优化器"""
        try:
            # 停止内存监控
            self.memory_optimizer.stop_memory_monitoring()
            
            # 关闭线程池
            self.inference_accelerator.thread_pool.shutdown(wait=True)
            
            self.logger.info("性能优化器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭性能优化器失败: {e}")


# 装饰器：自动性能优化
def optimize_performance(optimizer: PerformanceOptimizer):
    """性能优化装饰器"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            return await optimizer.inference_accelerator.async_accelerate_prediction(
                func, *args, **kwargs
            )
        
        def sync_wrapper(*args, **kwargs):
            return optimizer.inference_accelerator.accelerate_prediction(
                func, *args, **kwargs
            )
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
