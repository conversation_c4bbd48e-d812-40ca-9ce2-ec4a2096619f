#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地部署配置管理器
专门为本地部署优化的轻量级配置管理

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import yaml
import sqlite3
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class LocalConfigManager:
    """本地部署配置管理器"""
    
    def __init__(self):
        """初始化本地配置管理器"""
        self.project_root = Path(__file__).parent.parent.parent
        self.config_dir = self.project_root / "config"
        self.data_dir = self.project_root / "data"
        self.local_config_path = self.config_dir / "local_config.yaml"
        
        # 确保目录存在
        self.config_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        (self.data_dir / "models").mkdir(exist_ok=True)
        (self.data_dir / "logs").mkdir(exist_ok=True)
        (self.data_dir / "cache").mkdir(exist_ok=True)
        
        self.config = {}
        self.logger = None
    
    async def load_config(self):
        """加载本地配置"""
        try:
            if not self.local_config_path.exists():
                self._create_default_local_config()
            
            with open(self.local_config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f) or {}
            
            # 设置环境变量
            os.environ['HUICAI_LOCAL_MODE'] = 'true'
            os.environ['HUICAI_DATA_DIR'] = str(self.data_dir)
            
            return True
            
        except Exception as e:
            print(f"加载本地配置失败: {e}")
            return False
    
    def _create_default_local_config(self):
        """创建默认本地配置"""
        default_config = {
            'database': {
                'type': 'sqlite',
                'path': str(self.data_dir / "huicai.db"),
                'backup_enabled': True,
                'auto_vacuum': True,
                'journal_mode': 'WAL',
                'synchronous': 'NORMAL'
            },
            'cache': {
                'type': 'memory',
                'max_size': '512MB',
                'ttl': 3600,
                'cleanup_interval': 300
            },
            'web': {
                'host': '127.0.0.1',
                'port': 8000,
                'debug': False,
                'auto_reload': False,
                'cors_enabled': True,
                'static_files': True
            },
            'deep_learning': {
                'enabled': True,
                'device': 'cpu',
                'model_cache': True,
                'batch_size': 16,
                'max_memory_usage': '2GB',
                'enabled_models': ['lstm'],  # 本地部署默认只启用LSTM
                'lstm': {
                    'sequence_length': 15,  # 减少序列长度
                    'hidden_units': 64,     # 减少隐藏单元
                    'num_layers': 1,        # 减少层数
                    'dropout_rate': 0.2,
                    'learning_rate': 0.001,
                    'batch_size': 16,
                    'epochs': 50            # 减少训练轮数
                }
            },
            'logging': {
                'level': 'INFO',
                'file_enabled': True,
                'console_enabled': True,
                'max_file_size': '10MB',
                'backup_count': 5,
                'log_dir': str(self.data_dir / "logs")
            },
            'local_optimization': {
                'memory_limit': '4GB',
                'cpu_cores': 'auto',
                'cache_enabled': True,
                'compression_enabled': True,
                'lazy_loading': True,
                'model_quantization': True
            },
            'crawler': {
                'enabled': True,
                'request_delay': 2,      # 增加延迟
                'timeout': 30,
                'retry_times': 3,
                'concurrent_limit': 2,   # 减少并发
                'user_agent_rotation': True,
                'proxy_enabled': False   # 本地部署默认不使用代理
            },
            'lottery_configs': {
                'shuangseqiu': {
                    'name': '双色球',
                    'enabled': True,
                    'red_ball_range': [1, 33],
                    'blue_ball_range': [1, 16],
                    'draw_days': ['二', '四', '日'],
                    'draw_time': '21:15',
                    'data_source': {
                        'primary': 'http://www.cwl.gov.cn',
                        'backup': []
                    }
                },
                'daletou': {
                    'name': '大乐透',
                    'enabled': True,
                    'front_range': [1, 35],
                    'back_range': [1, 12],
                    'draw_days': ['一', '三', '六'],
                    'draw_time': '20:30',
                    'data_source': {
                        'primary': 'http://www.lottery.gov.cn',
                        'backup': []
                    }
                },
                'fucai3d': {
                    'name': '福彩3D',
                    'enabled': True,
                    'number_range': [0, 9],
                    'positions': 3,
                    'draw_days': ['每日'],
                    'draw_time': '20:30',
                    'data_source': {
                        'primary': 'http://www.cwl.gov.cn',
                        'backup': []
                    }
                }
            }
        }
        
        with open(self.local_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.local_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"保存配置失败: {e}")
            return False
    
    def get_lottery_config(self, lottery_type: str) -> Dict[str, Any]:
        """获取彩票配置"""
        return self.get(f'lottery_configs.{lottery_type}', {})
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        db_config = self.get('database', {})
        if db_config.get('type') == 'sqlite':
            db_path = db_config.get('path', str(self.data_dir / "huicai.db"))
            return f"sqlite:///{db_path}"
        else:
            # 兼容其他数据库类型
            host = db_config.get('host', 'localhost')
            port = db_config.get('port', 5432)
            name = db_config.get('name', 'huicai')
            user = db_config.get('user', 'huicai')
            password = db_config.get('password', '')
            return f"postgresql://{user}:{password}@{host}:{port}/{name}"
    
    def init_sqlite_database(self):
        """初始化SQLite数据库"""
        try:
            db_path = self.get('database.path', str(self.data_dir / "huicai.db"))
            
            # 创建数据库连接
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 创建彩票开奖表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_draws (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_type TEXT NOT NULL,
                    draw_number TEXT NOT NULL,
                    draw_date DATE NOT NULL,
                    numbers TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(lottery_type, draw_number)
                )
            ''')
            
            # 创建预测结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_type TEXT NOT NULL,
                    prediction_date DATE NOT NULL,
                    model_type TEXT NOT NULL,
                    predicted_numbers TEXT NOT NULL,
                    confidence REAL,
                    actual_numbers TEXT,
                    accuracy REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建模型训练记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_training_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_type TEXT NOT NULL,
                    model_type TEXT NOT NULL,
                    training_date TIMESTAMP NOT NULL,
                    training_samples INTEGER,
                    validation_accuracy REAL,
                    training_time REAL,
                    model_parameters TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建系统日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,
                    module TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_lottery_draws_type_date ON lottery_draws(lottery_type, draw_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_prediction_results_type_date ON prediction_results(lottery_type, prediction_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_model_training_type ON model_training_records(lottery_type, model_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)')
            
            conn.commit()
            conn.close()
            
            print("✅ SQLite数据库初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ SQLite数据库初始化失败: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        import psutil
        import platform
        
        try:
            return {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total // (1024**3),  # GB
                'disk_free': psutil.disk_usage(str(self.project_root)).free // (1024**3),  # GB
                'config_path': str(self.local_config_path),
                'data_path': str(self.data_dir),
                'local_mode': True
            }
        except ImportError:
            return {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'local_mode': True,
                'config_path': str(self.local_config_path),
                'data_path': str(self.data_dir)
            }
    
    def optimize_for_local(self):
        """针对本地环境进行优化"""
        try:
            # 检测系统资源
            import psutil
            
            # 根据内存大小调整配置
            memory_gb = psutil.virtual_memory().total // (1024**3)
            
            if memory_gb < 8:
                # 低内存优化
                self.set('deep_learning.batch_size', 8)
                self.set('deep_learning.lstm.hidden_units', 32)
                self.set('cache.max_size', '256MB')
                self.set('local_optimization.memory_limit', '2GB')
            elif memory_gb >= 16:
                # 高内存配置
                self.set('deep_learning.batch_size', 32)
                self.set('deep_learning.lstm.hidden_units', 128)
                self.set('cache.max_size', '1GB')
                self.set('local_optimization.memory_limit', '8GB')
            
            # 根据CPU核心数调整并发
            cpu_count = psutil.cpu_count()
            if cpu_count >= 8:
                self.set('crawler.concurrent_limit', 4)
            elif cpu_count >= 4:
                self.set('crawler.concurrent_limit', 2)
            else:
                self.set('crawler.concurrent_limit', 1)
            
            self.save_config()
            print("✅ 本地环境优化完成")
            
        except ImportError:
            print("⚠️ psutil未安装，跳过自动优化")
        except Exception as e:
            print(f"⚠️ 本地环境优化失败: {e}")
    
    @property
    def lottery_configs(self) -> Dict[str, Any]:
        """获取所有彩票配置"""
        return self.get('lottery_configs', {})
