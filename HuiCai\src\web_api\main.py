#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai Web API 服务
基于FastAPI的Web API接口

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
from pathlib import Path
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import uvicorn
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.management_layer.config_manager import ConfigManager
from src.management_layer.log_manager import LogManager
from src.learning_layer.incremental_learner import IncrementalLearner
from src.data_layer.data_storage import DataStorage


# Pydantic模型
class PredictionRequest(BaseModel):
    method: str = "comprehensive"
    parameters: Optional[Dict[str, Any]] = None


class EvaluationRequest(BaseModel):
    actual_numbers: List[int]


class SystemConfigRequest(BaseModel):
    config: Dict[str, Any]


# 全局变量
app = FastAPI(
    title="HuiCai Web API",
    description="慧彩智能体系统 Web API接口",
    version="4.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 系统组件
config_manager = None
logger = None
incremental_learner = None
data_storage = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global config_manager, logger, incremental_learner, data_storage
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        await config_manager.load_config()
        
        # 初始化日志管理器
        log_manager = LogManager(config_manager)
        logger = log_manager.get_logger("WebAPI")
        
        # 初始化数据存储
        data_storage = DataStorage(config_manager, logger)
        await data_storage.initialize()
        
        # 初始化增量学习器
        incremental_learner = IncrementalLearner(config_manager, logger)
        await incremental_learner.initialize()
        
        logger.info("Web API服务启动完成")
        
    except Exception as e:
        print(f"Web API服务启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    global incremental_learner, data_storage, logger
    
    try:
        if incremental_learner:
            await incremental_learner.close()
        
        if data_storage:
            await data_storage.close()
        
        if logger:
            logger.info("Web API服务已关闭")
            
    except Exception as e:
        print(f"Web API服务关闭失败: {e}")


# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 系统API
@app.get("/api/system/info")
async def get_system_info():
    """获取系统信息"""
    try:
        if not config_manager:
            raise HTTPException(status_code=500, detail="系统未初始化")
        
        # 获取系统信息
        import platform
        import psutil
        
        info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total // (1024**3),
            "disk_free": psutil.disk_usage(str(project_root)).free // (1024**3),
            "local_mode": True,
            "config_path": str(config_manager.config_file_path) if hasattr(config_manager, 'config_file_path') else 'N/A',
            "data_path": str(project_root / "data")
        }
        
        return JSONResponse(content=info)
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        import psutil
        import time
        
        # 计算运行时间（简化）
        uptime = time.time() - psutil.boot_time()
        
        status = {
            "status": "running",
            "uptime": int(uptime),
            "cpu_usage": psutil.cpu_percent(interval=1),
            "memory_usage": psutil.virtual_memory().percent,
            "active_models": 3,  # 模拟数据
            "last_prediction": "2025-01-15 10:30:00"
        }
        
        return JSONResponse(content=status)
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/system/logs")
async def get_system_logs(level: Optional[str] = None, limit: int = 100):
    """获取系统日志"""
    try:
        # 这里应该从日志文件或数据库读取日志
        # 暂时返回模拟数据
        logs = [
            "2025-01-15 10:30:00 - INFO - 系统启动完成",
            "2025-01-15 10:31:00 - INFO - 双色球预测完成",
            "2025-01-15 10:32:00 - WARNING - 内存使用率较高",
            "2025-01-15 10:33:00 - INFO - 数据爬取任务完成",
        ]
        
        return JSONResponse(content=logs[:limit])
        
    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 预测API
@app.post("/api/predict/{lottery_type}")
async def make_prediction(lottery_type: str, request: PredictionRequest):
    """进行预测"""
    try:
        if not incremental_learner:
            raise HTTPException(status_code=500, detail="学习器未初始化")
        
        method = request.method
        
        # 根据方法调用不同的预测函数
        if method == "traditional":
            result = await incremental_learner.predict(lottery_type)
        elif method == "chinese":
            result = await incremental_learner.predict_with_chinese_algorithms(lottery_type)
        elif method == "deeplearning":
            result = await incremental_learner.predict_with_deep_learning(lottery_type)
        elif method == "comprehensive":
            result = await incremental_learner.predict_comprehensive(lottery_type)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的预测方法: {method}")
        
        if not result:
            raise HTTPException(status_code=500, detail="预测失败")
        
        # 格式化返回结果
        formatted_result = {
            "id": f"pred_{lottery_type}_{method}_{int(asyncio.get_event_loop().time())}",
            "lottery_type": lottery_type,
            "prediction_date": "2025-01-15",
            "model_type": method,
            "predicted_numbers": _extract_predicted_numbers(result, lottery_type),
            "confidence": _extract_confidence(result),
            "method": method,
            "details": result
        }
        
        return JSONResponse(content=formatted_result)
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/predict/history")
async def get_prediction_history(lottery_type: Optional[str] = None, limit: int = 20):
    """获取预测历史"""
    try:
        # 这里应该从数据库读取历史记录
        # 暂时返回模拟数据
        history = [
            {
                "id": "pred_001",
                "lottery_type": "shuangseqiu",
                "prediction_date": "2025-01-15",
                "predicted_numbers": [3, 8, 16, 21, 28, 33, 12],
                "model_type": "comprehensive",
                "accuracy": 0.6,
                "match_count": 3
            },
            {
                "id": "pred_002",
                "lottery_type": "daletou",
                "prediction_date": "2025-01-14",
                "predicted_numbers": [5, 12, 19, 26, 31, 3, 8],
                "model_type": "deeplearning",
                "accuracy": 0.4,
                "match_count": 2
            }
        ]
        
        if lottery_type:
            history = [h for h in history if h["lottery_type"] == lottery_type]
        
        return JSONResponse(content=history[:limit])
        
    except Exception as e:
        logger.error(f"获取预测历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 数据API
@app.get("/api/data/draws/{lottery_type}")
async def get_lottery_draws(lottery_type: str, limit: int = 50, start_date: Optional[str] = None, end_date: Optional[str] = None):
    """获取开奖数据"""
    try:
        if not data_storage:
            raise HTTPException(status_code=500, detail="数据存储未初始化")
        
        # 转换日期参数
        from datetime import date, datetime
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
        
        draws = await data_storage.get_lottery_draws(
            lottery_type=lottery_type,
            start_date=start_date_obj,
            end_date=end_date_obj,
            limit=limit
        )
        
        return JSONResponse(content=draws)
        
    except Exception as e:
        logger.error(f"获取开奖数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/data/crawl/{lottery_type}")
async def crawl_lottery_data(lottery_type: str):
    """爬取彩票数据"""
    try:
        # 这里应该调用爬虫系统
        # 暂时返回成功响应
        result = {
            "status": "success",
            "message": f"开始爬取{lottery_type}数据",
            "task_id": f"crawl_{lottery_type}_{int(asyncio.get_event_loop().time())}"
        }
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"爬取数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 模型API
@app.get("/api/models/info/{lottery_type}")
async def get_model_info(lottery_type: str):
    """获取模型信息"""
    try:
        if not incremental_learner:
            raise HTTPException(status_code=500, detail="学习器未初始化")
        
        model_info = incremental_learner.get_model_info(lottery_type)
        
        if not model_info:
            raise HTTPException(status_code=404, detail="模型信息不存在")
        
        return JSONResponse(content=model_info)
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/models/info")
async def get_all_models_info():
    """获取所有模型信息"""
    try:
        if not incremental_learner:
            raise HTTPException(status_code=500, detail="学习器未初始化")
        
        # 获取所有支持的彩票类型的模型信息
        lottery_types = ["shuangseqiu", "daletou", "fucai3d"]
        all_info = {}
        
        for lottery_type in lottery_types:
            model_info = incremental_learner.get_model_info(lottery_type)
            if model_info:
                all_info[lottery_type] = model_info
        
        return JSONResponse(content=all_info)
        
    except Exception as e:
        logger.error(f"获取所有模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 静态文件服务
web_dist_path = project_root / "web" / "build"
if web_dist_path.exists():
    app.mount("/static", StaticFiles(directory=str(web_dist_path / "static")), name="static")
    
    @app.get("/")
    async def serve_react_app():
        """服务React应用"""
        return FileResponse(str(web_dist_path / "index.html"))
    
    @app.get("/{path:path}")
    async def serve_react_app_routes(path: str):
        """处理React路由"""
        # 如果是API路径，不处理
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API路径不存在")
        
        # 检查文件是否存在
        file_path = web_dist_path / path
        if file_path.exists() and file_path.is_file():
            return FileResponse(str(file_path))
        
        # 否则返回React应用的index.html
        return FileResponse(str(web_dist_path / "index.html"))


# 辅助函数
def _extract_predicted_numbers(result: Dict[str, Any], lottery_type: str) -> List[int]:
    """从预测结果中提取预测号码"""
    try:
        if "final_recommendation" in result:
            return result["final_recommendation"].get("recommended_numbers", [])
        elif "ensemble_prediction" in result:
            return result["ensemble_prediction"].get("predicted_numbers", [])
        elif "comprehensive_recommendation" in result:
            return result["comprehensive_recommendation"].get("recommended_numbers", [])
        else:
            # 生成模拟号码
            if lottery_type == "shuangseqiu":
                return [3, 8, 16, 21, 28, 33, 12]
            elif lottery_type == "daletou":
                return [5, 12, 19, 26, 31, 3, 8]
            elif lottery_type == "fucai3d":
                return [5, 8, 9]
            else:
                return [1, 2, 3, 4, 5, 6, 7]
    except:
        return []


def _extract_confidence(result: Dict[str, Any]) -> float:
    """从预测结果中提取置信度"""
    try:
        if "final_recommendation" in result:
            return result["final_recommendation"].get("confidence_score", 0.5)
        elif "confidence_score" in result:
            return result["confidence_score"]
        elif "confidence" in result:
            return result["confidence"]
        else:
            return 0.65  # 默认置信度
    except:
        return 0.5


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )
