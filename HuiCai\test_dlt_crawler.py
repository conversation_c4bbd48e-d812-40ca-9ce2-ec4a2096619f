#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透爬虫测试脚本

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_dlt_crawler():
    """测试大乐透爬虫"""
    print("🔵 测试大乐透爬虫...")
    print("-" * 40)
    
    try:
        from src.crawler_system.daletou_crawler import DaletouCrawler
        
        # 创建爬虫实例
        crawler = DaletouCrawler()
        print(f"✅ 爬虫创建成功")
        print(f"📍 数据源数量: {len(crawler.data_sources)}")
        
        # 显示数据源信息
        for i, source in enumerate(crawler.data_sources):
            print(f"  {i+1}. {source['name']} ({source['type']})")
        
        # 测试爬取数据
        print(f"\n📡 爬取最新5期数据...")
        result = crawler.crawl_latest_data(5)
        
        print(f"📊 爬取状态: {result['status']}")
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！")
            print(f"📍 数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            print(f"⏰ 爬取时间: {result['timestamp']}")
            
            # 显示数据详情
            print(f"\n🎯 开奖数据:")
            for i, draw in enumerate(result['data']):
                issue = draw.get('issue', '未知期号')
                date = draw.get('date', '未知日期')
                front_balls = draw.get('front_balls', [])
                back_balls = draw.get('back_balls', [])
                source = draw.get('source', '未知来源')
                
                if front_balls and back_balls:
                    front_str = ' '.join([f"{ball:02d}" for ball in front_balls])
                    back_str = ' '.join([f"{ball:02d}" for ball in back_balls])
                    print(f"  {i+1}. {issue} ({date}): {front_str} + {back_str} [{source}]")
                else:
                    print(f"  {i+1}. {issue} - 数据格式异常: {draw}")
            
            # 验证数据质量
            print(f"\n🔍 数据质量验证:")
            valid_count = 0
            for draw in result['data']:
                front_balls = draw.get('front_balls', [])
                back_balls = draw.get('back_balls', [])
                
                # 检查前区球
                front_valid = (len(front_balls) == 5 and 
                             all(1 <= ball <= 35 for ball in front_balls) and
                             len(set(front_balls)) == 5)
                
                # 检查后区球
                back_valid = (len(back_balls) == 2 and
                            all(1 <= ball <= 12 for ball in back_balls) and
                            len(set(back_balls)) == 2)
                
                if front_valid and back_valid:
                    valid_count += 1
                else:
                    print(f"    ❌ 无效数据: {draw}")
            
            print(f"  ✅ 有效数据: {valid_count}/{len(result['data'])}")
            
            if valid_count == len(result['data']):
                print(f"  🎉 所有数据都有效！")
                return True
            else:
                print(f"  ⚠️ 部分数据无效")
                return False
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            crawler.close()
        except:
            pass

def main():
    """主函数"""
    print("🎯 大乐透爬虫测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success = test_dlt_crawler()
    
    if success:
        print("\n🎉 大乐透爬虫测试成功！")
    else:
        print("\n❌ 大乐透爬虫需要修复")
        print("接下来将基于双色球的成功经验来修复大乐透爬虫")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
