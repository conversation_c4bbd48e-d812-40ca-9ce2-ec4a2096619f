# Copyright 2021 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Python TFLite metrics helper interface."""
import abc


class TFLiteMetricsInterface(metaclass=abc.ABCMeta):
  """Abstract class for TFLiteMetrics."""

  @abc.abstractmethod
  def increase_counter_debugger_creation(self):
    raise NotImplementedError

  @abc.abstractmethod
  def increase_counter_interpreter_creation(self):
    raise NotImplementedError

  @abc.abstractmethod
  def increase_counter_converter_attempt(self):
    raise NotImplementedError

  @abc.abstractmethod
  def increase_counter_converter_success(self):
    raise NotImplementedError

  @abc.abstractmethod
  def set_converter_param(self, name, value):
    raise NotImplementedError

  @abc.abstractmethod
  def set_converter_error(self, error_data):
    raise NotImplementedError

  @abc.abstractmethod
  def set_converter_latency(self, value):
    raise NotImplementedError
