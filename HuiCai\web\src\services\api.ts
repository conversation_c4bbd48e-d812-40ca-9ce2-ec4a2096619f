import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 统一错误处理
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 系统API
export const systemAPI = {
  getSystemInfo: () => api.get('/system/info'),
  getSystemStatus: () => api.get('/system/status'),
  getSystemLogs: (params: { level?: string; limit?: number }) => 
    api.get('/system/logs', { params }),
  updateConfig: (config: any) => api.post('/system/config', config),
};

// 预测API
export const predictionAPI = {
  predict: (lotteryType: string, method: string) => 
    api.post(`/predict/${lotteryType}`, { method }),
  getHistory: (params: { lotteryType?: string; limit?: number }) => 
    api.get('/predict/history', { params }),
  evaluate: (predictionId: string, actualNumbers: number[]) => 
    api.post(`/predict/evaluate/${predictionId}`, { actual_numbers: actualNumbers }),
};

// 数据API
export const dataAPI = {
  getLotteryDraws: (lotteryType: string, params?: { limit?: number; start_date?: string; end_date?: string }) => 
    api.get(`/data/draws/${lotteryType}`, { params }),
  importData: (lotteryType: string, file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(`/data/import/${lotteryType}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
  exportData: (lotteryType: string, params?: { start_date?: string; end_date?: string }) => 
    api.get(`/data/export/${lotteryType}`, { params, responseType: 'blob' }),
  crawlData: (lotteryType: string) => api.post(`/data/crawl/${lotteryType}`),
  getDataStats: (lotteryType: string) => api.get(`/data/stats/${lotteryType}`),
};

// 模型API
export const modelAPI = {
  getModelInfo: (lotteryType: string) => api.get(`/models/info/${lotteryType}`),
  getAllModelsInfo: () => api.get('/models/info'),
  trainModel: (lotteryType: string, modelType: string, params?: any) => 
    api.post(`/models/train/${lotteryType}/${modelType}`, params),
  getTrainingStatus: (lotteryType: string) => api.get(`/models/training-status/${lotteryType}`),
  getModelPerformance: (lotteryType: string, modelType?: string) => 
    api.get(`/models/performance/${lotteryType}`, { params: { model_type: modelType } }),
  deleteModel: (lotteryType: string, modelType: string) => 
    api.delete(`/models/${lotteryType}/${modelType}`),
};

export default api;
