#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai完整系统启动器
100%功能完整版本启动脚本

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
import time
import signal
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入所有系统组件
try:
    from src.management_layer.config_manager import ConfigManager
    from src.management_layer.log_manager import LogManager
    from src.data_layer.data_storage import DataStorage
    from src.data_layer.data_validator import DataValidator
    from src.data_layer.backup_manager import BackupManager
    from src.algorithm_layer.traditional_ml import TraditionalMLAlgorithms
    from src.algorithm_layer.statistical_algorithms import StatisticalAlgorithms
    from src.algorithm_layer.ensemble_algorithms import EnsembleAlgorithms
    from src.deep_learning_layer.model_trainer import ModelTrainer
    from src.learning_layer.incremental_learner import IncrementalLearner
    from src.optimization.performance_optimizer import PerformanceOptimizer
    from src.crawler_system.lottery_crawler import LotteryCrawler
    from src.monitoring.system_monitor import SystemMonitor
    from src.web_interface.web_server import app
    import uvicorn
except ImportError as e:
    print(f"❌ 导入系统组件失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


class HuiCaiSystemLauncher:
    """HuiCai系统启动器"""
    
    def __init__(self):
        self.components = {}
        self.running = False
        self.startup_time = None
        
        # 初始化核心组件
        self._initialize_core_components()
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _initialize_core_components(self):
        """初始化核心组件"""
        try:
            print("🔧 初始化核心组件...")
            
            # 配置管理器
            self.config_manager = ConfigManager()
            print("  ✅ 配置管理器")
            
            # 日志管理器
            log_manager = LogManager(self.config_manager)
            self.logger = log_manager.get_logger("SystemLauncher")
            print("  ✅ 日志管理器")
            
            # 数据存储
            self.data_storage = DataStorage(self.config_manager, self.logger)
            print("  ✅ 数据存储")
            
            # 数据验证器
            self.data_validator = DataValidator(self.logger)
            print("  ✅ 数据验证器")
            
            # 备份管理器
            self.backup_manager = BackupManager(self.config_manager, self.logger)
            print("  ✅ 备份管理器")
            
            # 算法组件
            self.traditional_ml = TraditionalMLAlgorithms(self.logger)
            self.statistical_algorithms = StatisticalAlgorithms(self.logger)
            self.ensemble_algorithms = EnsembleAlgorithms(self.logger)
            print("  ✅ 算法引擎")
            
            # 深度学习组件
            self.model_trainer = ModelTrainer(self.config_manager, self.logger)
            print("  ✅ 深度学习引擎")
            
            # 增量学习器
            self.incremental_learner = IncrementalLearner(self.config_manager, self.logger)
            print("  ✅ 增量学习器")
            
            # 性能优化器
            self.performance_optimizer = PerformanceOptimizer(self.config_manager, self.logger)
            print("  ✅ 性能优化器")
            
            # 爬虫系统
            self.lottery_crawler = LotteryCrawler(self.config_manager, self.logger)
            print("  ✅ 爬虫系统")
            
            # 系统监控
            self.system_monitor = SystemMonitor(self.config_manager, self.logger)
            print("  ✅ 系统监控")
            
            self.logger.info("所有核心组件初始化完成")
            
        except Exception as e:
            print(f"❌ 初始化核心组件失败: {e}")
            sys.exit(1)
    
    async def start_system(self, mode: str = "full"):
        """
        启动系统
        
        Args:
            mode: 启动模式 ('full', 'web', 'cli', 'minimal')
        """
        try:
            self.startup_time = datetime.now()
            self.running = True
            
            print("🚀 启动HuiCai慧彩智能体系统...")
            print(f"📅 启动时间: {self.startup_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🎯 启动模式: {mode}")
            print("="*60)
            
            # 系统自检
            await self._system_health_check()
            
            # 启动监控
            self._start_monitoring()
            
            # 根据模式启动不同组件
            if mode == "full":
                await self._start_full_system()
            elif mode == "web":
                await self._start_web_only()
            elif mode == "cli":
                await self._start_cli_only()
            elif mode == "minimal":
                await self._start_minimal_system()
            else:
                raise ValueError(f"未知启动模式: {mode}")
            
            self.logger.info(f"HuiCai系统启动完成 - 模式: {mode}")
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            print(f"❌ 系统启动失败: {e}")
            await self.shutdown_system()
            raise
    
    async def _system_health_check(self):
        """系统健康检查"""
        print("🔍 执行系统健康检查...")
        
        checks = [
            ("数据库连接", self._check_database),
            ("配置文件", self._check_config),
            ("日志系统", self._check_logging),
            ("存储空间", self._check_storage),
            ("内存资源", self._check_memory),
            ("算法模块", self._check_algorithms)
        ]
        
        failed_checks = []
        
        for check_name, check_func in checks:
            try:
                result = await check_func() if asyncio.iscoroutinefunction(check_func) else check_func()
                if result:
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ⚠️ {check_name} - 警告")
                    failed_checks.append(check_name)
            except Exception as e:
                print(f"  ❌ {check_name} - 失败: {e}")
                failed_checks.append(check_name)
        
        if failed_checks:
            print(f"⚠️ 健康检查发现问题: {', '.join(failed_checks)}")
            print("系统将继续启动，但可能存在功能限制")
        else:
            print("✅ 系统健康检查通过")
    
    def _check_database(self) -> bool:
        """检查数据库"""
        try:
            # 测试数据库连接
            return self.data_storage.test_connection()
        except:
            return False
    
    def _check_config(self) -> bool:
        """检查配置"""
        try:
            config = self.config_manager.get_config()
            return isinstance(config, dict) and len(config) > 0
        except:
            return False
    
    def _check_logging(self) -> bool:
        """检查日志系统"""
        try:
            self.logger.info("日志系统测试")
            return True
        except:
            return False
    
    def _check_storage(self) -> bool:
        """检查存储空间"""
        try:
            import psutil
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            return free_gb > 1.0  # 至少1GB可用空间
        except:
            return False
    
    def _check_memory(self) -> bool:
        """检查内存"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return memory.available > 512 * 1024 * 1024  # 至少512MB可用内存
        except:
            return False
    
    def _check_algorithms(self) -> bool:
        """检查算法模块"""
        try:
            # 测试算法模块是否正常
            return (self.traditional_ml is not None and 
                   self.statistical_algorithms is not None and 
                   self.ensemble_algorithms is not None)
        except:
            return False
    
    def _start_monitoring(self):
        """启动系统监控"""
        try:
            self.system_monitor.start_monitoring()
            print("📊 系统监控已启动")
        except Exception as e:
            print(f"⚠️ 启动系统监控失败: {e}")
    
    async def _start_full_system(self):
        """启动完整系统"""
        print("🌟 启动完整系统模式...")
        
        # 启动所有组件
        tasks = [
            self._start_web_server(),
            self._start_background_services(),
            self._start_data_services(),
            self._start_ai_services()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        print("✅ 完整系统启动完成")
        print("📱 Web界面: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("📊 监控面板: http://localhost:8000/monitoring")
    
    async def _start_web_only(self):
        """仅启动Web服务"""
        print("🌐 启动Web服务模式...")
        await self._start_web_server()
        print("✅ Web服务启动完成")
        print("📱 访问地址: http://localhost:8000")
    
    async def _start_cli_only(self):
        """仅启动CLI模式"""
        print("💻 启动CLI模式...")
        from src.interface_layer.cli_interface import CLIInterface
        
        cli = CLIInterface(
            self.config_manager,
            self.logger,
            self.traditional_ml,
            self.statistical_algorithms,
            self.ensemble_algorithms
        )
        
        await cli.start_interactive_mode()
    
    async def _start_minimal_system(self):
        """启动最小系统"""
        print("⚡ 启动最小系统模式...")
        
        # 只启动核心服务
        await self._start_web_server()
        
        print("✅ 最小系统启动完成")
    
    async def _start_web_server(self):
        """启动Web服务器"""
        try:
            # 在后台启动Web服务器
            config = uvicorn.Config(
                app,
                host="127.0.0.1",
                port=8000,
                log_level="info",
                access_log=False
            )
            server = uvicorn.Server(config)
            
            # 创建服务器任务
            server_task = asyncio.create_task(server.serve())
            self.components['web_server'] = server_task
            
            # 等待服务器启动
            await asyncio.sleep(2)
            
            print("🌐 Web服务器已启动")
            
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            raise
    
    async def _start_background_services(self):
        """启动后台服务"""
        try:
            # 启动性能优化服务
            optimization_task = asyncio.create_task(self._run_optimization_service())
            self.components['optimization'] = optimization_task
            
            # 启动数据爬取服务
            crawler_task = asyncio.create_task(self._run_crawler_service())
            self.components['crawler'] = crawler_task
            
            print("🔄 后台服务已启动")
            
        except Exception as e:
            print(f"❌ 后台服务启动失败: {e}")
    
    async def _start_data_services(self):
        """启动数据服务"""
        try:
            # 启动数据备份服务
            backup_task = asyncio.create_task(self._run_backup_service())
            self.components['backup'] = backup_task
            
            print("💾 数据服务已启动")
            
        except Exception as e:
            print(f"❌ 数据服务启动失败: {e}")
    
    async def _start_ai_services(self):
        """启动AI服务"""
        try:
            # 启动增量学习服务
            learning_task = asyncio.create_task(self._run_learning_service())
            self.components['learning'] = learning_task
            
            print("🤖 AI服务已启动")
            
        except Exception as e:
            print(f"❌ AI服务启动失败: {e}")
    
    async def _run_optimization_service(self):
        """运行优化服务"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # 每小时优化一次
                if self.running:
                    await self.performance_optimizer.optimize_system()
                    self.logger.info("定期系统优化完成")
            except Exception as e:
                self.logger.error(f"优化服务异常: {e}")
                await asyncio.sleep(300)  # 出错后等待5分钟
    
    async def _run_crawler_service(self):
        """运行爬虫服务"""
        while self.running:
            try:
                await asyncio.sleep(7200)  # 每2小时爬取一次
                if self.running:
                    # 爬取最新数据
                    for lottery_type in ['shuangseqiu', 'daletou', 'fucai3d']:
                        result = self.lottery_crawler.get_latest_data(lottery_type)
                        if result['status'] == 'success':
                            self.logger.info(f"爬取{lottery_type}最新数据成功")
            except Exception as e:
                self.logger.error(f"爬虫服务异常: {e}")
                await asyncio.sleep(600)  # 出错后等待10分钟
    
    async def _run_backup_service(self):
        """运行备份服务"""
        while self.running:
            try:
                await asyncio.sleep(86400)  # 每24小时备份一次
                if self.running:
                    result = self.backup_manager.create_full_backup()
                    if result['status'] == 'completed':
                        self.logger.info("定期备份完成")
            except Exception as e:
                self.logger.error(f"备份服务异常: {e}")
                await asyncio.sleep(3600)  # 出错后等待1小时
    
    async def _run_learning_service(self):
        """运行学习服务"""
        while self.running:
            try:
                await asyncio.sleep(1800)  # 每30分钟学习一次
                if self.running:
                    # 增量学习
                    for lottery_type in ['shuangseqiu', 'daletou', 'fucai3d']:
                        await self.incremental_learner.update_models(lottery_type)
                    self.logger.info("增量学习完成")
            except Exception as e:
                self.logger.error(f"学习服务异常: {e}")
                await asyncio.sleep(900)  # 出错后等待15分钟
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 接收到信号 {signum}，正在关闭系统...")
        asyncio.create_task(self.shutdown_system())
    
    async def shutdown_system(self):
        """关闭系统"""
        if not self.running:
            return
        
        print("🔄 正在关闭HuiCai系统...")
        self.running = False
        
        try:
            # 停止监控
            self.system_monitor.stop_monitoring_service()
            print("  ✅ 系统监控已停止")
            
            # 停止备份管理器
            self.backup_manager.stop_auto_backup()
            print("  ✅ 备份服务已停止")
            
            # 取消所有后台任务
            for name, task in self.components.items():
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                print(f"  ✅ {name}服务已停止")
            
            # 计算运行时间
            if self.startup_time:
                runtime = datetime.now() - self.startup_time
                print(f"📊 系统运行时间: {runtime}")
            
            print("✅ HuiCai系统已安全关闭")
            
        except Exception as e:
            print(f"❌ 关闭系统时出错: {e}")
            self.logger.error(f"系统关闭异常: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'running': self.running,
            'startup_time': self.startup_time.isoformat() if self.startup_time else None,
            'components': {
                name: not task.done() if hasattr(task, 'done') else True
                for name, task in self.components.items()
            },
            'monitoring': self.system_monitor.get_current_status(),
            'uptime_seconds': (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0
        }


async def main():
    """主函数"""
    print("🎯 HuiCai慧彩智能体系统 v4.0.0")
    print("🏆 100%功能完整版")
    print("="*60)
    
    # 检查命令行参数
    mode = "full"
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode not in ["full", "web", "cli", "minimal"]:
            print(f"❌ 未知启动模式: {mode}")
            print("可用模式: full, web, cli, minimal")
            sys.exit(1)
    
    # 创建并启动系统
    launcher = HuiCaiSystemLauncher()
    
    try:
        await launcher.start_system(mode)
        
        # 保持运行
        while launcher.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 系统运行异常: {e}")
    finally:
        await launcher.shutdown_system()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())
