#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试更新功能
专门用于调试数据更新到页面的问题

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_crawler_data_format():
    """测试爬虫数据格式"""
    print("🔍 测试爬虫数据格式...")
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        
        manager = CrawlerManager()
        
        # 测试双色球爬虫
        print("\n📡 测试双色球爬虫...")
        ssq_result = manager.manual_crawl('shuangseqiu', 1)
        print(f"双色球结果状态: {ssq_result['status']}")
        
        if ssq_result['status'] == 'success' and ssq_result.get('data'):
            ssq_data = ssq_result['data'][0]
            print(f"双色球数据结构:")
            print(f"  期号: {ssq_data.get('issue', 'N/A')}")
            print(f"  日期: {ssq_data.get('date', 'N/A')}")
            print(f"  红球: {ssq_data.get('red_balls', 'N/A')}")
            print(f"  蓝球: {ssq_data.get('blue_ball', 'N/A')}")
            print(f"  完整数据: {json.dumps(ssq_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"双色球爬取失败: {ssq_result}")
        
        # 测试大乐透爬虫
        print("\n📡 测试大乐透爬虫...")
        dlt_result = manager.manual_crawl('daletou', 1)
        print(f"大乐透结果状态: {dlt_result['status']}")
        
        if dlt_result['status'] == 'success' and dlt_result.get('data'):
            dlt_data = dlt_result['data'][0]
            print(f"大乐透数据结构:")
            print(f"  期号: {dlt_data.get('issue', 'N/A')}")
            print(f"  日期: {dlt_data.get('date', 'N/A')}")
            print(f"  前区球: {dlt_data.get('front_balls', 'N/A')}")
            print(f"  后区球: {dlt_data.get('back_balls', 'N/A')}")
            print(f"  完整数据: {json.dumps(dlt_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"大乐透爬取失败: {dlt_result}")
        
        manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_data():
    """测试数据库数据"""
    print("\n💾 测试数据库数据...")
    
    try:
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import ShuangseqiuDataAccess, DaletouDataAccess
        
        db_manager = LocalDatabaseManager()
        
        # 测试双色球数据
        print("\n🔴 测试双色球数据库...")
        ssq_data = ShuangseqiuDataAccess(db_manager)
        ssq_latest = ssq_data.get_latest_results(1)
        
        if ssq_latest:
            ssq = ssq_latest[0]
            print(f"双色球数据库数据:")
            print(f"  期号: {ssq.get('issue', 'N/A')}")
            print(f"  日期: {ssq.get('date', 'N/A')}")
            print(f"  红球: {ssq.get('red_balls', 'N/A')}")
            print(f"  蓝球: {ssq.get('blue_ball', 'N/A')}")
            print(f"  完整数据: {json.dumps(ssq, ensure_ascii=False, indent=2)}")
        else:
            print("双色球数据库中没有数据")
        
        # 测试大乐透数据
        print("\n🔵 测试大乐透数据库...")
        dlt_data = DaletouDataAccess(db_manager)
        dlt_latest = dlt_data.get_latest_results(1)
        
        if dlt_latest:
            dlt = dlt_latest[0]
            print(f"大乐透数据库数据:")
            print(f"  期号: {dlt.get('issue', 'N/A')}")
            print(f"  日期: {dlt.get('date', 'N/A')}")
            print(f"  前区球: {dlt.get('front_balls', 'N/A')}")
            print(f"  后区球: {dlt.get('back_balls', 'N/A')}")
            print(f"  完整数据: {json.dumps(dlt, ensure_ascii=False, indent=2)}")
        else:
            print("大乐透数据库中没有数据")
        
        db_manager.close_all_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_update_api():
    """模拟更新API的数据格式"""
    print("\n🔄 模拟更新API数据格式...")
    
    # 模拟API返回的数据格式
    mock_response = {
        'status': 'success',
        'shuangseqiu': {
            'status': 'success',
            'data': {
                'issue': '2025001',
                'date': '2025-01-15',
                'red_balls': [3, 8, 16, 21, 28, 33],
                'blue_ball': 12
            },
            'saved_count': 3
        },
        'daletou': {
            'status': 'success',
            'data': {
                'issue': '25001',
                'date': '2025-01-15',
                'front_balls': [5, 12, 19, 26, 31],
                'back_balls': [3, 8]
            },
            'saved_count': 3
        },
        'algorithms': {
            'lstm': 85.2,
            'random_forest': 78.9,
            'svm': 72.4,
            'ensemble': 88.7
        },
        'timestamp': '2025-01-15T10:30:00',
        'message': '数据更新完成'
    }
    
    print("模拟的API响应格式:")
    print(json.dumps(mock_response, ensure_ascii=False, indent=2))
    
    # 验证数据格式
    print("\n✅ 数据格式验证:")
    
    # 验证双色球数据
    if mock_response['shuangseqiu']['data']:
        ssq = mock_response['shuangseqiu']['data']
        red_balls = ssq.get('red_balls', [])
        blue_ball = ssq.get('blue_ball', 0)
        
        print(f"双色球验证:")
        print(f"  红球数量: {len(red_balls)} (应为6)")
        print(f"  红球范围: {all(1 <= ball <= 33 for ball in red_balls)} (应为1-33)")
        print(f"  蓝球范围: {1 <= blue_ball <= 16} (应为1-16)")
    
    # 验证大乐透数据
    if mock_response['daletou']['data']:
        dlt = mock_response['daletou']['data']
        front_balls = dlt.get('front_balls', [])
        back_balls = dlt.get('back_balls', [])
        
        print(f"大乐透验证:")
        print(f"  前区球数量: {len(front_balls)} (应为5)")
        print(f"  前区球范围: {all(1 <= ball <= 35 for ball in front_balls)} (应为1-35)")
        print(f"  后区球数量: {len(back_balls)} (应为2)")
        print(f"  后区球范围: {all(1 <= ball <= 12 for ball in back_balls)} (应为1-12)")

def main():
    """主函数"""
    print("🐛 HuiCai更新功能调试工具")
    print("=" * 50)
    
    # 测试爬虫数据格式
    print("1️⃣ 测试爬虫数据格式")
    test_crawler_data_format()
    
    # 测试数据库数据
    print("\n2️⃣ 测试数据库数据")
    test_database_data()
    
    # 模拟API数据格式
    print("\n3️⃣ 模拟API数据格式")
    simulate_update_api()
    
    print("\n" + "=" * 50)
    print("🔧 调试建议:")
    print("1. 检查爬虫是否能正常获取数据")
    print("2. 检查数据库中是否有最新数据")
    print("3. 检查API返回的数据格式是否正确")
    print("4. 在浏览器开发者工具中查看控制台日志")
    print("5. 检查JavaScript中的数据更新函数")
    
    print("\n🌐 测试步骤:")
    print("1. 运行: python start_dashboard.py")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 点击'更新数据'按钮")
    print("4. 查看控制台日志和网络请求")

if __name__ == "__main__":
    main()
