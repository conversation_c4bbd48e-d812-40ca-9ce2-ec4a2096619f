# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/lite/profiling/proto/profiling_info.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4tensorflow/lite/profiling/proto/profiling_info.proto\x12\x10tflite.profiling\"\xa7\x01\n\x16\x42\x65nchmarkProfilingData\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12:\n\x0cinit_profile\x18\x02 \x01(\x0b\x32$.tflite.profiling.ModelProfilingData\x12=\n\x0fruntime_profile\x18\x03 \x01(\x0b\x32$.tflite.profiling.ModelProfilingData\"\x9c\x01\n\x12ModelProfilingData\x12\x42\n\x11subgraph_profiles\x18\x01 \x03(\x0b\x32\'.tflite.profiling.SubGraphProfilingData\x12\x42\n\x11\x64\x65legate_profiles\x18\x02 \x03(\x0b\x32\'.tflite.profiling.DelegateProfilingData\"\x80\x01\n\x15SubGraphProfilingData\x12\x15\n\rsubgraph_name\x18\x01 \x01(\t\x12\x16\n\x0esubgraph_index\x18\x02 \x01(\x05\x12\x38\n\x0fper_op_profiles\x18\x03 \x03(\x0b\x32\x1f.tflite.profiling.OpProfileData\"h\n\x15\x44\x65legateProfilingData\x12\x15\n\rdelegate_name\x18\x01 \x01(\t\x12\x38\n\x0fper_op_profiles\x18\x02 \x03(\x0b\x32\x1f.tflite.profiling.OpProfileData\"\x93\x01\n\x0fOpProfilingStat\x12\r\n\x05\x66irst\x18\x01 \x01(\x03\x12\x0c\n\x04last\x18\x02 \x01(\x03\x12\x0b\n\x03\x61vg\x18\x03 \x01(\x03\x12\x0e\n\x06stddev\x18\x04 \x01(\x02\x12\x10\n\x08variance\x18\x05 \x01(\x02\x12\x0b\n\x03min\x18\x06 \x01(\x03\x12\x0b\n\x03max\x18\x07 \x01(\x03\x12\x0b\n\x03sum\x18\x08 \x01(\x03\x12\r\n\x05\x63ount\x18\t \x01(\x03\"\xcf\x01\n\rOpProfileData\x12\x11\n\tnode_type\x18\x01 \x01(\t\x12\x41\n\x16inference_microseconds\x18\x02 \x01(\x0b\x32!.tflite.profiling.OpProfilingStat\x12\x31\n\x06mem_kb\x18\x03 \x01(\x0b\x32!.tflite.profiling.OpProfilingStat\x12\x14\n\x0ctimes_called\x18\x04 \x01(\x03\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x11\n\trun_order\x18\x06 \x01(\x03\x42\x02P\x01')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.lite.profiling.proto.profiling_info_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'P\001'
  _BENCHMARKPROFILINGDATA._serialized_start=75
  _BENCHMARKPROFILINGDATA._serialized_end=242
  _MODELPROFILINGDATA._serialized_start=245
  _MODELPROFILINGDATA._serialized_end=401
  _SUBGRAPHPROFILINGDATA._serialized_start=404
  _SUBGRAPHPROFILINGDATA._serialized_end=532
  _DELEGATEPROFILINGDATA._serialized_start=534
  _DELEGATEPROFILINGDATA._serialized_end=638
  _OPPROFILINGSTAT._serialized_start=641
  _OPPROFILINGSTAT._serialized_end=788
  _OPPROFILEDATA._serialized_start=791
  _OPPROFILEDATA._serialized_end=998
# @@protoc_insertion_point(module_scope)
