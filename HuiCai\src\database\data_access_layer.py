#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据访问层
为四个数据库提供专门的数据操作接口

Author: HuiCai Team
Date: 2025-01-15
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import logging
from .local_database_manager import LocalDatabaseManager


class ShuangseqiuDataAccess:
    """双色球数据访问类"""
    
    def __init__(self, db_manager: LocalDatabaseManager):
        self.db_manager = db_manager
        self.db_name = 'shuangseqiu_data'
        self.logger = logging.getLogger(__name__)
    
    def insert_draw_result(self, issue: str, date: str, red_balls: List[int], 
                          blue_ball: int, sales_amount: int = 0, 
                          pool_amount: int = 0, source: str = '') -> bool:
        """插入开奖结果"""
        try:
            query = '''
                INSERT OR REPLACE INTO draw_results 
                (issue, date, red_ball_1, red_ball_2, red_ball_3, red_ball_4, 
                 red_ball_5, red_ball_6, blue_ball, sales_amount, pool_amount, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (issue, date, *red_balls, blue_ball, sales_amount, pool_amount, source)
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"插入双色球开奖结果失败: {e}")
            return False
    
    def batch_insert_draw_results(self, results: List[Dict[str, Any]]) -> int:
        """批量插入开奖结果"""
        try:
            query = '''
                INSERT OR REPLACE INTO draw_results 
                (issue, date, red_ball_1, red_ball_2, red_ball_3, red_ball_4, 
                 red_ball_5, red_ball_6, blue_ball, sales_amount, pool_amount, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            params_list = []
            for result in results:
                red_balls = result.get('red_balls', [])
                if len(red_balls) == 6:
                    params = (
                        result.get('issue', ''),
                        result.get('date', ''),
                        *red_balls,
                        result.get('blue_ball', 0),
                        result.get('sales_amount', 0),
                        result.get('pool_amount', 0),
                        result.get('source', '')
                    )
                    params_list.append(params)
            
            return self.db_manager.execute_many(self.db_name, query, params_list)
            
        except Exception as e:
            self.logger.error(f"批量插入双色球开奖结果失败: {e}")
            return 0
    
    def get_latest_results(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最新开奖结果"""
        query = '''
            SELECT * FROM draw_results 
            ORDER BY date DESC, issue DESC 
            LIMIT ?
        '''
        return self.db_manager.execute_query(self.db_name, query, (limit,))
    
    def get_results_by_date_range(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """根据日期范围获取开奖结果"""
        query = '''
            SELECT * FROM draw_results 
            WHERE date BETWEEN ? AND ? 
            ORDER BY date DESC
        '''
        return self.db_manager.execute_query(self.db_name, query, (start_date, end_date))
    
    def get_ball_frequency(self, ball_type: str = 'red', days: int = 365) -> Dict[int, int]:
        """获取球号出现频率"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        if ball_type == 'red':
            query = '''
                SELECT red_ball_1 as ball FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT red_ball_2 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT red_ball_3 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT red_ball_4 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT red_ball_5 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT red_ball_6 FROM draw_results WHERE date BETWEEN ? AND ?
            '''
            params = (start_date, end_date) * 6
        else:  # blue
            query = '''
                SELECT blue_ball as ball FROM draw_results 
                WHERE date BETWEEN ? AND ?
            '''
            params = (start_date, end_date)
        
        results = self.db_manager.execute_query(self.db_name, query, params)
        
        frequency = {}
        for row in results:
            ball = row['ball']
            frequency[ball] = frequency.get(ball, 0) + 1
        
        return frequency
    
    def insert_prediction(self, prediction_id: str, algorithm: str, red_balls: List[int],
                         blue_ball: int, confidence: float, target_issue: str = '') -> bool:
        """插入预测记录"""
        try:
            query = '''
                INSERT INTO predictions 
                (prediction_id, algorithm, red_ball_1, red_ball_2, red_ball_3, 
                 red_ball_4, red_ball_5, red_ball_6, blue_ball, confidence, target_issue)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (prediction_id, algorithm, *red_balls, blue_ball, confidence, target_issue)
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"插入双色球预测记录失败: {e}")
            return False


class DaletouDataAccess:
    """大乐透数据访问类"""
    
    def __init__(self, db_manager: LocalDatabaseManager):
        self.db_manager = db_manager
        self.db_name = 'daletou_data'
        self.logger = logging.getLogger(__name__)
    
    def insert_draw_result(self, issue: str, date: str, front_balls: List[int], 
                          back_balls: List[int], sales_amount: int = 0, 
                          pool_amount: int = 0, source: str = '') -> bool:
        """插入开奖结果"""
        try:
            query = '''
                INSERT OR REPLACE INTO draw_results 
                (issue, date, front_ball_1, front_ball_2, front_ball_3, front_ball_4, 
                 front_ball_5, back_ball_1, back_ball_2, sales_amount, pool_amount, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (issue, date, *front_balls, *back_balls, sales_amount, pool_amount, source)
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"插入大乐透开奖结果失败: {e}")
            return False
    
    def batch_insert_draw_results(self, results: List[Dict[str, Any]]) -> int:
        """批量插入开奖结果"""
        try:
            query = '''
                INSERT OR REPLACE INTO draw_results 
                (issue, date, front_ball_1, front_ball_2, front_ball_3, front_ball_4, 
                 front_ball_5, back_ball_1, back_ball_2, sales_amount, pool_amount, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            params_list = []
            for result in results:
                front_balls = result.get('front_balls', [])
                back_balls = result.get('back_balls', [])
                if len(front_balls) == 5 and len(back_balls) == 2:
                    params = (
                        result.get('issue', ''),
                        result.get('date', ''),
                        *front_balls,
                        *back_balls,
                        result.get('sales_amount', 0),
                        result.get('pool_amount', 0),
                        result.get('source', '')
                    )
                    params_list.append(params)
            
            return self.db_manager.execute_many(self.db_name, query, params_list)
            
        except Exception as e:
            self.logger.error(f"批量插入大乐透开奖结果失败: {e}")
            return 0
    
    def get_latest_results(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最新开奖结果"""
        query = '''
            SELECT * FROM draw_results 
            ORDER BY date DESC, issue DESC 
            LIMIT ?
        '''
        return self.db_manager.execute_query(self.db_name, query, (limit,))
    
    def get_ball_frequency(self, ball_type: str = 'front', days: int = 365) -> Dict[int, int]:
        """获取球号出现频率"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        if ball_type == 'front':
            query = '''
                SELECT front_ball_1 as ball FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT front_ball_2 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT front_ball_3 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT front_ball_4 FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT front_ball_5 FROM draw_results WHERE date BETWEEN ? AND ?
            '''
            params = (start_date, end_date) * 5
        else:  # back
            query = '''
                SELECT back_ball_1 as ball FROM draw_results WHERE date BETWEEN ? AND ?
                UNION ALL SELECT back_ball_2 FROM draw_results WHERE date BETWEEN ? AND ?
            '''
            params = (start_date, end_date) * 2
        
        results = self.db_manager.execute_query(self.db_name, query, params)
        
        frequency = {}
        for row in results:
            ball = row['ball']
            frequency[ball] = frequency.get(ball, 0) + 1
        
        return frequency


class LearningDataAccess:
    """学习优化数据访问类"""
    
    def __init__(self, db_manager: LocalDatabaseManager, lottery_type: str):
        self.db_manager = db_manager
        self.lottery_type = lottery_type
        self.db_name = f'{lottery_type}_learning'
        self.logger = logging.getLogger(__name__)
    
    def save_model_parameter(self, model_name: str, parameter_name: str, 
                           parameter_value: Any, parameter_type: str = 'json') -> bool:
        """保存模型参数"""
        try:
            if parameter_type == 'json':
                value_str = json.dumps(parameter_value)
            else:
                value_str = str(parameter_value)
            
            query = '''
                INSERT OR REPLACE INTO model_parameters 
                (model_name, parameter_name, parameter_value, parameter_type)
                VALUES (?, ?, ?, ?)
            '''
            params = (model_name, parameter_name, value_str, parameter_type)
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"保存模型参数失败: {e}")
            return False
    
    def get_model_parameters(self, model_name: str) -> Dict[str, Any]:
        """获取模型参数"""
        query = '''
            SELECT parameter_name, parameter_value, parameter_type 
            FROM model_parameters 
            WHERE model_name = ?
            ORDER BY created_at DESC
        '''
        results = self.db_manager.execute_query(self.db_name, query, (model_name,))
        
        parameters = {}
        for row in results:
            name = row['parameter_name']
            value = row['parameter_value']
            param_type = row['parameter_type']
            
            if param_type == 'json':
                try:
                    parameters[name] = json.loads(value)
                except:
                    parameters[name] = value
            else:
                parameters[name] = value
        
        return parameters
    
    def record_learning_history(self, model_name: str, learning_type: str,
                              input_data: Any, output_data: Any,
                              accuracy: float = None, loss: float = None,
                              epoch: int = None) -> bool:
        """记录学习历史"""
        try:
            query = '''
                INSERT INTO learning_history 
                (model_name, learning_type, input_data, output_data, accuracy, loss, epoch)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                model_name, learning_type,
                json.dumps(input_data), json.dumps(output_data),
                accuracy, loss, epoch
            )
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"记录学习历史失败: {e}")
            return False
    
    def save_optimization_strategy(self, strategy_name: str, strategy_config: Dict[str, Any],
                                 performance_score: float = 0.0) -> bool:
        """保存优化策略"""
        try:
            query = '''
                INSERT OR REPLACE INTO optimization_strategies 
                (strategy_name, strategy_config, performance_score, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            '''
            params = (strategy_name, json.dumps(strategy_config), performance_score)
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"保存优化策略失败: {e}")
            return False
    
    def get_best_strategies(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最佳优化策略"""
        query = '''
            SELECT * FROM optimization_strategies 
            ORDER BY performance_score DESC 
            LIMIT ?
        '''
        return self.db_manager.execute_query(self.db_name, query, (limit,))
    
    def update_knowledge_base(self, knowledge_type: str, knowledge_key: str,
                            knowledge_value: Any, confidence: float = 1.0,
                            source: str = '') -> bool:
        """更新知识库"""
        try:
            query = '''
                INSERT OR REPLACE INTO knowledge_base 
                (knowledge_type, knowledge_key, knowledge_value, confidence, source, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            '''
            params = (
                knowledge_type, knowledge_key,
                json.dumps(knowledge_value) if not isinstance(knowledge_value, str) else knowledge_value,
                confidence, source
            )
            
            self.db_manager.execute_update(self.db_name, query, params)
            return True
            
        except Exception as e:
            self.logger.error(f"更新知识库失败: {e}")
            return False
    
    def get_knowledge(self, knowledge_type: str, knowledge_key: str = None) -> List[Dict[str, Any]]:
        """获取知识"""
        if knowledge_key:
            query = '''
                SELECT * FROM knowledge_base 
                WHERE knowledge_type = ? AND knowledge_key = ?
                ORDER BY confidence DESC
            '''
            params = (knowledge_type, knowledge_key)
        else:
            query = '''
                SELECT * FROM knowledge_base 
                WHERE knowledge_type = ?
                ORDER BY confidence DESC
            '''
            params = (knowledge_type,)
        
        return self.db_manager.execute_query(self.db_name, query, params)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    db_manager = LocalDatabaseManager()
    
    # 测试双色球数据访问
    ssq_data = ShuangseqiuDataAccess(db_manager)
    print("双色球数据访问测试完成")
    
    # 测试大乐透数据访问
    dlt_data = DaletouDataAccess(db_manager)
    print("大乐透数据访问测试完成")
    
    # 测试学习数据访问
    ssq_learning = LearningDataAccess(db_manager, 'shuangseqiu')
    dlt_learning = LearningDataAccess(db_manager, 'daletou')
    print("学习数据访问测试完成")
    
    db_manager.close_all_connections()
