#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试测试脚本
直接测试官方API接口

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
import json
import time

def test_ssq_official_api():
    """测试双色球官方API"""
    print("🔴 测试双色球官方API...")
    
    # 方法1: POST with form data
    print("  方法1: POST with form data")
    try:
        url = 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice'
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.cwl.gov.cn/kjxx/ssq/',
            'Origin': 'https://www.cwl.gov.cn',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        data = {
            'name': 'ssq',
            'issueCount': '5'
        }
        
        response = requests.post(url, data=data, headers=headers, timeout=10)
        print(f"    状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"    响应类型: JSON")
                print(f"    响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
                if isinstance(result, dict) and 'result' in result:
                    print(f"    数据条数: {len(result['result']) if isinstance(result['result'], list) else 'Not list'}")
            except:
                print(f"    响应类型: 非JSON")
                print(f"    响应内容前200字符: {response.text[:200]}")
        else:
            print(f"    错误响应: {response.text[:200]}")
    except Exception as e:
        print(f"    异常: {e}")
    
    time.sleep(2)
    
    # 方法2: GET with params
    print("  方法2: GET with params")
    try:
        url = 'https://www.cwl.gov.cn/cwl_admin/kjxx/findDrawNotice'
        headers = {
            'Accept': 'application/json',
            'Referer': 'https://www.cwl.gov.cn/kjxx/ssq/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        params = {
            'name': 'ssq',
            'issueCount': '5'
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"    状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"    响应类型: JSON")
                print(f"    响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
            except:
                print(f"    响应类型: 非JSON")
                print(f"    响应内容前200字符: {response.text[:200]}")
        else:
            print(f"    错误响应: {response.text[:200]}")
    except Exception as e:
        print(f"    异常: {e}")

def test_dlt_official_api():
    """测试大乐透官方API"""
    print("\n🔵 测试大乐透官方API...")
    
    # 方法1: POST with JSON
    print("  方法1: POST with JSON")
    try:
        url = 'https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry'
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json, text/plain, */*',
            'Referer': 'https://www.sporttery.cn/lottery/dlt/',
            'Origin': 'https://www.sporttery.cn',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        data = {
            'gameNo': '85',
            'provinceId': '0',
            'pageSize': 5,
            'isVerify': 1
        }
        
        response = requests.post(url, json=data, headers=headers, timeout=10)
        print(f"    状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"    响应类型: JSON")
                print(f"    响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
                if isinstance(result, dict) and 'value' in result:
                    value = result['value']
                    if isinstance(value, dict) and 'list' in value:
                        print(f"    数据条数: {len(value['list'])}")
                        if value['list']:
                            first_item = value['list'][0]
                            print(f"    第一条数据键: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not dict'}")
            except:
                print(f"    响应类型: 非JSON")
                print(f"    响应内容前200字符: {response.text[:200]}")
        else:
            print(f"    错误响应: {response.text[:200]}")
    except Exception as e:
        print(f"    异常: {e}")
    
    time.sleep(2)
    
    # 方法2: GET with params
    print("  方法2: GET with params")
    try:
        url = 'https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry'
        headers = {
            'Accept': 'application/json',
            'Referer': 'https://www.sporttery.cn/lottery/dlt/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        params = {
            'gameNo': '85',
            'provinceId': '0',
            'pageSize': 5,
            'isVerify': 1
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"    状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"    响应类型: JSON")
                print(f"    响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
            except:
                print(f"    响应类型: 非JSON")
                print(f"    响应内容前200字符: {response.text[:200]}")
        else:
            print(f"    错误响应: {response.text[:200]}")
    except Exception as e:
        print(f"    异常: {e}")

def test_third_party_api():
    """测试第三方API"""
    print("\n🌐 测试第三方API...")
    
    try:
        url = 'https://api.apiopen.top/api/lottery'
        headers = {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        params = {
            'type': 'ssq',
            'count': 3
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"  响应类型: JSON")
                print(f"  响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
                if isinstance(result, dict) and 'result' in result:
                    print(f"  数据条数: {len(result['result']) if isinstance(result['result'], list) else 'Not list'}")
                    if isinstance(result['result'], list) and result['result']:
                        first_item = result['result'][0]
                        print(f"  第一条数据键: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not dict'}")
            except:
                print(f"  响应类型: 非JSON")
                print(f"  响应内容前200字符: {response.text[:200]}")
        else:
            print(f"  错误响应: {response.text[:200]}")
    except Exception as e:
        print(f"  异常: {e}")

def main():
    """主函数"""
    print("🎯 API调试测试")
    print("="*50)
    
    # 测试双色球官方API
    test_ssq_official_api()
    
    # 测试大乐透官方API
    test_dlt_official_api()
    
    # 测试第三方API
    test_third_party_api()
    
    print("\n✅ API测试完成")
    print("="*50)

if __name__ == "__main__":
    main()
