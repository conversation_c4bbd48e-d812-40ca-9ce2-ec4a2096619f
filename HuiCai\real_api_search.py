#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实彩票API搜索测试脚本
测试各种可能的彩票数据源

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
import json
import time
from urllib.parse import urlencode

def test_api_source(name, url, method='GET', headers=None, params=None, data=None, json_data=None):
    """测试单个API源"""
    print(f"\n🔍 测试 {name}")
    print(f"   URL: {url}")
    print(f"   方法: {method}")
    
    if headers is None:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, params=params, headers=headers, timeout=15, verify=False)
        elif method.upper() == 'POST':
            if json_data:
                response = requests.post(url, json=json_data, headers=headers, timeout=15, verify=False)
            else:
                response = requests.post(url, data=data, headers=headers, timeout=15, verify=False)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            
            if 'json' in content_type:
                try:
                    result = response.json()
                    print(f"   ✅ JSON响应成功")
                    print(f"   响应键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
                    
                    # 尝试找到数据
                    data_found = False
                    for key in ['result', 'data', 'list', 'items', 'records']:
                        if key in result and isinstance(result[key], list) and len(result[key]) > 0:
                            print(f"   📊 找到数据字段: {key}, 数量: {len(result[key])}")
                            first_item = result[key][0]
                            if isinstance(first_item, dict):
                                print(f"   第一条数据键: {list(first_item.keys())}")
                                print(f"   第一条数据: {json.dumps(first_item, ensure_ascii=False)[:200]}...")
                            data_found = True
                            break
                    
                    if not data_found:
                        print(f"   完整响应: {json.dumps(result, ensure_ascii=False)[:500]}...")
                    
                    return True, result
                except:
                    print(f"   ❌ JSON解析失败")
                    print(f"   响应内容: {response.text[:300]}...")
            else:
                print(f"   📄 HTML/文本响应")
                text = response.text
                if '双色球' in text or 'ssq' in text.lower() or '大乐透' in text or 'dlt' in text.lower():
                    print(f"   ✅ 包含彩票相关内容")
                    print(f"   内容片段: {text[:200]}...")
                    return True, text
                else:
                    print(f"   ❌ 不包含彩票内容")
                    print(f"   内容片段: {text[:200]}...")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            print(f"   错误内容: {response.text[:200]}...")
        
        return False, None
        
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False, None

def test_all_sources():
    """测试所有可能的数据源"""
    print("🎯 全网彩票API搜索测试")
    print("=" * 60)
    
    # 测试源列表
    sources = [
        # 官方源
        {
            'name': '福彩官网-双色球',
            'url': 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
            'method': 'POST',
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Referer': 'https://www.cwl.gov.cn/kjxx/ssq/',
                'X-Requested-With': 'XMLHttpRequest'
            },
            'data': {'name': 'ssq', 'issueCount': '5'}
        },
        {
            'name': '体彩官网-大乐透',
            'url': 'https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry',
            'method': 'POST',
            'headers': {'Content-Type': 'application/json'},
            'json_data': {'gameNo': '85', 'provinceId': '0', 'pageSize': 5, 'isVerify': 1}
        },
        
        # 第三方API
        {
            'name': 'API开放平台',
            'url': 'https://api.apiopen.top/api/lottery',
            'params': {'type': 'ssq', 'count': 5}
        },
        {
            'name': '聚合数据API',
            'url': 'http://apis.juhe.cn/lottery/query',
            'params': {'lottery_id': 'ssq', 'lottery_no': '', 'key': 'your_key'}
        },
        {
            'name': '天行数据API',
            'url': 'https://api.tianapi.com/txapi/ssq/index',
            'params': {'key': 'your_key', 'num': 5}
        },
        
        # 彩票网站API
        {
            'name': '500彩票网API',
            'url': 'https://datachart.500.com/ssq/history/newinc/history.php',
            'params': {'start': '24001', 'end': '24010'}
        },
        {
            'name': '新浪彩票API',
            'url': 'https://interface.sina.cn/pc_api/lottery_data.d.json',
            'params': {'lottery_type': 'ssq', 'num': 5}
        },
        {
            'name': '网易彩票API',
            'url': 'https://caipiao.163.com/award/ssq/list.html',
            'params': {'pageNo': 1, 'pageSize': 5}
        },
        {
            'name': '搜狐彩票API',
            'url': 'https://zx.caipiao.sohu.com/lottery/ssqNew.action',
            'params': {'num': 5}
        },
        
        # 其他可能的源
        {
            'name': '彩票宝API',
            'url': 'https://api.caipiaoapi.com/lottery/ssq',
            'params': {'count': 5}
        },
        {
            'name': '彩票助手API',
            'url': 'https://api.lottery-helper.com/ssq/latest',
            'params': {'limit': 5}
        },
        {
            'name': '开奖网API',
            'url': 'https://api.kaijiang.com/ssq/history',
            'params': {'count': 5}
        },
        {
            'name': '彩经网API',
            'url': 'https://api.caijing.com.cn/lottery/ssq',
            'params': {'num': 5}
        },
        
        # 尝试不同的URL格式
        {
            'name': '福彩中心备用API',
            'url': 'https://kaijiang.zhcw.com/zhcw/incl/ssq_wqhg.jsp',
            'params': {'pageNum': 1}
        },
        {
            'name': '体彩中心备用API',
            'url': 'https://www.lottery.gov.cn/api/lottery_kj_detail_new.jspx',
            'params': {'name': 'dlt', 'issueCount': 5}
        },
        
        # 移动端API
        {
            'name': '福彩移动端API',
            'url': 'https://m.cwl.gov.cn/api/lottery/ssq',
            'params': {'count': 5}
        },
        {
            'name': '体彩移动端API',
            'url': 'https://m.sporttery.cn/api/lottery/dlt',
            'params': {'count': 5}
        },
        
        # JSON数据文件
        {
            'name': '静态JSON数据',
            'url': 'https://raw.githubusercontent.com/lottery-data/china-lottery/main/ssq.json'
        },
        
        # 尝试JSONP格式
        {
            'name': 'JSONP格式API',
            'url': 'https://api.lottery.com/jsonp/ssq',
            'params': {'callback': 'jsonp', 'count': 5}
        }
    ]
    
    successful_sources = []
    
    for i, source in enumerate(sources, 1):
        print(f"\n[{i}/{len(sources)}]", end="")
        success, data = test_api_source(**source)
        
        if success:
            successful_sources.append({
                'name': source['name'],
                'url': source['url'],
                'method': source.get('method', 'GET'),
                'data': data
            })
        
        # 避免请求过快
        time.sleep(1)
    
    # 总结结果
    print("\n" + "=" * 60)
    print(f"📊 测试完成，成功源数量: {len(successful_sources)}")
    
    if successful_sources:
        print("\n✅ 可用的数据源:")
        for i, source in enumerate(successful_sources, 1):
            print(f"  {i}. {source['name']}")
            print(f"     URL: {source['url']}")
            print(f"     方法: {source['method']}")
    else:
        print("\n❌ 未找到可用的数据源")
        print("建议:")
        print("  1. 检查网络连接")
        print("  2. 尝试使用代理")
        print("  3. 查看是否需要API密钥")
        print("  4. 考虑使用爬虫抓取HTML页面")
    
    return successful_sources

if __name__ == "__main__":
    # 忽略SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    successful_sources = test_all_sources()
    
    if successful_sources:
        print(f"\n🎉 找到 {len(successful_sources)} 个可用数据源！")
        print("接下来可以基于这些源开发爬虫。")
    else:
        print("\n😞 没有找到可用的API源，可能需要:")
        print("  - 使用HTML解析方式")
        print("  - 申请API密钥")
        print("  - 使用代理服务器")
        print("  - 寻找其他数据源")
