crawler:
  concurrent_limit: 5
  request_delay: 1
  retry_times: 3
  timeout: 30
  user_agents:
  - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
database:
  backup_enabled: true
  path: data/huicai.db
  postgresql:
    host: localhost
    name: huicai
    password: huicai123
    pool_size: 10
    port: 5432
    user: huicai
  type: sqlite
deep_learning:
  enabled: true
  enabled_models:
  - lstm
  - transformer
  ensemble:
    method: weighted_voting
    weight_update: performance_based
  lstm:
    batch_size: 32
    dropout_rate: 0.2
    epochs: 100
    hidden_units: 128
    learning_rate: 0.001
    num_layers: 2
    sequence_length: 20
  reinforcement_learning:
    episodes: 1000
    epsilon_decay: 0.995
    gamma: 0.95
    learning_rate: 0.001
    update_target_freq: 100
  transformer:
    batch_size: 32
    d_model: 128
    dff: 512
    dropout_rate: 0.1
    epochs: 100
    learning_rate: 0.0001
    num_heads: 8
    num_layers: 4
    sequence_length: 30
learning:
  batch_size: 32
  early_stopping_patience: 10
  epochs: 100
  learning_rate: 0.001
  model_save_path: data/models
  validation_split: 0.2
logging:
  backup_count: 5
  file_path: data/logs/huicai.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_file_size: 10MB
redis:
  db: 0
  host: localhost
  max_connections: 20
  password: null
  port: 6379
scheduler:
  job_defaults:
    coalesce: false
    max_instances: 1
  max_workers: 4
  timezone: Asia/Shanghai
system:
  debug: true
  name: HuiCai
  timezone: Asia/Shanghai
  version: 1.0.0
