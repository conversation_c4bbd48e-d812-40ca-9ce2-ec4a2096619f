#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习管理器
统一管理所有深度学习模型的训练、预测和集成

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import pandas as pd
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, date
import os

from .lstm_model import LSTMPredictor
from .transformer_model import TransformerPredictor
from .reinforcement_learning import ReinforcementLearningPredictor
from .ensemble_models import EnsemblePredictor


class DeepLearningManager:
    """深度学习管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化深度学习管理器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 深度学习配置
        self.dl_config = config_manager.get('deep_learning', {})
        self.enabled_models = self.dl_config.get('enabled_models', ['lstm', 'transformer'])
        
        # 模型实例
        self.models = {}
        self.ensemble_predictors = {}
        
        # 训练状态
        self.training_status = {}
        
        # 数据存储引用
        self.data_storage = None
        
        self.logger.info("深度学习管理器初始化完成")
    
    async def initialize(self):
        """初始化深度学习管理器"""
        try:
            # 导入数据存储
            from ..data_layer.data_storage import DataStorage
            self.data_storage = DataStorage(self.config_manager, self.logger)
            await self.data_storage.initialize()
            
            # 初始化各彩票类型的模型
            lottery_configs = self.config_manager.lottery_configs
            
            for lottery_type in lottery_configs.keys():
                await self._initialize_lottery_models(lottery_type)
            
            self.logger.info("深度学习管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"深度学习管理器初始化失败: {e}")
            raise
    
    async def _initialize_lottery_models(self, lottery_type: str):
        """初始化指定彩票类型的模型"""
        try:
            self.models[lottery_type] = {}
            
            # 初始化LSTM模型
            if 'lstm' in self.enabled_models:
                lstm_predictor = LSTMPredictor(self.config_manager, self.logger, lottery_type)
                lstm_predictor.load_model_components()  # 尝试加载已有模型
                self.models[lottery_type]['lstm'] = lstm_predictor
            
            # 初始化Transformer模型
            if 'transformer' in self.enabled_models:
                transformer_predictor = TransformerPredictor(self.config_manager, self.logger, lottery_type)
                transformer_predictor.load_model_components()
                self.models[lottery_type]['transformer'] = transformer_predictor
            
            # 初始化强化学习模型
            if 'reinforcement_learning' in self.enabled_models:
                rl_predictor = ReinforcementLearningPredictor(self.config_manager, self.logger, lottery_type)
                rl_predictor.load_model_components()
                self.models[lottery_type]['reinforcement_learning'] = rl_predictor
            
            # 初始化集成预测器
            ensemble_predictor = EnsemblePredictor(self.config_manager, self.logger, lottery_type)
            ensemble_predictor.load_ensemble_state()
            self.ensemble_predictors[lottery_type] = ensemble_predictor
            
            # 初始化训练状态
            self.training_status[lottery_type] = {
                'lstm': {'trained': False, 'training': False},
                'transformer': {'trained': False, 'training': False},
                'reinforcement_learning': {'trained': False, 'training': False}
            }
            
            self.logger.info(f"{lottery_type} 深度学习模型初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化 {lottery_type} 模型失败: {e}")
    
    async def train_all_models(self, lottery_type: str, force_retrain: bool = False) -> Dict[str, Any]:
        """
        训练指定彩票类型的所有模型
        
        Args:
            lottery_type: 彩票类型
            force_retrain: 是否强制重新训练
            
        Returns:
            Dict: 训练结果
        """
        try:
            self.logger.info(f"开始训练 {lottery_type} 的所有深度学习模型")
            
            # 获取训练数据
            training_data = await self._get_training_data(lottery_type)
            if training_data.empty:
                self.logger.warning(f"{lottery_type} 没有足够的训练数据")
                return {}
            
            training_results = {}
            
            # 训练LSTM模型
            if 'lstm' in self.models[lottery_type]:
                if force_retrain or not self.training_status[lottery_type]['lstm']['trained']:
                    self.training_status[lottery_type]['lstm']['training'] = True
                    try:
                        lstm_result = await self._train_lstm(lottery_type, training_data)
                        training_results['lstm'] = lstm_result
                        self.training_status[lottery_type]['lstm']['trained'] = True
                    except Exception as e:
                        self.logger.error(f"LSTM训练失败: {e}")
                        training_results['lstm'] = {'error': str(e)}
                    finally:
                        self.training_status[lottery_type]['lstm']['training'] = False
            
            # 训练Transformer模型
            if 'transformer' in self.models[lottery_type]:
                if force_retrain or not self.training_status[lottery_type]['transformer']['trained']:
                    self.training_status[lottery_type]['transformer']['training'] = True
                    try:
                        transformer_result = await self._train_transformer(lottery_type, training_data)
                        training_results['transformer'] = transformer_result
                        self.training_status[lottery_type]['transformer']['trained'] = True
                    except Exception as e:
                        self.logger.error(f"Transformer训练失败: {e}")
                        training_results['transformer'] = {'error': str(e)}
                    finally:
                        self.training_status[lottery_type]['transformer']['training'] = False
            
            # 训练强化学习模型
            if 'reinforcement_learning' in self.models[lottery_type]:
                if force_retrain or not self.training_status[lottery_type]['reinforcement_learning']['trained']:
                    self.training_status[lottery_type]['reinforcement_learning']['training'] = True
                    try:
                        rl_result = await self._train_reinforcement_learning(lottery_type, training_data)
                        training_results['reinforcement_learning'] = rl_result
                        self.training_status[lottery_type]['reinforcement_learning']['trained'] = True
                    except Exception as e:
                        self.logger.error(f"强化学习训练失败: {e}")
                        training_results['reinforcement_learning'] = {'error': str(e)}
                    finally:
                        self.training_status[lottery_type]['reinforcement_learning']['training'] = False
            
            self.logger.info(f"{lottery_type} 所有模型训练完成")
            return training_results
            
        except Exception as e:
            self.logger.error(f"训练 {lottery_type} 模型失败: {e}")
            return {}
    
    async def _get_training_data(self, lottery_type: str) -> pd.DataFrame:
        """获取训练数据"""
        try:
            # 获取最近一年的数据
            end_date = date.today()
            start_date = date(end_date.year - 1, end_date.month, end_date.day)
            
            draws = await self.data_storage.get_lottery_draws(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )
            
            if not draws:
                return pd.DataFrame()
            
            df = pd.DataFrame(draws)
            df['draw_date'] = pd.to_datetime(df['draw_date'])
            df = df.sort_values('draw_date')
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {lottery_type} 训练数据失败: {e}")
            return pd.DataFrame()
    
    async def _train_lstm(self, lottery_type: str, data: pd.DataFrame) -> Dict[str, Any]:
        """训练LSTM模型"""
        lstm_predictor = self.models[lottery_type]['lstm']
        
        # 在线程池中运行训练（避免阻塞）
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, 
            lambda: lstm_predictor.train(data, use_attention=True, use_multi_features=True)
        )
        
        return result
    
    async def _train_transformer(self, lottery_type: str, data: pd.DataFrame) -> Dict[str, Any]:
        """训练Transformer模型"""
        transformer_predictor = self.models[lottery_type]['transformer']
        
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: transformer_predictor.train(data)
        )
        
        return result
    
    async def _train_reinforcement_learning(self, lottery_type: str, data: pd.DataFrame) -> Dict[str, Any]:
        """训练强化学习模型"""
        rl_predictor = self.models[lottery_type]['reinforcement_learning']
        
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: rl_predictor.train(data)
        )
        
        return result
    
    async def predict_with_deep_learning(self, lottery_type: str) -> Dict[str, Any]:
        """
        使用深度学习模型进行预测
        
        Args:
            lottery_type: 彩票类型
            
        Returns:
            Dict: 预测结果
        """
        try:
            self.logger.info(f"开始深度学习预测: {lottery_type}")
            
            # 获取最近数据
            recent_data = await self._get_recent_data(lottery_type)
            if recent_data.empty:
                self.logger.warning(f"{lottery_type} 没有足够的预测数据")
                return {}
            
            # 收集各模型的预测结果
            predictions = {}
            
            # LSTM预测
            if 'lstm' in self.models[lottery_type]:
                try:
                    lstm_pred = self.models[lottery_type]['lstm'].predict(recent_data)
                    if lstm_pred:
                        predictions['lstm'] = lstm_pred
                except Exception as e:
                    self.logger.error(f"LSTM预测失败: {e}")
            
            # Transformer预测
            if 'transformer' in self.models[lottery_type]:
                try:
                    transformer_pred = self.models[lottery_type]['transformer'].predict(recent_data)
                    if transformer_pred:
                        predictions['transformer'] = transformer_pred
                except Exception as e:
                    self.logger.error(f"Transformer预测失败: {e}")
            
            # 强化学习预测
            if 'reinforcement_learning' in self.models[lottery_type]:
                try:
                    rl_pred = self.models[lottery_type]['reinforcement_learning'].predict(recent_data)
                    if rl_pred:
                        predictions['reinforcement_learning'] = rl_pred
                except Exception as e:
                    self.logger.error(f"强化学习预测失败: {e}")
            
            if not predictions:
                self.logger.warning(f"{lottery_type} 所有深度学习模型预测都失败")
                return {}
            
            # 集成预测
            ensemble_predictor = self.ensemble_predictors[lottery_type]
            ensemble_result = ensemble_predictor.ensemble_predict(predictions)
            
            # 添加个体预测信息
            ensemble_result['individual_predictions'] = predictions
            ensemble_result['model_count'] = len(predictions)
            
            return ensemble_result
            
        except Exception as e:
            self.logger.error(f"深度学习预测失败: {e}")
            return {}
    
    async def _get_recent_data(self, lottery_type: str, days: int = 30) -> pd.DataFrame:
        """获取最近的数据"""
        try:
            end_date = date.today()
            start_date = date(end_date.year, end_date.month, end_date.day - days)
            
            draws = await self.data_storage.get_lottery_draws(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date,
                limit=100
            )
            
            if not draws:
                return pd.DataFrame()
            
            df = pd.DataFrame(draws)
            df['draw_date'] = pd.to_datetime(df['draw_date'])
            df = df.sort_values('draw_date')
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {lottery_type} 最近数据失败: {e}")
            return pd.DataFrame()
    
    def get_model_info(self, lottery_type: str) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            if lottery_type not in self.models:
                return {}
            
            model_info = {
                'lottery_type': lottery_type,
                'enabled_models': self.enabled_models,
                'training_status': self.training_status.get(lottery_type, {}),
                'models': {}
            }
            
            # 收集各模型信息
            for model_name, model in self.models[lottery_type].items():
                try:
                    info = model.get_model_info()
                    model_info['models'][model_name] = info
                except Exception as e:
                    model_info['models'][model_name] = {'error': str(e)}
            
            # 集成器信息
            if lottery_type in self.ensemble_predictors:
                ensemble_stats = self.ensemble_predictors[lottery_type].get_ensemble_statistics()
                model_info['ensemble'] = ensemble_stats
            
            return model_info
            
        except Exception as e:
            self.logger.error(f"获取 {lottery_type} 模型信息失败: {e}")
            return {}
    
    def get_all_models_info(self) -> Dict[str, Any]:
        """获取所有模型信息"""
        all_info = {}
        
        for lottery_type in self.models.keys():
            all_info[lottery_type] = self.get_model_info(lottery_type)
        
        return all_info
    
    async def update_ensemble_weights(self, lottery_type: str, performance_data: Dict[str, float]):
        """更新集成权重"""
        try:
            if lottery_type in self.ensemble_predictors:
                ensemble_predictor = self.ensemble_predictors[lottery_type]
                ensemble_predictor.update_model_weights(performance_data)
                ensemble_predictor.save_ensemble_state()
                
                self.logger.info(f"{lottery_type} 集成权重已更新")
                
        except Exception as e:
            self.logger.error(f"更新 {lottery_type} 集成权重失败: {e}")
    
    async def evaluate_models_performance(self, lottery_type: str, actual_numbers: List[int]) -> Dict[str, Any]:
        """评估模型性能"""
        try:
            # 获取最近的预测结果（这里简化处理）
            recent_data = await self._get_recent_data(lottery_type, days=7)
            
            if recent_data.empty:
                return {}
            
            # 进行预测
            predictions = await self.predict_with_deep_learning(lottery_type)
            
            if not predictions:
                return {}
            
            # 评估集成性能
            ensemble_predictor = self.ensemble_predictors[lottery_type]
            evaluation = ensemble_predictor.evaluate_ensemble_performance(predictions, actual_numbers)
            
            return evaluation
            
        except Exception as e:
            self.logger.error(f"评估 {lottery_type} 模型性能失败: {e}")
            return {}
    
    async def close(self):
        """关闭深度学习管理器"""
        try:
            # 保存所有集成状态
            for lottery_type, ensemble_predictor in self.ensemble_predictors.items():
                ensemble_predictor.save_ensemble_state()
            
            # 关闭数据存储
            if self.data_storage:
                await self.data_storage.close()
            
            self.logger.info("深度学习管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭深度学习管理器失败: {e}")
    
    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        return {
            'training_status': self.training_status,
            'enabled_models': self.enabled_models,
            'total_models': sum(len(models) for models in self.models.values()),
            'total_ensembles': len(self.ensemble_predictors)
        }
