#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量学习器
实现模型的增量学习和自适应更新

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, date, timedelta
import pickle
import json
from pathlib import Path
import hashlib

from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import SGDClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report

# 导入中国特色算法
from ..algorithm_layer.chinese_algorithms import ChineseAlgorithms


class IncrementalLearner:
    """增量学习器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化增量学习器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 学习配置
        self.learning_config = config_manager.get('learning', {})
        self.model_save_path = Path(self.learning_config.get('model_save_path', 'data/models'))
        self.model_save_path.mkdir(parents=True, exist_ok=True)
        
        # 模型字典 - 每个彩票类型对应一个模型
        self.models: Dict[str, Dict[str, Any]] = {}
        
        # 数据缓存
        self.data_cache: Dict[str, pd.DataFrame] = {}
        
        # 学习参数
        self.batch_size = self.learning_config.get('batch_size', 32)
        self.learning_rate = self.learning_config.get('learning_rate', 0.001)
        
        # 状态标志
        self.is_running = False
        
        # 数据存储引用
        self.data_storage = None

        # 中国特色算法
        self.chinese_algorithms = ChineseAlgorithms(config_manager, logger)
    
    async def initialize(self):
        """初始化增量学习器"""
        try:
            # 导入数据存储
            from ..data_layer.data_storage import DataStorage
            self.data_storage = DataStorage(self.config_manager, self.logger)
            await self.data_storage.initialize()
            
            # 加载已有模型
            await self._load_existing_models()
            
            # 初始化新模型
            await self._initialize_models()
            
            self.logger.info("增量学习器初始化完成")
            
        except Exception as e:
            self.logger.error(f"增量学习器初始化失败: {e}")
            raise
    
    async def start(self):
        """启动增量学习器"""
        if self.is_running:
            self.logger.warning("增量学习器已在运行")
            return
        
        self.is_running = True
        self.logger.info("启动增量学习器")
        
        # 启动定期学习任务
        asyncio.create_task(self._periodic_learning_task())
    
    async def stop(self):
        """停止增量学习器"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.logger.info("停止增量学习器")
        
        # 保存所有模型
        await self._save_all_models()
        
        # 关闭数据存储
        if self.data_storage:
            await self.data_storage.close()
    
    async def _load_existing_models(self):
        """加载已有模型"""
        try:
            for model_file in self.model_save_path.glob("*.pkl"):
                lottery_type = model_file.stem.replace('_model', '')
                
                try:
                    with open(model_file, 'rb') as f:
                        model_data = pickle.load(f)
                    
                    self.models[lottery_type] = model_data
                    self.logger.info(f"加载 {lottery_type} 模型成功")
                    
                except Exception as e:
                    self.logger.error(f"加载 {lottery_type} 模型失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
    
    async def _initialize_models(self):
        """初始化新模型"""
        # 获取所有彩票类型
        lottery_configs = self.config_manager.lottery_configs
        
        for lottery_type in lottery_configs.keys():
            if lottery_type not in self.models:
                # 创建新模型
                model_data = self._create_new_model(lottery_type)
                self.models[lottery_type] = model_data
                self.logger.info(f"初始化 {lottery_type} 新模型")
    
    def _create_new_model(self, lottery_type: str) -> Dict[str, Any]:
        """创建新模型"""
        # 获取彩票配置
        lottery_config = self.config_manager.get_lottery_config(lottery_type)
        
        # 创建模型组件
        model_data = {
            'lottery_type': lottery_type,
            'created_at': datetime.now(),
            'last_updated': datetime.now(),
            'version': '1.0.0',
            'data_count': 0,
            'accuracy': 0.0,
            
            # 主模型 - 随机森林（用于稳定预测）
            'main_model': RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                n_jobs=-1
            ),
            
            # 增量模型 - SGD分类器（用于在线学习）
            'incremental_model': SGDClassifier(
                loss='log_loss',
                learning_rate='adaptive',
                eta0=self.learning_rate,
                random_state=42
            ),
            
            # 数据预处理器
            'scaler': StandardScaler(),
            
            # 特征配置
            'feature_config': self._get_feature_config(lottery_type),
            
            # 训练历史
            'training_history': [],
            
            # 预测历史
            'prediction_history': []
        }
        
        return model_data
    
    def _get_feature_config(self, lottery_type: str) -> Dict[str, Any]:
        """获取特征配置"""
        lottery_config = self.config_manager.get_lottery_config(lottery_type)
        
        if lottery_type == 'shuangseqiu':
            return {
                'red_ball_range': lottery_config.get('red_ball_range', [1, 33]),
                'red_ball_count': lottery_config.get('red_ball_count', 6),
                'blue_ball_range': lottery_config.get('blue_ball_range', [1, 16]),
                'blue_ball_count': lottery_config.get('blue_ball_count', 1),
                'feature_window': 10,  # 使用最近10期数据作为特征
                'target_type': 'next_numbers'  # 预测下一期号码
            }
        
        # 其他彩票类型的配置
        return {
            'feature_window': 10,
            'target_type': 'next_numbers'
        }
    
    async def _periodic_learning_task(self):
        """定期学习任务"""
        while self.is_running:
            try:
                # 每小时执行一次增量学习
                await asyncio.sleep(3600)
                
                if not self.is_running:
                    break
                
                # 对所有彩票类型执行增量学习
                for lottery_type in self.models.keys():
                    await self._incremental_learn(lottery_type)
                
            except Exception as e:
                self.logger.error(f"定期学习任务错误: {e}")
    
    async def _incremental_learn(self, lottery_type: str):
        """执行增量学习"""
        try:
            self.logger.info(f"开始 {lottery_type} 增量学习")
            
            # 获取新数据
            new_data = await self._get_new_training_data(lottery_type)
            if new_data.empty:
                self.logger.info(f"{lottery_type} 没有新数据，跳过学习")
                return
            
            # 准备特征和标签
            X, y = self._prepare_features_and_labels(lottery_type, new_data)
            if X is None or y is None:
                self.logger.warning(f"{lottery_type} 特征准备失败")
                return
            
            # 获取模型
            model_data = self.models[lottery_type]
            
            # 数据预处理
            if hasattr(model_data['scaler'], 'n_features_in_'):
                X_scaled = model_data['scaler'].transform(X)
            else:
                X_scaled = model_data['scaler'].fit_transform(X)
            
            # 增量学习
            incremental_model = model_data['incremental_model']
            
            # 如果是第一次训练
            if not hasattr(incremental_model, 'classes_'):
                # 获取所有可能的类别
                all_classes = self._get_all_possible_classes(lottery_type)
                incremental_model.partial_fit(X_scaled, y, classes=all_classes)
            else:
                incremental_model.partial_fit(X_scaled, y)
            
            # 更新主模型（定期重训练）
            if model_data['data_count'] % 100 == 0:  # 每100条新数据重训练一次
                await self._retrain_main_model(lottery_type)
            
            # 更新模型信息
            model_data['last_updated'] = datetime.now()
            model_data['data_count'] += len(new_data)
            
            # 评估模型性能
            accuracy = await self._evaluate_model(lottery_type, X_scaled, y)
            model_data['accuracy'] = accuracy
            
            # 记录训练历史
            model_data['training_history'].append({
                'timestamp': datetime.now(),
                'data_size': len(new_data),
                'accuracy': accuracy
            })
            
            # 保存模型
            await self._save_model(lottery_type)
            
            self.logger.info(f"{lottery_type} 增量学习完成，准确率: {accuracy:.4f}")
            
        except Exception as e:
            self.logger.error(f"{lottery_type} 增量学习失败: {e}")
    
    async def _get_new_training_data(self, lottery_type: str) -> pd.DataFrame:
        """获取新的训练数据"""
        try:
            # 获取最近的开奖数据
            end_date = date.today()
            start_date = end_date - timedelta(days=30)  # 最近30天
            
            draws = await self.data_storage.get_lottery_draws(
                lottery_type=lottery_type,
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )
            
            if not draws:
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(draws)
            df['draw_date'] = pd.to_datetime(df['draw_date'])
            df = df.sort_values('draw_date')
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {lottery_type} 训练数据失败: {e}")
            return pd.DataFrame()
    
    def _prepare_features_and_labels(self, lottery_type: str, data: pd.DataFrame) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """准备特征和标签"""
        try:
            if len(data) < 2:
                return None, None
            
            feature_config = self.models[lottery_type]['feature_config']
            window_size = feature_config.get('feature_window', 10)
            
            features = []
            labels = []
            
            for i in range(window_size, len(data)):
                # 特征：前window_size期的号码
                feature_window = data.iloc[i-window_size:i]
                feature_vector = self._extract_features(lottery_type, feature_window)
                
                # 标签：当前期的号码
                current_numbers = data.iloc[i]['numbers']
                label = self._encode_label(lottery_type, current_numbers)
                
                if feature_vector is not None and label is not None:
                    features.append(feature_vector)
                    labels.append(label)
            
            if not features:
                return None, None
            
            return np.array(features), np.array(labels)
            
        except Exception as e:
            self.logger.error(f"准备 {lottery_type} 特征和标签失败: {e}")
            return None, None
    
    def _extract_features(self, lottery_type: str, window_data: pd.DataFrame) -> Optional[np.ndarray]:
        """提取特征"""
        try:
            features = []
            
            for _, row in window_data.iterrows():
                numbers = row['numbers']
                
                # 基础特征：号码本身
                features.extend(numbers)
                
                # 统计特征
                features.append(np.mean(numbers))  # 平均值
                features.append(np.std(numbers))   # 标准差
                features.append(max(numbers))      # 最大值
                features.append(min(numbers))      # 最小值
                features.append(len(set(numbers))) # 唯一数字个数
            
            return np.array(features)
            
        except Exception as e:
            self.logger.error(f"提取 {lottery_type} 特征失败: {e}")
            return None
    
    def _encode_label(self, lottery_type: str, numbers: List[int]) -> Optional[int]:
        """编码标签"""
        try:
            # 简单编码：使用号码的哈希值作为类别
            numbers_str = ','.join(map(str, sorted(numbers)))
            hash_value = int(hashlib.md5(numbers_str.encode()).hexdigest()[:8], 16)
            return hash_value % 1000  # 限制在1000个类别内
            
        except Exception as e:
            self.logger.error(f"编码 {lottery_type} 标签失败: {e}")
            return None
    
    def _get_all_possible_classes(self, lottery_type: str) -> np.ndarray:
        """获取所有可能的类别"""
        # 返回0-999的类别（与_encode_label对应）
        return np.arange(1000)
    
    async def _retrain_main_model(self, lottery_type: str):
        """重训练主模型"""
        try:
            self.logger.info(f"重训练 {lottery_type} 主模型")
            
            # 获取更多历史数据
            end_date = date.today()
            start_date = end_date - timedelta(days=365)  # 最近一年
            
            data = await self._get_new_training_data(lottery_type)
            if data.empty:
                return
            
            X, y = self._prepare_features_and_labels(lottery_type, data)
            if X is None or y is None:
                return
            
            model_data = self.models[lottery_type]
            X_scaled = model_data['scaler'].fit_transform(X)
            
            # 重训练主模型
            model_data['main_model'].fit(X_scaled, y)
            
            self.logger.info(f"{lottery_type} 主模型重训练完成")
            
        except Exception as e:
            self.logger.error(f"重训练 {lottery_type} 主模型失败: {e}")
    
    async def _evaluate_model(self, lottery_type: str, X: np.ndarray, y: np.ndarray) -> float:
        """评估模型性能"""
        try:
            model_data = self.models[lottery_type]
            incremental_model = model_data['incremental_model']
            
            if hasattr(incremental_model, 'predict'):
                y_pred = incremental_model.predict(X)
                accuracy = accuracy_score(y, y_pred)
                return accuracy
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"评估 {lottery_type} 模型失败: {e}")
            return 0.0
    
    async def _save_model(self, lottery_type: str):
        """保存模型"""
        try:
            model_file = self.model_save_path / f"{lottery_type}_model.pkl"
            
            with open(model_file, 'wb') as f:
                pickle.dump(self.models[lottery_type], f)
            
            self.logger.debug(f"保存 {lottery_type} 模型成功")
            
        except Exception as e:
            self.logger.error(f"保存 {lottery_type} 模型失败: {e}")
    
    async def _save_all_models(self):
        """保存所有模型"""
        for lottery_type in self.models.keys():
            await self._save_model(lottery_type)
    
    async def predict(self, lottery_type: str) -> Optional[Dict[str, Any]]:
        """预测下一期号码"""
        try:
            if lottery_type not in self.models:
                self.logger.error(f"未找到 {lottery_type} 模型")
                return None
            
            # 获取最近数据作为特征
            recent_data = await self._get_new_training_data(lottery_type)
            if recent_data.empty:
                return None
            
            # 准备特征
            feature_config = self.models[lottery_type]['feature_config']
            window_size = feature_config.get('feature_window', 10)
            
            if len(recent_data) < window_size:
                return None
            
            feature_window = recent_data.tail(window_size)
            feature_vector = self._extract_features(lottery_type, feature_window)
            
            if feature_vector is None:
                return None
            
            # 预处理
            model_data = self.models[lottery_type]
            X = feature_vector.reshape(1, -1)
            X_scaled = model_data['scaler'].transform(X)
            
            # 预测
            incremental_pred = model_data['incremental_model'].predict(X_scaled)[0]
            main_pred = model_data['main_model'].predict(X_scaled)[0] if hasattr(model_data['main_model'], 'predict') else incremental_pred
            
            # 获取预测概率
            if hasattr(model_data['incremental_model'], 'predict_proba'):
                proba = model_data['incremental_model'].predict_proba(X_scaled)[0]
                confidence = max(proba)
            else:
                confidence = 0.5
            
            prediction_result = {
                'lottery_type': lottery_type,
                'prediction_date': date.today(),
                'incremental_prediction': int(incremental_pred),
                'main_prediction': int(main_pred),
                'confidence': float(confidence),
                'model_accuracy': model_data['accuracy'],
                'data_count': model_data['data_count']
            }
            
            # 记录预测历史
            model_data['prediction_history'].append(prediction_result)
            
            return prediction_result

        except Exception as e:
            self.logger.error(f"预测 {lottery_type} 失败: {e}")
            return None

    async def predict_with_chinese_algorithms(self, lottery_type: str) -> Optional[Dict[str, Any]]:
        """
        使用中国特色算法进行预测

        Args:
            lottery_type: 彩票类型

        Returns:
            Dict: 预测结果
        """
        try:
            self.logger.info(f"使用中国特色算法预测 {lottery_type}")

            # 获取最近的开奖数据
            recent_data = await self._get_new_training_data(lottery_type)
            if recent_data.empty:
                self.logger.warning(f"{lottery_type} 没有历史数据")
                return None

            # 获取最近一期的号码用于分析
            latest_draw = recent_data.iloc[-1]
            latest_numbers = latest_draw['numbers']

            # 进行中国特色算法分析
            chinese_analysis = self.chinese_algorithms.comprehensive_chinese_analysis(
                numbers=latest_numbers,
                historical_data=recent_data,
                target_date=date.today()
            )

            if not chinese_analysis:
                self.logger.warning("中国特色算法分析失败")
                return None

            # 获取传统机器学习预测
            ml_prediction = await self.predict(lottery_type)

            # 综合预测结果
            comprehensive_result = {
                'lottery_type': lottery_type,
                'prediction_date': date.today(),
                'chinese_analysis': chinese_analysis,
                'ml_prediction': ml_prediction,
                'comprehensive_recommendation': self._combine_predictions(
                    chinese_analysis, ml_prediction, lottery_type
                )
            }

            return comprehensive_result

        except Exception as e:
            self.logger.error(f"中国特色算法预测失败: {e}")
            return None

    def _combine_predictions(self, chinese_analysis: Dict[str, Any],
                           ml_prediction: Optional[Dict[str, Any]],
                           lottery_type: str) -> Dict[str, Any]:
        """
        综合中国特色算法和机器学习的预测结果

        Args:
            chinese_analysis: 中国特色算法分析结果
            ml_prediction: 机器学习预测结果
            lottery_type: 彩票类型

        Returns:
            Dict: 综合推荐
        """
        try:
            recommendation = {
                'recommended_numbers': [],
                'confidence_score': 0.0,
                'strategy_advice': "",
                'risk_level': "中等"
            }

            # 从中国特色算法获取推荐
            chinese_rec = chinese_analysis.get('comprehensive_recommendation', {})
            chinese_numbers = chinese_rec.get('recommended_numbers', [])
            chinese_confidence = chinese_rec.get('confidence', 0.0)

            # 从机器学习获取推荐
            ml_numbers = []
            ml_confidence = 0.0

            if ml_prediction:
                ml_confidence = ml_prediction.get('confidence', 0.0)
                # 这里可以根据ML预测结果生成推荐号码
                # 由于ML预测的是类别，需要转换为具体号码

            # 综合推荐策略
            if chinese_confidence > 0.6:
                # 中国特色算法置信度高，主要采用其推荐
                recommendation['recommended_numbers'] = chinese_numbers[:10]
                recommendation['confidence_score'] = chinese_confidence * 0.7 + ml_confidence * 0.3
                recommendation['strategy_advice'] = "基于传统文化分析，建议关注推荐号码"
                recommendation['risk_level'] = "较低"
            elif ml_confidence > 0.6:
                # 机器学习置信度高，主要采用其推荐
                recommendation['recommended_numbers'] = ml_numbers[:10]
                recommendation['confidence_score'] = ml_confidence * 0.7 + chinese_confidence * 0.3
                recommendation['strategy_advice'] = "基于数据模式分析，建议关注推荐号码"
                recommendation['risk_level'] = "中等"
            else:
                # 两者置信度都不高，综合考虑
                all_numbers = list(set(chinese_numbers + ml_numbers))
                recommendation['recommended_numbers'] = all_numbers[:10]
                recommendation['confidence_score'] = (chinese_confidence + ml_confidence) / 2
                recommendation['strategy_advice'] = "综合多种算法，建议谨慎选择"
                recommendation['risk_level'] = "较高"

            return recommendation

        except Exception as e:
            self.logger.error(f"综合预测结果失败: {e}")
            return {}
    
    def get_model_info(self, lottery_type: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        if lottery_type not in self.models:
            return None
        
        model_data = self.models[lottery_type]
        
        return {
            'lottery_type': lottery_type,
            'created_at': model_data['created_at'],
            'last_updated': model_data['last_updated'],
            'version': model_data['version'],
            'data_count': model_data['data_count'],
            'accuracy': model_data['accuracy'],
            'training_history_count': len(model_data['training_history']),
            'prediction_history_count': len(model_data['prediction_history'])
        }
