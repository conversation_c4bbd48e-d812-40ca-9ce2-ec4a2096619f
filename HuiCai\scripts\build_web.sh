#!/bin/bash
# HuiCai Web界面构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js环境
check_nodejs() {
    log_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 16+"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | sed 's/v//')
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d. -f1)
    
    if [ "$NODE_MAJOR" -lt 16 ]; then
        log_error "Node.js版本过低: $NODE_VERSION，需要16+"
        exit 1
    else
        log_success "Node.js版本检查通过: $NODE_VERSION"
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    else
        NPM_VERSION=$(npm --version)
        log_success "npm版本: $NPM_VERSION"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装Web界面依赖..."
    
    cd web
    
    # 清理node_modules和package-lock.json
    if [ -d "node_modules" ]; then
        log_info "清理旧的依赖..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        rm package-lock.json
    fi
    
    # 安装依赖
    npm install
    
    if [ $? -eq 0 ]; then
        log_success "依赖安装完成"
    else
        log_error "依赖安装失败"
        exit 1
    fi
    
    cd ..
}

# 构建生产版本
build_production() {
    log_info "构建生产版本..."
    
    cd web
    
    # 设置环境变量
    export REACT_APP_API_URL="http://localhost:8000/api"
    export GENERATE_SOURCEMAP=false
    
    # 构建
    npm run build
    
    if [ $? -eq 0 ]; then
        log_success "生产版本构建完成"
    else
        log_error "生产版本构建失败"
        exit 1
    fi
    
    cd ..
}

# 优化构建结果
optimize_build() {
    log_info "优化构建结果..."
    
    BUILD_DIR="web/build"
    
    if [ ! -d "$BUILD_DIR" ]; then
        log_error "构建目录不存在"
        exit 1
    fi
    
    # 显示构建大小
    log_info "构建文件大小:"
    du -sh $BUILD_DIR/*
    
    # 压缩静态文件（如果有gzip命令）
    if command -v gzip &> /dev/null; then
        log_info "压缩静态文件..."
        find $BUILD_DIR -name "*.js" -o -name "*.css" -o -name "*.html" | while read file; do
            gzip -c "$file" > "$file.gz"
        done
        log_success "静态文件压缩完成"
    fi
    
    # 创建文件清单
    log_info "创建文件清单..."
    find $BUILD_DIR -type f > $BUILD_DIR/file_manifest.txt
    
    log_success "构建优化完成"
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    BUILD_DIR="web/build"
    
    # 检查关键文件
    REQUIRED_FILES=("index.html" "static/js" "static/css")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -e "$BUILD_DIR/$file" ]; then
            log_error "缺少关键文件: $file"
            exit 1
        fi
    done
    
    # 检查index.html内容
    if ! grep -q "HuiCai" "$BUILD_DIR/index.html"; then
        log_warning "index.html中未找到HuiCai标识"
    fi
    
    log_success "构建结果验证通过"
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    BUILD_DIR="web/build"
    PACKAGE_NAME="huicai-web-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # 创建部署包
    tar -czf "$PACKAGE_NAME" -C "$BUILD_DIR" .
    
    if [ $? -eq 0 ]; then
        log_success "部署包创建完成: $PACKAGE_NAME"
        log_info "部署包大小: $(du -sh $PACKAGE_NAME | cut -f1)"
    else
        log_error "部署包创建失败"
        exit 1
    fi
}

# 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    cd web
    
    # 设置开发环境变量
    export REACT_APP_API_URL="http://localhost:8000/api"
    export BROWSER=none  # 不自动打开浏览器
    
    log_success "开发服务器启动中..."
    log_info "访问地址: http://localhost:3000"
    log_info "API地址: http://localhost:8000/api"
    log_info "按 Ctrl+C 停止服务器"
    
    npm start
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    cd web
    
    # 设置测试环境
    export CI=true
    
    npm test -- --coverage --watchAll=false
    
    if [ $? -eq 0 ]; then
        log_success "测试通过"
    else
        log_error "测试失败"
        exit 1
    fi
    
    cd ..
}

# 清理构建文件
clean_build() {
    log_info "清理构建文件..."
    
    if [ -d "web/build" ]; then
        rm -rf web/build
        log_success "构建文件已清理"
    fi
    
    if [ -d "web/node_modules" ]; then
        rm -rf web/node_modules
        log_success "依赖文件已清理"
    fi
    
    # 清理部署包
    rm -f huicai-web-*.tar.gz
    log_success "部署包已清理"
}

# 显示帮助信息
show_help() {
    echo "HuiCai Web界面构建脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  install     - 安装依赖"
    echo "  build       - 构建生产版本"
    echo "  dev         - 启动开发服务器"
    echo "  test        - 运行测试"
    echo "  package     - 创建部署包"
    echo "  clean       - 清理构建文件"
    echo "  all         - 完整构建流程（安装+构建+打包）"
    echo "  help        - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 all      # 完整构建"
    echo "  $0 dev      # 开发模式"
    echo "  $0 clean    # 清理文件"
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  HuiCai 慧彩系统 - Web界面构建脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    # 检查是否在项目根目录
    if [ ! -f "main.py" ]; then
        log_error "请在HuiCai项目根目录下运行此脚本"
        exit 1
    fi
    
    # 检查web目录
    if [ ! -d "web" ]; then
        log_error "web目录不存在"
        exit 1
    fi
    
    # 解析命令
    case "${1:-help}" in
        "install")
            check_nodejs
            install_dependencies
            ;;
        "build")
            check_nodejs
            build_production
            optimize_build
            verify_build
            ;;
        "dev")
            check_nodejs
            if [ ! -d "web/node_modules" ]; then
                install_dependencies
            fi
            start_dev_server
            ;;
        "test")
            check_nodejs
            if [ ! -d "web/node_modules" ]; then
                install_dependencies
            fi
            run_tests
            ;;
        "package")
            if [ ! -d "web/build" ]; then
                log_error "请先构建项目 (./build_web.sh build)"
                exit 1
            fi
            create_deployment_package
            ;;
        "clean")
            clean_build
            ;;
        "all")
            check_nodejs
            install_dependencies
            build_production
            optimize_build
            verify_build
            create_deployment_package
            ;;
        "help"|*)
            show_help
            ;;
    esac
    
    echo ""
    log_success "操作完成！"
}

# 运行主程序
main "$@"
