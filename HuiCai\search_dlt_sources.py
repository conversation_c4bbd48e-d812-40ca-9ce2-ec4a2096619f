#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索大乐透数据源
寻找可用的大乐透数据源

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
from bs4 import BeautifulSoup
import re
import time

def test_dlt_sources():
    """测试大乐透数据源"""
    print("🔍 搜索大乐透数据源...")
    
    # 可能的大乐透数据源
    sources = [
        {
            'name': '500彩票网大乐透',
            'url': 'https://datachart.500.com/dlt/',
            'encoding': 'gb2312'
        },
        {
            'name': '500彩票网大乐透历史',
            'url': 'https://datachart.500.com/dlt/history/',
            'encoding': 'gb2312'
        },
        {
            'name': '500彩票网大乐透历史数据',
            'url': 'https://datachart.500.com/dlt/history/newinc/history.php',
            'encoding': 'gb2312'
        },
        {
            'name': '新浪彩票大乐透',
            'url': 'https://lottery.sina.com.cn/dlt/',
            'encoding': 'utf-8'
        },
        {
            'name': '腾讯彩票大乐透',
            'url': 'https://caipiao.qq.com/dlt/',
            'encoding': 'utf-8'
        },
        {
            'name': '网易彩票大乐透',
            'url': 'https://caipiao.163.com/award/dlt/',
            'encoding': 'utf-8'
        },
        {
            'name': '搜狐彩票大乐透',
            'url': 'https://caipiao.sohu.com/dlt/',
            'encoding': 'utf-8'
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    successful_sources = []
    
    for source in sources:
        print(f"\n🔍 测试 {source['name']}")
        print(f"   URL: {source['url']}")
        
        try:
            response = requests.get(source['url'], headers=headers, timeout=15)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 设置编码
                response.encoding = source['encoding']
                html_content = response.text
                
                # 检查是否包含大乐透相关内容
                dlt_keywords = ['大乐透', 'dlt', '前区', '后区', '开奖', '期号']
                found_keywords = [kw for kw in dlt_keywords if kw in html_content]
                
                if found_keywords:
                    print(f"   ✅ 包含大乐透关键词: {found_keywords}")
                    
                    # 尝试解析HTML
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # 查找可能包含开奖数据的元素
                    potential_data = []
                    
                    # 查找表格
                    tables = soup.find_all('table')
                    for table in tables[:3]:  # 只检查前3个表格
                        text = table.get_text()
                        if any(kw in text for kw in ['期号', '开奖', '前区', '后区']):
                            potential_data.append(f"表格数据: {text[:100]}...")
                    
                    # 查找包含数字的div
                    divs = soup.find_all('div', class_=re.compile(r'(ball|number|result|award)'))
                    for div in divs[:5]:  # 只检查前5个
                        text = div.get_text().strip()
                        if re.search(r'\d{2}', text):  # 包含两位数字
                            potential_data.append(f"数字元素: {text}")
                    
                    # 正则表达式查找大乐透号码模式
                    # 大乐透: 5个前区球(1-35) + 2个后区球(1-12)
                    patterns = [
                        r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})',
                        r'(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2})',
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, html_content)
                        for match in matches[:3]:  # 只检查前3个匹配
                            numbers = [int(x) for x in match]
                            if len(numbers) == 7:
                                front_balls = numbers[:5]
                                back_balls = numbers[5:7]
                                
                                # 验证大乐透号码范围
                                front_valid = all(1 <= ball <= 35 for ball in front_balls)
                                back_valid = all(1 <= ball <= 12 for ball in back_balls)
                                
                                if front_valid and back_valid:
                                    front_str = ','.join([f"{ball:02d}" for ball in front_balls])
                                    back_str = ','.join([f"{ball:02d}" for ball in back_balls])
                                    potential_data.append(f"有效号码: {front_str} + {back_str}")
                    
                    if potential_data:
                        print(f"   📊 找到潜在数据:")
                        for data in potential_data[:3]:  # 只显示前3个
                            print(f"     {data}")
                        
                        successful_sources.append({
                            'name': source['name'],
                            'url': source['url'],
                            'encoding': source['encoding'],
                            'data_samples': potential_data
                        })
                    else:
                        print(f"   ❌ 未找到结构化数据")
                else:
                    print(f"   ❌ 不包含大乐透关键词")
            
            time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            print(f"   异常: {e}")
    
    return successful_sources

def main():
    """主函数"""
    print("🎯 大乐透数据源搜索")
    print("=" * 50)
    
    successful_sources = test_dlt_sources()
    
    print("\n" + "=" * 50)
    print(f"📊 搜索完成，找到 {len(successful_sources)} 个可用数据源")
    
    if successful_sources:
        print("\n✅ 可用的大乐透数据源:")
        for i, source in enumerate(successful_sources, 1):
            print(f"  {i}. {source['name']}")
            print(f"     URL: {source['url']}")
            print(f"     编码: {source['encoding']}")
            print(f"     数据样本: {len(source['data_samples'])} 个")
            print()
        
        print("🎉 找到可用数据源！接下来可以基于这些源开发大乐透爬虫。")
    else:
        print("\n❌ 未找到可用的大乐透数据源")
        print("建议:")
        print("  1. 检查网络连接")
        print("  2. 尝试使用代理")
        print("  3. 寻找其他大乐透数据源")
        print("  4. 考虑使用模拟数据进行开发")

if __name__ == "__main__":
    main()
