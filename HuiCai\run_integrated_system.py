#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai集成系统启动脚本
简化版启动器，自动安装依赖并启动系统

Author: HuiCai Team
Date: 2025-01-15
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """安装必需的依赖"""
    print("📦 检查并安装依赖...")
    
    dependencies = [
        'fastapi',
        'uvicorn[standard]',
        'psutil',
        'pandas',
        'numpy'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.split('[')[0])
            print(f"  ✅ {dep} 已安装")
        except ImportError:
            print(f"  📥 安装 {dep}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
                print(f"  ✅ {dep} 安装成功")
            except subprocess.CalledProcessError:
                print(f"  ❌ {dep} 安装失败")
                return False
    
    return True

def check_system():
    """系统检查"""
    print("🔍 系统检查...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("  ❌ Python版本过低，需要3.7+")
        return False
    else:
        print(f"  ✅ Python版本: {sys.version}")
    
    # 检查文件存在
    integrated_file = Path(__file__).parent / "integrated_system.py"
    if not integrated_file.exists():
        print("  ❌ integrated_system.py 文件不存在")
        return False
    else:
        print("  ✅ 集成系统文件存在")
    
    return True

def start_system():
    """启动系统"""
    print("🚀 启动HuiCai集成系统...")
    
    try:
        # 运行集成系统
        integrated_file = Path(__file__).parent / "integrated_system.py"
        subprocess.run([sys.executable, str(integrated_file)], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 系统启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 用户中断")
        return True
    
    return True

def main():
    """主函数"""
    print("🎯 HuiCai慧彩智能体集成系统启动器")
    print("🏆 Web界面 + 仪表盘 + 系统主入口一体化")
    print("="*60)
    
    # 系统检查
    if not check_system():
        print("❌ 系统检查失败")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return
    
    print("✅ 准备工作完成")
    print("="*60)
    
    # 启动系统
    start_system()

if __name__ == "__main__":
    main()
