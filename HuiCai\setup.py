#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai 慧彩系统安装脚本

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import sys
import asyncio
import asyncpg
import redis.asyncio as redis
from pathlib import Path


async def check_dependencies():
    """检查依赖环境"""
    print("检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8或更高")
        return False
    else:
        print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'asyncpg', 'redis', 'aiohttp', 'beautifulsoup4',
        'numpy', 'pandas', 'scikit-learn', 'APScheduler', 'PyYAML'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True


async def check_database():
    """检查数据库连接"""
    print("\n检查数据库连接...")
    
    # 默认数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'user': 'huicai',
        'password': 'huicai123',
        'database': 'huicai'
    }
    
    try:
        # 尝试连接PostgreSQL
        conn = await asyncpg.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database']
        )
        await conn.close()
        print("✅ PostgreSQL连接成功")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        print("\n请确保PostgreSQL已安装并运行，或创建数据库和用户:")
        print("CREATE DATABASE huicai;")
        print("CREATE USER huicai WITH PASSWORD 'huicai123';")
        print("GRANT ALL PRIVILEGES ON DATABASE huicai TO huicai;")
        return False


async def check_redis():
    """检查Redis连接"""
    print("\n检查Redis连接...")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, db=0)
        await redis_client.ping()
        await redis_client.close()
        print("✅ Redis连接成功")
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        print("请确保Redis已安装并运行")
        return False


def create_directories():
    """创建必要的目录"""
    print("\n创建目录结构...")
    
    directories = [
        'config',
        'config/lottery_configs',
        'config/algorithm_configs', 
        'config/crawler_configs',
        'data',
        'data/raw_data',
        'data/processed_data',
        'data/models',
        'data/logs',
        'data/backups'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")


async def init_database():
    """初始化数据库"""
    print("\n初始化数据库...")
    
    try:
        # 导入数据存储模块
        sys.path.insert(0, str(Path(__file__).parent))
        from src.management_layer.config_manager import ConfigManager
        from src.data_layer.data_storage import DataStorage
        from src.management_layer.log_manager import LogManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        await config_manager.load_config()
        
        # 创建日志管理器
        log_manager = LogManager(config_manager)
        logger = log_manager.get_logger("Setup")
        
        # 创建数据存储
        data_storage = DataStorage(config_manager, logger)
        await data_storage.initialize()
        await data_storage.close()
        
        print("✅ 数据库初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False


async def main():
    """主函数"""
    print("="*60)
    print("HuiCai 慧彩智能体系统 - 环境检查和初始化")
    print("="*60)
    
    # 检查依赖
    if not await check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的包")
        return
    
    # 创建目录
    create_directories()
    
    # 检查数据库
    db_ok = await check_database()
    redis_ok = await check_redis()
    
    if not (db_ok and redis_ok):
        print("\n❌ 数据库连接失败，请检查配置")
        return
    
    # 初始化数据库
    if await init_database():
        print("\n" + "="*60)
        print("✅ HuiCai 慧彩系统初始化完成!")
        print("="*60)
        print("\n现在可以运行: python main.py")
    else:
        print("\n❌ 系统初始化失败")


if __name__ == "__main__":
    asyncio.run(main())
