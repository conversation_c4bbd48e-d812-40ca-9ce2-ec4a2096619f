#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控和告警模块
监控系统性能、资源使用、错误率等指标

Author: HuiCai Team
Date: 2025-01-15
"""

import psutil
import threading
import time
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import sqlite3
from collections import deque, defaultdict
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int


@dataclass
class AlertRule:
    """告警规则数据类"""
    name: str
    metric: str
    operator: str  # '>', '<', '>=', '<=', '==', '!='
    threshold: float
    duration: int  # 持续时间（秒）
    severity: str  # 'low', 'medium', 'high', 'critical'
    enabled: bool = True
    last_triggered: Optional[str] = None


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        self.config = config_manager
        self.logger = logger
        
        # 监控配置
        self.monitor_config = {
            'collection_interval': 10,  # 数据收集间隔（秒）
            'retention_hours': 24,      # 数据保留时间（小时）
            'max_memory_points': 8640,  # 最大内存数据点数（24小时 * 360点/小时）
            'alert_check_interval': 30, # 告警检查间隔（秒）
            'email_enabled': False,     # 邮件告警
            'webhook_enabled': False    # Webhook告警
        }
        
        # 数据存储
        self.metrics_history = deque(maxlen=self.monitor_config['max_memory_points'])
        self.alert_history = deque(maxlen=1000)
        self.active_alerts = {}
        
        # 告警规则
        self.alert_rules = self._initialize_default_rules()
        
        # 监控线程
        self.monitoring_thread = None
        self.alert_thread = None
        self.stop_monitoring = False
        
        # 数据库
        self.db_path = Path("data/monitoring.db")
        self._initialize_database()
        
        # 网络基准值
        self.network_baseline = self._get_network_baseline()
        
        self.logger.info("系统监控器初始化完成")
    
    def _initialize_default_rules(self) -> List[AlertRule]:
        """初始化默认告警规则"""
        return [
            AlertRule(
                name="CPU使用率过高",
                metric="cpu_percent",
                operator=">",
                threshold=80.0,
                duration=300,  # 5分钟
                severity="high"
            ),
            AlertRule(
                name="内存使用率过高",
                metric="memory_percent",
                operator=">",
                threshold=85.0,
                duration=180,  # 3分钟
                severity="high"
            ),
            AlertRule(
                name="磁盘使用率过高",
                metric="disk_percent",
                operator=">",
                threshold=90.0,
                duration=600,  # 10分钟
                severity="medium"
            ),
            AlertRule(
                name="内存使用率极高",
                metric="memory_percent",
                operator=">",
                threshold=95.0,
                duration=60,   # 1分钟
                severity="critical"
            ),
            AlertRule(
                name="CPU使用率极高",
                metric="cpu_percent",
                operator=">",
                threshold=95.0,
                duration=120,  # 2分钟
                severity="critical"
            )
        ]
    
    def _initialize_database(self):
        """初始化监控数据库"""
        try:
            self.db_path.parent.mkdir(exist_ok=True)
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 创建指标表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        cpu_percent REAL,
                        memory_percent REAL,
                        memory_used_gb REAL,
                        memory_total_gb REAL,
                        disk_percent REAL,
                        disk_used_gb REAL,
                        disk_total_gb REAL,
                        network_sent_mb REAL,
                        network_recv_mb REAL,
                        process_count INTEGER,
                        thread_count INTEGER
                    )
                ''')
                
                # 创建告警表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        rule_name TEXT NOT NULL,
                        metric TEXT NOT NULL,
                        value REAL NOT NULL,
                        threshold REAL NOT NULL,
                        severity TEXT NOT NULL,
                        message TEXT,
                        resolved_at TEXT
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON system_metrics(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts(timestamp)')
                
                conn.commit()
                
            self.logger.info("监控数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化监控数据库失败: {e}")
    
    def _get_network_baseline(self) -> Dict[str, float]:
        """获取网络基准值"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv
            }
        except:
            return {'bytes_sent': 0, 'bytes_recv': 0}
    
    def collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            # 磁盘信息
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            # 网络信息
            net_io = psutil.net_io_counters()
            network_sent_mb = (net_io.bytes_sent - self.network_baseline['bytes_sent']) / (1024**2)
            network_recv_mb = (net_io.bytes_recv - self.network_baseline['bytes_recv']) / (1024**2)
            
            # 进程信息
            process_count = len(psutil.pids())
            thread_count = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) if p.info['num_threads'])
            
            metrics = SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_gb=memory_used_gb,
                memory_total_gb=memory_total_gb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_total_gb=disk_total_gb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                thread_count=thread_count
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            # 返回默认值
            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_gb=0.0,
                memory_total_gb=0.0,
                disk_percent=0.0,
                disk_used_gb=0.0,
                disk_total_gb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                process_count=0,
                thread_count=0
            )
    
    def store_metrics(self, metrics: SystemMetrics):
        """存储指标到数据库"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO system_metrics (
                        timestamp, cpu_percent, memory_percent, memory_used_gb,
                        memory_total_gb, disk_percent, disk_used_gb, disk_total_gb,
                        network_sent_mb, network_recv_mb, process_count, thread_count
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metrics.timestamp, metrics.cpu_percent, metrics.memory_percent,
                    metrics.memory_used_gb, metrics.memory_total_gb, metrics.disk_percent,
                    metrics.disk_used_gb, metrics.disk_total_gb, metrics.network_sent_mb,
                    metrics.network_recv_mb, metrics.process_count, metrics.thread_count
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"存储指标失败: {e}")
    
    def check_alerts(self, metrics: SystemMetrics):
        """检查告警条件"""
        try:
            current_time = datetime.now()
            
            for rule in self.alert_rules:
                if not rule.enabled:
                    continue
                
                # 获取指标值
                metric_value = getattr(metrics, rule.metric, None)
                if metric_value is None:
                    continue
                
                # 检查条件
                condition_met = self._evaluate_condition(
                    metric_value, rule.operator, rule.threshold
                )
                
                alert_key = f"{rule.name}_{rule.metric}"
                
                if condition_met:
                    # 检查是否已经在告警状态
                    if alert_key in self.active_alerts:
                        # 检查持续时间
                        alert_start = datetime.fromisoformat(self.active_alerts[alert_key]['start_time'])
                        duration = (current_time - alert_start).total_seconds()
                        
                        if duration >= rule.duration:
                            # 触发告警
                            self._trigger_alert(rule, metric_value, metrics.timestamp)
                    else:
                        # 开始新的告警监控
                        self.active_alerts[alert_key] = {
                            'rule': rule,
                            'start_time': current_time.isoformat(),
                            'metric_value': metric_value
                        }
                else:
                    # 条件不满足，清除告警状态
                    if alert_key in self.active_alerts:
                        del self.active_alerts[alert_key]
                        
        except Exception as e:
            self.logger.error(f"检查告警失败: {e}")
    
    def _evaluate_condition(self, value: float, operator: str, threshold: float) -> bool:
        """评估告警条件"""
        if operator == '>':
            return value > threshold
        elif operator == '<':
            return value < threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return value == threshold
        elif operator == '!=':
            return value != threshold
        else:
            return False
    
    def _trigger_alert(self, rule: AlertRule, value: float, timestamp: str):
        """触发告警"""
        try:
            alert_message = f"告警: {rule.name} - {rule.metric}当前值{value:.2f}超过阈值{rule.threshold}"
            
            alert_data = {
                'rule_name': rule.name,
                'metric': rule.metric,
                'value': value,
                'threshold': rule.threshold,
                'severity': rule.severity,
                'message': alert_message,
                'timestamp': timestamp
            }
            
            # 记录到内存
            self.alert_history.append(alert_data)
            
            # 记录到数据库
            self._store_alert(alert_data)
            
            # 发送通知
            self._send_alert_notification(alert_data)
            
            # 更新规则的最后触发时间
            rule.last_triggered = timestamp
            
            self.logger.warning(alert_message)
            
        except Exception as e:
            self.logger.error(f"触发告警失败: {e}")
    
    def _store_alert(self, alert_data: Dict[str, Any]):
        """存储告警到数据库"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO alerts (
                        timestamp, rule_name, metric, value, threshold, severity, message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    alert_data['timestamp'], alert_data['rule_name'], alert_data['metric'],
                    alert_data['value'], alert_data['threshold'], alert_data['severity'],
                    alert_data['message']
                ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"存储告警失败: {e}")
    
    def _send_alert_notification(self, alert_data: Dict[str, Any]):
        """发送告警通知"""
        try:
            # 邮件通知
            if self.monitor_config.get('email_enabled'):
                self._send_email_alert(alert_data)
            
            # Webhook通知
            if self.monitor_config.get('webhook_enabled'):
                self._send_webhook_alert(alert_data)
                
        except Exception as e:
            self.logger.error(f"发送告警通知失败: {e}")
    
    def _send_email_alert(self, alert_data: Dict[str, Any]):
        """发送邮件告警"""
        # 邮件发送逻辑（需要配置SMTP服务器）
        pass
    
    def _send_webhook_alert(self, alert_data: Dict[str, Any]):
        """发送Webhook告警"""
        # Webhook发送逻辑
        pass
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.logger.warning("监控已经在运行")
            return
        
        self.stop_monitoring = False
        
        # 启动指标收集线程
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        # 启动告警检查线程
        self.alert_thread = threading.Thread(target=self._alert_loop, daemon=True)
        self.alert_thread.start()
        
        self.logger.info("系统监控已启动")
    
    def stop_monitoring_service(self):
        """停止监控"""
        self.stop_monitoring = True
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        if self.alert_thread:
            self.alert_thread.join(timeout=5)
        
        self.logger.info("系统监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while not self.stop_monitoring:
            try:
                # 收集指标
                metrics = self.collect_metrics()
                
                # 存储到内存
                self.metrics_history.append(metrics)
                
                # 存储到数据库
                self.store_metrics(metrics)
                
                # 清理旧数据
                self._cleanup_old_data()
                
                time.sleep(self.monitor_config['collection_interval'])
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)
    
    def _alert_loop(self):
        """告警检查循环"""
        while not self.stop_monitoring:
            try:
                if self.metrics_history:
                    latest_metrics = self.metrics_history[-1]
                    self.check_alerts(latest_metrics)
                
                time.sleep(self.monitor_config['alert_check_interval'])
                
            except Exception as e:
                self.logger.error(f"告警检查循环异常: {e}")
                time.sleep(5)
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.monitor_config['retention_hours'])
            cutoff_str = cutoff_time.isoformat()
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 清理旧的指标数据
                cursor.execute('DELETE FROM system_metrics WHERE timestamp < ?', (cutoff_str,))
                
                # 清理旧的告警数据（保留更长时间）
                alert_cutoff = (datetime.now() - timedelta(days=7)).isoformat()
                cursor.execute('DELETE FROM alerts WHERE timestamp < ?', (alert_cutoff,))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态"""
        try:
            if self.metrics_history:
                latest_metrics = self.metrics_history[-1]
                
                return {
                    'timestamp': latest_metrics.timestamp,
                    'cpu_percent': latest_metrics.cpu_percent,
                    'memory_percent': latest_metrics.memory_percent,
                    'memory_used_gb': latest_metrics.memory_used_gb,
                    'memory_total_gb': latest_metrics.memory_total_gb,
                    'disk_percent': latest_metrics.disk_percent,
                    'disk_used_gb': latest_metrics.disk_used_gb,
                    'disk_total_gb': latest_metrics.disk_total_gb,
                    'process_count': latest_metrics.process_count,
                    'active_alerts_count': len(self.active_alerts),
                    'monitoring_status': 'running' if not self.stop_monitoring else 'stopped'
                }
            else:
                return {
                    'monitoring_status': 'no_data',
                    'message': '暂无监控数据'
                }
                
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {
                'error': str(e),
                'monitoring_status': 'error'
            }
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取指标历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 从内存中获取最近的数据
            recent_metrics = []
            for metrics in self.metrics_history:
                metrics_time = datetime.fromisoformat(metrics.timestamp)
                if metrics_time >= cutoff_time:
                    recent_metrics.append(asdict(metrics))
            
            return recent_metrics
            
        except Exception as e:
            self.logger.error(f"获取指标历史失败: {e}")
            return []
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_str = cutoff_time.isoformat()
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT timestamp, rule_name, metric, value, threshold, severity, message
                    FROM alerts 
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                    LIMIT 100
                ''', (cutoff_str,))
                
                alerts = []
                for row in cursor.fetchall():
                    alerts.append({
                        'timestamp': row[0],
                        'rule_name': row[1],
                        'metric': row[2],
                        'value': row[3],
                        'threshold': row[4],
                        'severity': row[5],
                        'message': row[6]
                    })
                
                return alerts
                
        except Exception as e:
            self.logger.error(f"获取告警历史失败: {e}")
            return []
    
    def add_alert_rule(self, rule: AlertRule) -> bool:
        """添加告警规则"""
        try:
            self.alert_rules.append(rule)
            self.logger.info(f"添加告警规则: {rule.name}")
            return True
        except Exception as e:
            self.logger.error(f"添加告警规则失败: {e}")
            return False
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """删除告警规则"""
        try:
            self.alert_rules = [rule for rule in self.alert_rules if rule.name != rule_name]
            self.logger.info(f"删除告警规则: {rule_name}")
            return True
        except Exception as e:
            self.logger.error(f"删除告警规则失败: {e}")
            return False
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        try:
            current_status = self.get_current_status()
            recent_alerts = self.get_alert_history(hours=1)
            
            return {
                'current_status': current_status,
                'alert_rules_count': len(self.alert_rules),
                'active_alerts_count': len(self.active_alerts),
                'recent_alerts_count': len(recent_alerts),
                'data_points_collected': len(self.metrics_history),
                'monitoring_uptime': 'running' if not self.stop_monitoring else 'stopped',
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取监控摘要失败: {e}")
            return {'error': str(e)}
