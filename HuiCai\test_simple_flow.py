#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的数据流程
爬取数据 → 写入数据库 → 从数据库读取显示

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_complete_flow():
    """测试完整的数据流程"""
    print("🔄 测试完整数据流程...")
    print("流程: 爬取数据 → 写入数据库 → 从数据库读取显示")
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import ShuangseqiuDataAccess, DaletouDataAccess
        
        # 步骤1: 爬取数据
        print("\n📡 步骤1: 爬取数据...")
        manager = CrawlerManager()
        
        # 爬取双色球
        print("  🔴 爬取双色球数据...")
        ssq_result = manager.manual_crawl('shuangseqiu', 2)
        print(f"     结果: {ssq_result['status']}, 保存: {ssq_result.get('saved_count', 0)}条")
        
        # 爬取大乐透
        print("  🔵 爬取大乐透数据...")
        dlt_result = manager.manual_crawl('daletou', 2)
        print(f"     结果: {dlt_result['status']}, 保存: {dlt_result.get('saved_count', 0)}条")
        
        manager.close()
        
        # 步骤2: 从数据库读取数据
        print("\n💾 步骤2: 从数据库读取最新数据...")
        db_manager = LocalDatabaseManager()
        
        # 读取双色球数据
        ssq_data = ShuangseqiuDataAccess(db_manager)
        ssq_latest = ssq_data.get_latest_results(1)
        
        if ssq_latest:
            ssq = ssq_latest[0]
            print(f"  🔴 双色球最新数据:")
            print(f"     期号: {ssq.get('issue', 'N/A')}")
            print(f"     日期: {ssq.get('date', 'N/A')}")
            print(f"     红球: {ssq.get('red_balls', [])}")
            print(f"     蓝球: {ssq.get('blue_ball', 0)}")
        else:
            print("  🔴 双色球: 数据库中没有数据")
        
        # 读取大乐透数据
        dlt_data = DaletouDataAccess(db_manager)
        dlt_latest = dlt_data.get_latest_results(1)
        
        if dlt_latest:
            dlt = dlt_latest[0]
            print(f"  🔵 大乐透最新数据:")
            print(f"     期号: {dlt.get('issue', 'N/A')}")
            print(f"     日期: {dlt.get('date', 'N/A')}")
            print(f"     前区: {dlt.get('front_balls', [])}")
            print(f"     后区: {dlt.get('back_balls', [])}")
        else:
            print("  🔵 大乐透: 数据库中没有数据")
        
        # 步骤3: 模拟Web API响应格式
        print("\n🌐 步骤3: 构建Web API响应格式...")
        
        api_response = {
            'status': 'success',
            'shuangseqiu': {
                'status': ssq_result['status'],
                'data': {
                    'issue': ssq.get('issue', '未知期号') if ssq_latest else '未知期号',
                    'date': ssq.get('date', '未知日期') if ssq_latest else '未知日期',
                    'red_balls': ssq.get('red_balls', []) if ssq_latest else [],
                    'blue_ball': ssq.get('blue_ball', 0) if ssq_latest else 0
                } if ssq_latest else None,
                'saved_count': ssq_result.get('saved_count', 0)
            },
            'daletou': {
                'status': dlt_result['status'],
                'data': {
                    'issue': dlt.get('issue', '未知期号') if dlt_latest else '未知期号',
                    'date': dlt.get('date', '未知日期') if dlt_latest else '未知日期',
                    'front_balls': dlt.get('front_balls', []) if dlt_latest else [],
                    'back_balls': dlt.get('back_balls', []) if dlt_latest else []
                } if dlt_latest else None,
                'saved_count': dlt_result.get('saved_count', 0)
            },
            'message': '数据流程测试完成'
        }
        
        print("API响应格式:")
        print(json.dumps(api_response, ensure_ascii=False, indent=2))
        
        # 步骤4: 验证数据完整性
        print("\n✅ 步骤4: 验证数据完整性...")
        
        success = True
        
        # 验证双色球数据
        if api_response['shuangseqiu']['data']:
            ssq_data_check = api_response['shuangseqiu']['data']
            red_balls = ssq_data_check.get('red_balls', [])
            blue_ball = ssq_data_check.get('blue_ball', 0)
            
            if len(red_balls) == 6 and all(1 <= ball <= 33 for ball in red_balls) and 1 <= blue_ball <= 16:
                print("  ✅ 双色球数据格式正确")
            else:
                print("  ❌ 双色球数据格式错误")
                success = False
        else:
            print("  ⚠️ 双色球数据为空")
        
        # 验证大乐透数据
        if api_response['daletou']['data']:
            dlt_data_check = api_response['daletou']['data']
            front_balls = dlt_data_check.get('front_balls', [])
            back_balls = dlt_data_check.get('back_balls', [])
            
            front_valid = len(front_balls) == 5 and all(1 <= ball <= 35 for ball in front_balls)
            back_valid = len(back_balls) == 2 and all(1 <= ball <= 12 for ball in back_balls)
            
            if front_valid and back_valid:
                print("  ✅ 大乐透数据格式正确")
            else:
                print("  ❌ 大乐透数据格式错误")
                success = False
        else:
            print("  ⚠️ 大乐透数据为空")
        
        db_manager.close_all_connections()
        
        if success:
            print("\n🎉 完整数据流程测试成功！")
            print("✅ 数据流程正常:")
            print("  1. 爬虫能够获取真实数据")
            print("  2. 数据能够正确保存到数据库")
            print("  3. 能够从数据库读取最新数据")
            print("  4. API响应格式正确")
            print("  5. 数据格式验证通过")
        else:
            print("\n❌ 数据流程存在问题")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔄 简化数据流程测试")
    print("=" * 50)
    
    if test_complete_flow():
        print("\n🎯 现在可以启动仪表盘测试更新功能:")
        print("  python start_dashboard.py")
        print("\n💡 更新按钮的工作流程:")
        print("  1. 点击'更新数据'按钮")
        print("  2. 调用爬虫获取最新数据")
        print("  3. 爬虫自动保存数据到数据库（去重）")
        print("  4. 从数据库读取最新数据")
        print("  5. 返回给前端并更新页面显示")
        return 0
    else:
        print("\n❌ 数据流程测试失败")
        print("请检查:")
        print("  - 爬虫系统是否正常")
        print("  - 数据库连接是否正常")
        print("  - 网络连接是否正常")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
