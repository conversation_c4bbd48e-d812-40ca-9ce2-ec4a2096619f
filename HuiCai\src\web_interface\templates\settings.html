{% extends "base.html" %}

{% block title %}系统设置 - HuiCai 慧彩智能体系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-cog text-primary me-2"></i>
            系统设置
        </h2>
    </div>
</div>

<div class="row">
    <!-- 设置表单 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h text-info me-2"></i>
                    系统配置
                </h5>
            </div>
            <div class="card-body">
                <form id="settingsForm">
                    <!-- 基本设置 -->
                    <h6 class="border-bottom pb-2 mb-3">基本设置</h6>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">界面主题</label>
                            <select class="form-select" name="theme">
                                <option value="light" {{ 'selected' if settings.theme == 'light' }}>浅色主题</option>
                                <option value="dark" {{ 'selected' if settings.theme == 'dark' }}>深色主题</option>
                                <option value="auto" {{ 'selected' if settings.theme == 'auto' }}>自动切换</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">界面语言</label>
                            <select class="form-select" name="language">
                                <option value="zh-CN" {{ 'selected' if settings.language == 'zh-CN' }}>简体中文</option>
                                <option value="zh-TW">繁体中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 预测设置 -->
                    <h6 class="border-bottom pb-2 mb-3 mt-4">预测设置</h6>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="auto_predict" 
                                       {{ 'checked' if settings.auto_predict }}>
                                <label class="form-check-label">自动预测</label>
                            </div>
                            <small class="text-muted">开启后系统将自动进行预测</small>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="notification" 
                                       {{ 'checked' if settings.notification }}>
                                <label class="form-check-label">消息通知</label>
                            </div>
                            <small class="text-muted">开启预测完成通知</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">默认预测方法</label>
                            <select class="form-select" name="default_method">
                                <option value="comprehensive">综合预测</option>
                                <option value="deeplearning">深度学习</option>
                                <option value="chinese">中国算法</option>
                                <option value="traditional">传统ML</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">预测历史保留天数</label>
                            <input type="number" class="form-control" name="history_days" value="30" min="1" max="365">
                        </div>
                    </div>
                    
                    <!-- 系统设置 -->
                    <h6 class="border-bottom pb-2 mb-3 mt-4">系统设置</h6>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="backup" 
                                       {{ 'checked' if settings.backup }}>
                                <label class="form-check-label">自动备份</label>
                            </div>
                            <small class="text-muted">定期备份数据和模型</small>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="auto_update">
                                <label class="form-check-label">自动更新</label>
                            </div>
                            <small class="text-muted">自动检查并安装更新</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">日志级别</label>
                            <select class="form-select" name="log_level">
                                <option value="DEBUG">调试</option>
                                <option value="INFO" selected>信息</option>
                                <option value="WARNING">警告</option>
                                <option value="ERROR">错误</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">最大内存使用率 (%)</label>
                            <input type="range" class="form-range" name="max_memory" min="50" max="90" value="80" 
                                   oninput="this.nextElementSibling.textContent = this.value + '%'">
                            <span>80%</span>
                        </div>
                    </div>
                    
                    <!-- 按钮 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-save me-2"></i>保存设置
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetSettings()">
                                <i class="fas fa-undo me-2"></i>重置默认
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
                                <i class="fas fa-download me-2"></i>导出配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 系统信息和操作 -->
    <div class="col-lg-4">
        <!-- 系统状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-success me-2"></i>
                    系统状态
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between">
                        <span>系统版本</span>
                        <span class="badge bg-primary">v4.0.0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between">
                        <span>数据库状态</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between">
                        <span>模型状态</span>
                        <span class="badge bg-success">运行中</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between">
                        <span>最后备份</span>
                        <span class="text-muted">2小时前</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools text-warning me-2"></i>
                    系统操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info" onclick="checkUpdates()">
                        <i class="fas fa-sync-alt me-2"></i>检查更新
                    </button>
                    <button class="btn btn-outline-success" onclick="backupSystem()">
                        <i class="fas fa-save me-2"></i>立即备份
                    </button>
                    <button class="btn btn-outline-warning" onclick="clearCache()">
                        <i class="fas fa-trash me-2"></i>清理缓存
                    </button>
                    <button class="btn btn-outline-danger" onclick="restartSystem()">
                        <i class="fas fa-power-off me-2"></i>重启系统
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 日志查看 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-file-alt text-secondary me-2"></i>
                    系统日志
                </h6>
            </div>
            <div class="card-body">
                <div class="log-container" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
                    <div class="text-success">[2025-01-15 10:30:00] INFO: 系统启动完成</div>
                    <div class="text-info">[2025-01-15 10:31:00] INFO: 加载配置文件</div>
                    <div class="text-info">[2025-01-15 10:31:30] INFO: 初始化数据库连接</div>
                    <div class="text-success">[2025-01-15 10:32:00] INFO: 模型加载完成</div>
                    <div class="text-warning">[2025-01-15 10:32:30] WARN: 内存使用率较高: 75%</div>
                    <div class="text-info">[2025-01-15 10:33:00] INFO: 开始预测任务</div>
                    <div class="text-success">[2025-01-15 10:33:30] INFO: 预测完成</div>
                </div>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="downloadLogs()">
                        <i class="fas fa-download me-1"></i>下载
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 保存设置
    document.getElementById('settingsForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const settings = {};
        
        for (let [key, value] of formData.entries()) {
            settings[key] = value;
        }
        
        // 处理复选框
        const checkboxes = this.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            settings[cb.name] = cb.checked;
        });
        
        try {
            showAlert('正在保存设置...', 'info');
            
            // 模拟保存过程
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            showAlert('设置保存成功！', 'success');
        } catch (error) {
            showAlert('设置保存失败：' + error.message, 'danger');
        }
    });
    
    // 重置设置
    function resetSettings() {
        if (confirm('确定要重置为默认设置吗？')) {
            document.getElementById('settingsForm').reset();
            showAlert('设置已重置为默认值', 'info');
        }
    }
    
    // 导出设置
    function exportSettings() {
        const settings = {
            theme: 'light',
            language: 'zh-CN',
            auto_predict: true,
            notification: true,
            backup: true
        };
        
        const blob = new Blob([JSON.stringify(settings, null, 2)], {type: 'application/json'});
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'huicai_settings.json';
        link.click();
        URL.revokeObjectURL(url);
        
        showAlert('设置导出成功！', 'success');
    }
    
    // 检查更新
    async function checkUpdates() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            // 模拟检查更新
            await new Promise(resolve => setTimeout(resolve, 2000));
            showAlert('当前已是最新版本！', 'success');
        } catch (error) {
            showAlert('检查更新失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 备份系统
    async function backupSystem() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            showAlert('开始备份系统...', 'info');
            await new Promise(resolve => setTimeout(resolve, 3000));
            showAlert('系统备份完成！', 'success');
        } catch (error) {
            showAlert('系统备份失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 清理缓存
    async function clearCache() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            showAlert('正在清理缓存...', 'info');
            await new Promise(resolve => setTimeout(resolve, 1500));
            showAlert('缓存清理完成！释放了256MB空间', 'success');
        } catch (error) {
            showAlert('缓存清理失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 重启系统
    function restartSystem() {
        if (confirm('确定要重启系统吗？这将中断当前所有任务。')) {
            showAlert('系统正在重启...', 'warning');
            setTimeout(() => {
                location.reload();
            }, 3000);
        }
    }
    
    // 刷新日志
    function refreshLogs() {
        const logContainer = document.querySelector('.log-container');
        const newLog = `<div class="text-info">[${new Date().toLocaleString()}] INFO: 日志已刷新</div>`;
        logContainer.innerHTML += newLog;
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 下载日志
    function downloadLogs() {
        const logs = document.querySelector('.log-container').textContent;
        const blob = new Blob([logs], {type: 'text/plain'});
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `huicai_logs_${new Date().toISOString().split('T')[0]}.txt`;
        link.click();
        URL.revokeObjectURL(url);
        
        showAlert('日志下载成功！', 'success');
    }
</script>
{% endblock %}
