#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球爬虫
专门爬取双色球开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
import logging

from .base_crawler import BaseCrawler


class ShuangseqiuCrawler(BaseCrawler):
    """双色球爬虫"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """初始化双色球爬虫"""
        super().__init__(config_manager, logger, 'shuangseqiu')
        
        # 双色球特定配置
        self.data_sources = self.lottery_config.get('data_source', {})
        self.primary_url = self.data_sources.get('primary', 'https://www.cwl.gov.cn/kjxx/ssq/')
        self.backup_urls = self.data_sources.get('backup', [])
        
        # 数字范围配置
        self.red_ball_range = self.lottery_config.get('red_ball_range', [1, 33])
        self.red_ball_count = self.lottery_config.get('red_ball_count', 6)
        self.blue_ball_range = self.lottery_config.get('blue_ball_range', [1, 16])
        self.blue_ball_count = self.lottery_config.get('blue_ball_count', 1)
    
    async def crawl_latest_draw(self) -> Optional[Dict[str, Any]]:
        """爬取最新开奖数据"""
        try:
            self.logger.info("开始爬取双色球最新开奖数据")
            
            # 尝试主要数据源
            result = await self._crawl_from_primary_source()
            if result:
                return result
            
            # 尝试备用数据源
            for backup_url in self.backup_urls:
                self.logger.info(f"尝试备用数据源: {backup_url}")
                result = await self._crawl_from_backup_source(backup_url)
                if result:
                    return result
            
            self.logger.error("所有数据源都无法获取最新开奖数据")
            return None
            
        except Exception as e:
            self.logger.error(f"爬取最新开奖数据失败: {e}")
            return None
    
    async def _crawl_from_primary_source(self) -> Optional[Dict[str, Any]]:
        """从主要数据源爬取数据"""
        try:
            html = await self.get_html(self.primary_url)
            if not html:
                return None
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找开奖信息容器
            draw_container = soup.find('div', class_='kjjg')
            if not draw_container:
                self.logger.warning("未找到开奖结果容器")
                return None
            
            # 提取期号
            period_elem = draw_container.find('span', class_='qi')
            if not period_elem:
                self.logger.warning("未找到期号信息")
                return None
            
            period_text = period_elem.get_text(strip=True)
            period_match = re.search(r'(\d{7})', period_text)
            if not period_match:
                self.logger.warning("无法解析期号")
                return None
            
            draw_number = period_match.group(1)
            
            # 提取开奖日期
            date_elem = draw_container.find('span', class_='rq')
            if not date_elem:
                self.logger.warning("未找到开奖日期")
                return None
            
            date_text = date_elem.get_text(strip=True)
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', date_text)
            if not date_match:
                self.logger.warning("无法解析开奖日期")
                return None
            
            draw_date = datetime.strptime(date_match.group(1), '%Y-%m-%d').date()
            
            # 提取开奖号码
            red_balls = []
            blue_balls = []
            
            # 红球
            red_ball_elems = draw_container.find_all('span', class_='red')
            for elem in red_ball_elems:
                try:
                    number = int(elem.get_text(strip=True))
                    if self.red_ball_range[0] <= number <= self.red_ball_range[1]:
                        red_balls.append(number)
                except ValueError:
                    continue
            
            # 蓝球
            blue_ball_elems = draw_container.find_all('span', class_='blue')
            for elem in blue_ball_elems:
                try:
                    number = int(elem.get_text(strip=True))
                    if self.blue_ball_range[0] <= number <= self.blue_ball_range[1]:
                        blue_balls.append(number)
                except ValueError:
                    continue
            
            # 验证数据
            if len(red_balls) != self.red_ball_count or len(blue_balls) != self.blue_ball_count:
                self.logger.warning(f"号码数量不正确: 红球{len(red_balls)}, 蓝球{len(blue_balls)}")
                return None
            
            return {
                'lottery_type': self.lottery_type,
                'draw_date': draw_date,
                'draw_number': draw_number,
                'red_balls': sorted(red_balls),
                'blue_balls': blue_balls,
                'numbers': sorted(red_balls) + blue_balls,
                'source': 'primary'
            }
            
        except Exception as e:
            self.logger.error(f"从主要数据源爬取失败: {e}")
            return None
    
    async def _crawl_from_backup_source(self, url: str) -> Optional[Dict[str, Any]]:
        """从备用数据源爬取数据"""
        try:
            # 这里实现备用数据源的爬取逻辑
            # 由于不同网站结构不同，需要针对性实现
            self.logger.info(f"尝试从备用源爬取: {url}")
            
            # 示例：500彩票网的API接口
            if '500.com' in url:
                return await self._crawl_from_500_com()
            
            return None
            
        except Exception as e:
            self.logger.error(f"从备用数据源爬取失败: {e}")
            return None
    
    async def _crawl_from_500_com(self) -> Optional[Dict[str, Any]]:
        """从500彩票网爬取数据"""
        try:
            # 500彩票网的API接口
            api_url = "https://datachart.500.com/ssq/history/newinc/history.php"
            params = {
                'limit': '1',
                'sort': 'desc'
            }
            
            json_data = await self.get_json(api_url, params=params)
            if not json_data or 'data' not in json_data:
                return None
            
            data = json_data['data']
            if not data:
                return None
            
            latest_draw = data[0]
            
            # 解析数据
            draw_number = latest_draw.get('expect', '')
            draw_date_str = latest_draw.get('opentime', '')
            numbers_str = latest_draw.get('opencode', '')
            
            if not all([draw_number, draw_date_str, numbers_str]):
                return None
            
            # 解析日期
            draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d').date()
            
            # 解析号码
            numbers = [int(x) for x in numbers_str.split(',')]
            if len(numbers) != 7:
                return None
            
            red_balls = sorted(numbers[:6])
            blue_balls = [numbers[6]]
            
            return {
                'lottery_type': self.lottery_type,
                'draw_date': draw_date,
                'draw_number': draw_number,
                'red_balls': red_balls,
                'blue_balls': blue_balls,
                'numbers': red_balls + blue_balls,
                'source': 'backup_500com'
            }
            
        except Exception as e:
            self.logger.error(f"从500彩票网爬取失败: {e}")
            return None
    
    async def crawl_historical_draws(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """爬取历史开奖数据"""
        try:
            self.logger.info(f"开始爬取双色球历史数据: {start_date} 到 {end_date}")
            
            results = []
            current_date = end_date
            
            while current_date >= start_date:
                # 这里可以实现按日期爬取历史数据的逻辑
                # 由于篇幅限制，这里只是示例框架
                
                # 模拟爬取一天的数据
                daily_data = await self._crawl_daily_data(current_date)
                if daily_data:
                    results.append(daily_data)
                
                current_date -= timedelta(days=1)
                
                # 避免请求过于频繁
                await self._delay_request()
            
            self.logger.info(f"爬取历史数据完成，共获取 {len(results)} 条记录")
            return results
            
        except Exception as e:
            self.logger.error(f"爬取历史数据失败: {e}")
            return []
    
    async def _crawl_daily_data(self, target_date: date) -> Optional[Dict[str, Any]]:
        """爬取指定日期的开奖数据"""
        # 这里实现具体的日期数据爬取逻辑
        # 返回格式与crawl_latest_draw相同
        pass
    
    def parse_draw_data(self, raw_data: Any) -> Optional[Dict[str, Any]]:
        """解析开奖数据"""
        try:
            if isinstance(raw_data, dict):
                return raw_data
            
            # 如果是其他格式的原始数据，在这里进行解析
            # 返回标准格式的数据
            
            return None
            
        except Exception as e:
            self.logger.error(f"解析开奖数据失败: {e}")
            return None
    
    def validate_numbers(self, red_balls: List[int], blue_balls: List[int]) -> bool:
        """验证号码有效性"""
        # 验证红球
        if len(red_balls) != self.red_ball_count:
            return False
        
        for ball in red_balls:
            if not (self.red_ball_range[0] <= ball <= self.red_ball_range[1]):
                return False
        
        # 验证蓝球
        if len(blue_balls) != self.blue_ball_count:
            return False
        
        for ball in blue_balls:
            if not (self.blue_ball_range[0] <= ball <= self.blue_ball_range[1]):
                return False
        
        # 验证红球无重复
        if len(set(red_balls)) != len(red_balls):
            return False
        
        return True
