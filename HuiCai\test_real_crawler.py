#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据爬虫测试
测试基于500彩票网的真实数据爬虫

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_real_ssq_crawler():
    """测试真实双色球爬虫"""
    print("🔴 测试真实双色球爬虫...")
    print("-" * 40)
    
    try:
        from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler
        
        # 创建爬虫实例
        crawler = ShuangseqiuCrawler()
        print(f"✅ 爬虫创建成功")
        print(f"📍 数据源数量: {len(crawler.data_sources)}")
        
        # 显示数据源信息
        for i, source in enumerate(crawler.data_sources):
            print(f"  {i+1}. {source['name']} ({source['type']})")
        
        # 测试爬取数据
        print(f"\n📡 爬取最新5期数据...")
        result = crawler.crawl_latest_data(5)
        
        print(f"📊 爬取状态: {result['status']}")
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！")
            print(f"📍 数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            print(f"⏰ 爬取时间: {result['timestamp']}")
            
            # 显示数据详情
            print(f"\n🎯 开奖数据:")
            for i, draw in enumerate(result['data']):
                issue = draw.get('issue', '未知期号')
                date = draw.get('date', '未知日期')
                red_balls = draw.get('red_balls', [])
                blue_ball = draw.get('blue_ball', 0)
                source = draw.get('source', '未知来源')
                
                if red_balls and blue_ball:
                    red_str = ' '.join([f"{ball:02d}" for ball in red_balls])
                    print(f"  {i+1}. {issue} ({date}): {red_str} + {blue_ball:02d} [{source}]")
                else:
                    print(f"  {i+1}. {issue} - 数据格式异常: {draw}")
            
            # 验证数据质量
            print(f"\n🔍 数据质量验证:")
            valid_count = 0
            for draw in result['data']:
                red_balls = draw.get('red_balls', [])
                blue_ball = draw.get('blue_ball', 0)
                
                # 检查红球
                red_valid = (len(red_balls) == 6 and 
                           all(1 <= ball <= 33 for ball in red_balls) and
                           len(set(red_balls)) == 6)
                
                # 检查蓝球
                blue_valid = 1 <= blue_ball <= 16
                
                if red_valid and blue_valid:
                    valid_count += 1
                else:
                    print(f"    ❌ 无效数据: {draw}")
            
            print(f"  ✅ 有效数据: {valid_count}/{len(result['data'])}")
            
            if valid_count == len(result['data']):
                print(f"  🎉 所有数据都有效！")
                return True
            else:
                print(f"  ⚠️ 部分数据无效")
                return False
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            crawler.close()
        except:
            pass

def test_crawler_manager_with_real_data():
    """测试爬虫管理器的真实数据功能"""
    print("\n🎛️ 测试爬虫管理器...")
    print("-" * 40)
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        
        # 创建管理器
        manager = CrawlerManager()
        print("✅ 爬虫管理器创建成功")
        
        # 测试手动爬取
        print("\n📡 手动爬取双色球数据...")
        result = manager.manual_crawl('shuangseqiu', 3)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功，保存 {result.get('saved_count', 0)} 条记录")
            
            # 显示数据摘要
            print("\n📊 数据摘要:")
            summary = manager.get_data_summary()
            if 'shuangseqiu' in summary:
                ssq_data = summary['shuangseqiu']
                print(f"  - 总记录数: {ssq_data.get('total_records', 0)}")
                if 'latest_issue' in ssq_data and ssq_data['latest_issue']:
                    latest = ssq_data['latest_issue']
                    print(f"  - 最新期号: {latest.get('issue', 'N/A')}")
                    print(f"  - 开奖日期: {latest.get('date', 'N/A')}")
                else:
                    print(f"  - 最新期号: 暂无数据")
            
            return True
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            manager.close()
        except:
            pass

def main():
    """主函数"""
    print("🎯 真实数据爬虫测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success_count = 0
    total_tests = 2
    
    # 测试双色球爬虫
    if test_real_ssq_crawler():
        success_count += 1
    
    # 测试爬虫管理器
    if test_crawler_manager_with_real_data():
        success_count += 1
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
        print("\n✅ 真实数据爬虫系统功能正常:")
        print("  🔴 双色球爬虫 - 可从500彩票网获取真实开奖数据")
        print("  🎛️ 爬虫管理器 - 可统一管理和存储真实数据")
        print("  💾 数据验证 - 严格验证开奖号码的有效性")
        print("  🔄 自动切换 - 数据源失败时自动切换备用源")
        print("\n📝 数据来源:")
        print("  - 500彩票网双色球页面 (HTML解析)")
        print("  - 500彩票网历史数据页面 (HTML解析)")
        print("  - 模拟数据源 (备用)")
        print("\n🎯 可用于:")
        print("  - 彩票数据分析和统计")
        print("  - AI模型训练和预测")
        print("  - 历史趋势分析")
        print("  - 号码频率统计")
    else:
        print("❌ 部分测试失败")
        print("可能的原因:")
        print("  - 网络连接问题")
        print("  - 500彩票网页面结构变化")
        print("  - 反爬虫机制阻止访问")
        print("  - 数据解析逻辑需要调整")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
