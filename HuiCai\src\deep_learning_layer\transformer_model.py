#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Transformer模型
基于注意力机制的彩票号码预测模型

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, Dense, Dropout, LayerNormalization, 
    MultiHeadAttention, GlobalAveragePooling1D,
    Embedding, Concatenate, Add
)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from sklearn.preprocessing import StandardScaler, LabelEncoder
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime
import pickle
import os
import math


class PositionalEncoding(tf.keras.layers.Layer):
    """位置编码层"""
    
    def __init__(self, max_len: int = 1000, d_model: int = 128):
        super(PositionalEncoding, self).__init__()
        self.max_len = max_len
        self.d_model = d_model
        
        # 创建位置编码矩阵
        pe = np.zeros((max_len, d_model))
        position = np.arange(0, max_len).reshape(-1, 1)
        
        div_term = np.exp(np.arange(0, d_model, 2) * -(math.log(10000.0) / d_model))
        
        pe[:, 0::2] = np.sin(position * div_term)
        pe[:, 1::2] = np.cos(position * div_term)
        
        self.pe = tf.constant(pe, dtype=tf.float32)
    
    def call(self, x):
        seq_len = tf.shape(x)[1]
        return x + self.pe[:seq_len, :]


class TransformerBlock(tf.keras.layers.Layer):
    """Transformer块"""
    
    def __init__(self, d_model: int, num_heads: int, dff: int, dropout_rate: float = 0.1):
        super(TransformerBlock, self).__init__()
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.dff = dff
        self.dropout_rate = dropout_rate
        
        # 多头注意力
        self.mha = MultiHeadAttention(
            num_heads=num_heads,
            key_dim=d_model // num_heads,
            dropout=dropout_rate
        )
        
        # 前馈网络
        self.ffn = tf.keras.Sequential([
            Dense(dff, activation='relu'),
            Dropout(dropout_rate),
            Dense(d_model)
        ])
        
        # 层归一化
        self.layernorm1 = LayerNormalization(epsilon=1e-6)
        self.layernorm2 = LayerNormalization(epsilon=1e-6)
        
        # Dropout
        self.dropout1 = Dropout(dropout_rate)
        self.dropout2 = Dropout(dropout_rate)
    
    def call(self, x, training=None, mask=None):
        # 多头注意力
        attn_output = self.mha(x, x, attention_mask=mask, training=training)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(x + attn_output)
        
        # 前馈网络
        ffn_output = self.ffn(out1, training=training)
        ffn_output = self.dropout2(ffn_output, training=training)
        out2 = self.layernorm2(out1 + ffn_output)
        
        return out2


class TransformerPredictor:
    """Transformer预测器"""
    
    def __init__(self, config_manager, logger: logging.Logger, lottery_type: str):
        """
        初始化Transformer预测器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
            lottery_type: 彩票类型
        """
        self.config_manager = config_manager
        self.logger = logger
        self.lottery_type = lottery_type
        
        # Transformer配置
        self.dl_config = config_manager.get('deep_learning', {})
        self.transformer_config = self.dl_config.get('transformer', {})
        
        # 模型参数
        self.sequence_length = self.transformer_config.get('sequence_length', 30)
        self.d_model = self.transformer_config.get('d_model', 128)
        self.num_heads = self.transformer_config.get('num_heads', 8)
        self.num_layers = self.transformer_config.get('num_layers', 4)
        self.dff = self.transformer_config.get('dff', 512)
        self.dropout_rate = self.transformer_config.get('dropout_rate', 0.1)
        self.learning_rate = self.transformer_config.get('learning_rate', 0.0001)
        self.batch_size = self.transformer_config.get('batch_size', 32)
        self.epochs = self.transformer_config.get('epochs', 100)
        
        # 彩票配置
        self.lottery_config = config_manager.get_lottery_config(lottery_type)
        self.max_number = self._get_max_number()
        
        # 模型组件
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # 训练历史
        self.training_history = []
        
        # 模型保存路径
        self.model_save_path = config_manager.get('learning.model_save_path', 'data/models')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.info(f"Transformer预测器初始化完成: {lottery_type}")
    
    def _get_max_number(self) -> int:
        """获取彩票的最大号码"""
        if self.lottery_type == 'shuangseqiu':
            return max(self.lottery_config.get('red_ball_range', [1, 33])[1],
                      self.lottery_config.get('blue_ball_range', [1, 16])[1])
        elif self.lottery_type == 'daletou':
            return max(self.lottery_config.get('front_range', [1, 35])[1],
                      self.lottery_config.get('back_range', [1, 12])[1])
        elif self.lottery_type == 'fucai3d':
            return self.lottery_config.get('number_range', [0, 9])[1]
        else:
            return 50
    
    def build_model(self, input_shape: Tuple[int, int], output_dim: int) -> Model:
        """
        构建Transformer模型
        
        Args:
            input_shape: 输入形状
            output_dim: 输出维度
            
        Returns:
            Model: Transformer模型
        """
        try:
            # 输入层
            inputs = Input(shape=input_shape, name='input')
            
            # 投影到d_model维度
            x = Dense(self.d_model, name='input_projection')(inputs)
            
            # 位置编码
            pos_encoding = PositionalEncoding(
                max_len=self.sequence_length,
                d_model=self.d_model
            )
            x = pos_encoding(x)
            
            # Transformer块堆叠
            for i in range(self.num_layers):
                transformer_block = TransformerBlock(
                    d_model=self.d_model,
                    num_heads=self.num_heads,
                    dff=self.dff,
                    dropout_rate=self.dropout_rate
                )
                x = transformer_block(x)
            
            # 全局平均池化
            x = GlobalAveragePooling1D(name='global_avg_pool')(x)
            
            # 分类头
            x = Dense(256, activation='relu', name='classifier_1')(x)
            x = Dropout(self.dropout_rate, name='classifier_dropout_1')(x)
            
            x = Dense(128, activation='relu', name='classifier_2')(x)
            x = Dropout(self.dropout_rate / 2, name='classifier_dropout_2')(x)
            
            # 输出层
            outputs = Dense(output_dim, activation='softmax', name='output')(x)
            
            # 创建模型
            model = Model(inputs=inputs, outputs=outputs, name='transformer_model')
            
            # 编译模型
            model.compile(
                optimizer=Adam(learning_rate=self.learning_rate),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("Transformer模型构建完成")
            return model
            
        except Exception as e:
            self.logger.error(f"构建Transformer模型失败: {e}")
            raise
    
    def prepare_transformer_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备Transformer训练数据
        
        Args:
            data: 历史开奖数据
            
        Returns:
            Tuple: (X, y) 训练数据
        """
        try:
            sequences = []
            targets = []
            
            for i in range(len(data) - self.sequence_length):
                # 提取序列窗口
                window_data = data.iloc[i:i + self.sequence_length]
                target_data = data.iloc[i + self.sequence_length]
                
                # 构建丰富的特征序列
                sequence_features = []
                for idx, row in window_data.iterrows():
                    numbers = row['numbers']
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    
                    # 基础统计特征
                    basic_features = [
                        np.mean(numbers),                    # 平均值
                        np.std(numbers),                     # 标准差
                        max(numbers),                        # 最大值
                        min(numbers),                        # 最小值
                        len(set(numbers)),                   # 唯一数字个数
                        sum(numbers),                        # 和值
                        max(numbers) - min(numbers),         # 跨度
                        sum(1 for x in numbers if x % 2 == 1), # 奇数个数
                        sum(1 for x in numbers if x % 2 == 0), # 偶数个数
                    ]
                    
                    # 数字分布特征
                    distribution_features = []
                    for num_range in [(1, 10), (11, 20), (21, 30), (31, 40)]:
                        count = sum(1 for x in numbers if num_range[0] <= x <= num_range[1])
                        distribution_features.append(count)
                    
                    # 连号特征
                    consecutive_count = 0
                    sorted_numbers = sorted(numbers)
                    for j in range(len(sorted_numbers) - 1):
                        if sorted_numbers[j + 1] - sorted_numbers[j] == 1:
                            consecutive_count += 1
                    
                    # 时间特征（如果有日期信息）
                    time_features = []
                    if 'draw_date' in row:
                        draw_date = pd.to_datetime(row['draw_date'])
                        time_features = [
                            draw_date.weekday(),             # 星期几
                            draw_date.month,                 # 月份
                            draw_date.day,                   # 日期
                        ]
                    else:
                        time_features = [0, 0, 0]
                    
                    # 归一化的号码
                    normalized_numbers = [x / self.max_number for x in numbers]
                    
                    # 组合所有特征
                    all_features = (basic_features + distribution_features + 
                                  [consecutive_count] + time_features + normalized_numbers)
                    
                    sequence_features.append(all_features)
                
                sequences.append(sequence_features)
                
                # 目标：预测下一期的主要号码
                target_numbers = target_data['numbers']
                if isinstance(target_numbers, str):
                    target_numbers = eval(target_numbers)
                
                # 简化：预测第一个号码
                targets.append(target_numbers[0])
            
            X = np.array(sequences)
            y = np.array(targets)
            
            # 标准化特征
            original_shape = X.shape
            X_reshaped = X.reshape(-1, X.shape[-1])
            X_scaled = self.scaler.fit_transform(X_reshaped)
            X = X_scaled.reshape(original_shape)
            
            # 编码目标
            y_encoded = self.label_encoder.fit_transform(y)
            
            self.logger.info(f"Transformer数据准备完成: X.shape={X.shape}, y.shape={y_encoded.shape}")
            return X, y_encoded
            
        except Exception as e:
            self.logger.error(f"准备Transformer数据失败: {e}")
            raise
    
    def train(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练Transformer模型
        
        Args:
            data: 训练数据
            
        Returns:
            Dict: 训练结果
        """
        try:
            self.logger.info(f"开始训练Transformer模型: {self.lottery_type}")
            
            # 准备数据
            X, y = self.prepare_transformer_data(data)
            
            # 分割训练和验证集
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # 构建模型
            input_shape = (X.shape[1], X.shape[2])
            output_dim = len(np.unique(y))
            
            self.model = self.build_model(input_shape, output_dim)
            
            # 回调函数
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=15,
                    restore_best_weights=True,
                    verbose=1
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=8,
                    min_lr=1e-8,
                    verbose=1
                ),
                ModelCheckpoint(
                    filepath=os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_best.h5'),
                    monitor='val_loss',
                    save_best_only=True,
                    verbose=1
                )
            ]
            
            # 训练模型
            history = self.model.fit(
                X_train, y_train,
                batch_size=self.batch_size,
                epochs=self.epochs,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1
            )
            
            # 评估模型
            train_loss, train_acc = self.model.evaluate(X_train, y_train, verbose=0)
            val_loss, val_acc = self.model.evaluate(X_val, y_val, verbose=0)
            
            # 保存训练历史
            training_result = {
                'timestamp': datetime.now(),
                'lottery_type': self.lottery_type,
                'model_type': 'Transformer',
                'train_samples': len(X_train),
                'val_samples': len(X_val),
                'train_loss': float(train_loss),
                'train_accuracy': float(train_acc),
                'val_loss': float(val_loss),
                'val_accuracy': float(val_acc),
                'epochs_trained': len(history.history['loss']),
                'best_val_loss': float(min(history.history['val_loss'])),
                'model_params': {
                    'sequence_length': self.sequence_length,
                    'd_model': self.d_model,
                    'num_heads': self.num_heads,
                    'num_layers': self.num_layers,
                    'dff': self.dff,
                    'dropout_rate': self.dropout_rate,
                    'learning_rate': self.learning_rate
                }
            }
            
            self.training_history.append(training_result)
            
            # 保存模型组件
            self._save_model_components()
            
            self.logger.info(f"Transformer模型训练完成: 验证准确率={val_acc:.4f}")
            return training_result
            
        except Exception as e:
            self.logger.error(f"Transformer模型训练失败: {e}")
            raise
    
    def predict(self, recent_data: pd.DataFrame, num_predictions: int = 1) -> Dict[str, Any]:
        """
        使用Transformer模型进行预测
        
        Args:
            recent_data: 最近的数据
            num_predictions: 预测数量
            
        Returns:
            Dict: 预测结果
        """
        try:
            if self.model is None:
                raise ValueError("模型未训练或加载")
            
            if len(recent_data) < self.sequence_length:
                raise ValueError(f"数据不足，需要至少 {self.sequence_length} 期数据")
            
            # 准备输入数据（与训练时保持一致的特征工程）
            last_sequence = recent_data.tail(self.sequence_length)
            
            sequence_features = []
            for _, row in last_sequence.iterrows():
                numbers = row['numbers']
                if isinstance(numbers, str):
                    numbers = eval(numbers)
                
                # 构建与训练时相同的特征
                basic_features = [
                    np.mean(numbers),
                    np.std(numbers),
                    max(numbers),
                    min(numbers),
                    len(set(numbers)),
                    sum(numbers),
                    max(numbers) - min(numbers),
                    sum(1 for x in numbers if x % 2 == 1),
                    sum(1 for x in numbers if x % 2 == 0),
                ]
                
                distribution_features = []
                for num_range in [(1, 10), (11, 20), (21, 30), (31, 40)]:
                    count = sum(1 for x in numbers if num_range[0] <= x <= num_range[1])
                    distribution_features.append(count)
                
                consecutive_count = 0
                sorted_numbers = sorted(numbers)
                for j in range(len(sorted_numbers) - 1):
                    if sorted_numbers[j + 1] - sorted_numbers[j] == 1:
                        consecutive_count += 1
                
                time_features = [0, 0, 0]  # 简化处理
                normalized_numbers = [x / self.max_number for x in numbers]
                
                all_features = (basic_features + distribution_features + 
                              [consecutive_count] + time_features + normalized_numbers)
                
                sequence_features.append(all_features)
            
            # 标准化
            X_pred = np.array([sequence_features])
            original_shape = X_pred.shape
            X_pred_reshaped = X_pred.reshape(-1, X_pred.shape[-1])
            X_pred_scaled = self.scaler.transform(X_pred_reshaped)
            X_pred = X_pred_scaled.reshape(original_shape)
            
            # 预测
            predictions = []
            for _ in range(num_predictions):
                pred_proba = self.model.predict(X_pred, verbose=0)
                pred_class = np.argmax(pred_proba[0])
                
                predicted_number = self.label_encoder.inverse_transform([pred_class])[0]
                confidence = float(np.max(pred_proba[0]))
                
                predictions.append({
                    'predicted_number': int(predicted_number),
                    'confidence': confidence,
                    'probability_distribution': pred_proba[0].tolist()
                })
            
            result = {
                'lottery_type': self.lottery_type,
                'prediction_date': datetime.now(),
                'model_type': 'Transformer',
                'predictions': predictions,
                'input_sequence_length': self.sequence_length,
                'model_accuracy': self.training_history[-1]['val_accuracy'] if self.training_history else 0.0
            }
            
            self.logger.info(f"Transformer预测完成: {len(predictions)} 个预测")
            return result
            
        except Exception as e:
            self.logger.error(f"Transformer预测失败: {e}")
            return {}
    
    def _save_model_components(self):
        """保存模型组件"""
        try:
            # 保存模型
            model_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_model.h5')
            self.model.save(model_path)
            
            # 保存预处理器
            scaler_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_scaler.pkl')
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            encoder_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_encoder.pkl')
            with open(encoder_path, 'wb') as f:
                pickle.dump(self.label_encoder, f)
            
            # 保存训练历史
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_history.pkl')
            with open(history_path, 'wb') as f:
                pickle.dump(self.training_history, f)
            
            self.logger.info("Transformer模型组件保存完成")
            
        except Exception as e:
            self.logger.error(f"保存Transformer模型组件失败: {e}")
    
    def load_model_components(self) -> bool:
        """加载模型组件"""
        try:
            model_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_model.h5')
            if os.path.exists(model_path):
                self.model = tf.keras.models.load_model(model_path, custom_objects={
                    'PositionalEncoding': PositionalEncoding,
                    'TransformerBlock': TransformerBlock
                })
            else:
                return False
            
            scaler_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_scaler.pkl')
            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
            
            encoder_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_encoder.pkl')
            if os.path.exists(encoder_path):
                with open(encoder_path, 'rb') as f:
                    self.label_encoder = pickle.load(f)
            
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_transformer_history.pkl')
            if os.path.exists(history_path):
                with open(history_path, 'rb') as f:
                    self.training_history = pickle.load(f)
            
            self.logger.info("Transformer模型组件加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载Transformer模型组件失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.training_history:
            return {}
        
        latest_training = self.training_history[-1]
        
        return {
            'model_type': 'Transformer',
            'lottery_type': self.lottery_type,
            'last_training': latest_training['timestamp'],
            'validation_accuracy': latest_training['val_accuracy'],
            'validation_loss': latest_training['val_loss'],
            'epochs_trained': latest_training['epochs_trained'],
            'model_parameters': latest_training['model_params'],
            'total_trainings': len(self.training_history)
        }
