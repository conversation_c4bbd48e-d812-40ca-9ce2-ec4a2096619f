<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HuiCai慧彩智能体 - 仪表盘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .algorithm-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .algorithm-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .algorithm-card:hover {
            transform: translateY(-5px);
        }
        
        .algorithm-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .algorithm-card h3 {
            font-size: 1.1em;
            margin-bottom: 8px;
            color: #333;
        }
        
        .algorithm-card .accuracy {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .lottery-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .lottery-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .lottery-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .latest-result {
            text-align: center;
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 12px;
        }
        
        .result-info {
            margin-bottom: 15px;
        }
        
        .issue-date {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 5px;
        }

        .lottery-balls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin: 15px 0;
        }

        .ball {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .red-ball {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .blue-ball {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .front-ball {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .back-ball {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .plus {
            font-size: 24px;
            color: #666;
            margin: 0 8px;
            font-weight: bold;
        }

        .countdown {
            font-size: 0.9em;
            color: #666;
            margin-top: 10px;
            font-style: italic;
        }

        .predictions {
            margin-top: 20px;
        }

        .prediction-item {
            margin-bottom: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .prediction-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 0.9em;
        }

        .prediction-balls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-wrap: wrap;
        }

        .prediction-ball {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .update-section {
            text-align: center;
            margin-top: 30px;
        }

        .update-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            font-weight: bold;
        }

        .update-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .update-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            margin-top: 10px;
            color: #666;
        }

        @media (max-width: 1200px) {
            .algorithm-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .lottery-grid {
                grid-template-columns: 1fr;
            }

            .algorithm-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .ball, .prediction-ball {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .prediction-ball {
                width: 24px;
                height: 24px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 HuiCai慧彩智能体</h1>
            <p>智能彩票分析与预测系统</p>
        </div>

        <!-- 四个算法卡片 -->
        <div class="algorithm-cards">
            <div class="algorithm-card">
                <div class="icon">🧠</div>
                <h3>LSTM神经网络</h3>
                <div class="accuracy" id="lstm-accuracy">85.2%</div>
            </div>
            <div class="algorithm-card">
                <div class="icon">🌲</div>
                <h3>随机森林</h3>
                <div class="accuracy" id="rf-accuracy">78.9%</div>
            </div>
            <div class="algorithm-card">
                <div class="icon">📊</div>
                <h3>支持向量机</h3>
                <div class="accuracy" id="svm-accuracy">72.4%</div>
            </div>
            <div class="algorithm-card">
                <div class="icon">🔮</div>
                <h3>集成学习</h3>
                <div class="accuracy" id="ensemble-accuracy">88.7%</div>
            </div>
        </div>

        <!-- 双色球和大乐透区域 -->
        <div class="lottery-grid">
            <!-- 双色球区域 -->
            <div class="lottery-section">
                <div class="lottery-title">🔴 双色球</div>

                <!-- 最新开奖结果 -->
                <div class="latest-result">
                    <div class="result-info">
                        <div class="issue-date" id="ssq-issue">期号: 2025001</div>
                        <div class="issue-date" id="ssq-date">开奖日期: 2025-01-15</div>
                    </div>

                    <div class="lottery-balls" id="ssq-balls">
                        <div class="ball red-ball">03</div>
                        <div class="ball red-ball">08</div>
                        <div class="ball red-ball">16</div>
                        <div class="ball red-ball">21</div>
                        <div class="ball red-ball">28</div>
                        <div class="ball red-ball">33</div>
                        <div class="plus">+</div>
                        <div class="ball blue-ball">12</div>
                    </div>

                    <div class="countdown" id="ssq-countdown">
                        距离下次开奖: 2天 14小时 32分钟
                    </div>
                </div>

                <!-- 四个算法预测 -->
                <div class="predictions">
                    <div class="prediction-item">
                        <div class="prediction-title">🧠 LSTM神经网络预测</div>
                        <div class="prediction-balls" id="ssq-lstm-prediction">
                            <div class="prediction-ball red-ball">05</div>
                            <div class="prediction-ball red-ball">12</div>
                            <div class="prediction-ball red-ball">19</div>
                            <div class="prediction-ball red-ball">26</div>
                            <div class="prediction-ball red-ball">31</div>
                            <div class="prediction-ball red-ball">33</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball blue-ball">08</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">🌲 随机森林预测</div>
                        <div class="prediction-balls" id="ssq-rf-prediction">
                            <div class="prediction-ball red-ball">02</div>
                            <div class="prediction-ball red-ball">15</div>
                            <div class="prediction-ball red-ball">22</div>
                            <div class="prediction-ball red-ball">27</div>
                            <div class="prediction-ball red-ball">30</div>
                            <div class="prediction-ball red-ball">32</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball blue-ball">11</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">📊 支持向量机预测</div>
                        <div class="prediction-balls" id="ssq-svm-prediction">
                            <div class="prediction-ball red-ball">07</div>
                            <div class="prediction-ball red-ball">14</div>
                            <div class="prediction-ball red-ball">18</div>
                            <div class="prediction-ball red-ball">25</div>
                            <div class="prediction-ball red-ball">29</div>
                            <div class="prediction-ball red-ball">31</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball blue-ball">06</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">🔮 集成学习预测</div>
                        <div class="prediction-balls" id="ssq-ensemble-prediction">
                            <div class="prediction-ball red-ball">01</div>
                            <div class="prediction-ball red-ball">09</div>
                            <div class="prediction-ball red-ball">17</div>
                            <div class="prediction-ball red-ball">23</div>
                            <div class="prediction-ball red-ball">28</div>
                            <div class="prediction-ball red-ball">33</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball blue-ball">15</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大乐透区域 -->
            <div class="lottery-section">
                <div class="lottery-title">🔵 大乐透</div>

                <!-- 最新开奖结果 -->
                <div class="latest-result">
                    <div class="result-info">
                        <div class="issue-date" id="dlt-issue">期号: 25001</div>
                        <div class="issue-date" id="dlt-date">开奖日期: 2025-01-15</div>
                    </div>

                    <div class="lottery-balls" id="dlt-balls">
                        <div class="ball front-ball">05</div>
                        <div class="ball front-ball">12</div>
                        <div class="ball front-ball">19</div>
                        <div class="ball front-ball">26</div>
                        <div class="ball front-ball">31</div>
                        <div class="plus">+</div>
                        <div class="ball back-ball">03</div>
                        <div class="ball back-ball">08</div>
                    </div>

                    <div class="countdown" id="dlt-countdown">
                        距离下次开奖: 1天 8小时 15分钟
                    </div>
                </div>

                <!-- 四个算法预测 -->
                <div class="predictions">
                    <div class="prediction-item">
                        <div class="prediction-title">🧠 LSTM神经网络预测</div>
                        <div class="prediction-balls" id="dlt-lstm-prediction">
                            <div class="prediction-ball front-ball">02</div>
                            <div class="prediction-ball front-ball">15</div>
                            <div class="prediction-ball front-ball">22</div>
                            <div class="prediction-ball front-ball">28</div>
                            <div class="prediction-ball front-ball">34</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball back-ball">05</div>
                            <div class="prediction-ball back-ball">11</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">🌲 随机森林预测</div>
                        <div class="prediction-balls" id="dlt-rf-prediction">
                            <div class="prediction-ball front-ball">08</div>
                            <div class="prediction-ball front-ball">16</div>
                            <div class="prediction-ball front-ball">23</div>
                            <div class="prediction-ball front-ball">29</div>
                            <div class="prediction-ball front-ball">33</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball back-ball">02</div>
                            <div class="prediction-ball back-ball">09</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">📊 支持向量机预测</div>
                        <div class="prediction-balls" id="dlt-svm-prediction">
                            <div class="prediction-ball front-ball">04</div>
                            <div class="prediction-ball front-ball">11</div>
                            <div class="prediction-ball front-ball">18</div>
                            <div class="prediction-ball front-ball">25</div>
                            <div class="prediction-ball front-ball">32</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball back-ball">07</div>
                            <div class="prediction-ball back-ball">12</div>
                        </div>
                    </div>

                    <div class="prediction-item">
                        <div class="prediction-title">🔮 集成学习预测</div>
                        <div class="prediction-balls" id="dlt-ensemble-prediction">
                            <div class="prediction-ball front-ball">06</div>
                            <div class="prediction-ball front-ball">13</div>
                            <div class="prediction-ball front-ball">20</div>
                            <div class="prediction-ball front-ball">27</div>
                            <div class="prediction-ball front-ball">35</div>
                            <div class="plus">+</div>
                            <div class="prediction-ball back-ball">01</div>
                            <div class="prediction-ball back-ball">10</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更新数据按钮 -->
        <div class="update-section">
            <button class="update-btn" id="updateBtn" onclick="updateData()">
                🔄 更新数据
            </button>
            <div class="loading" id="loading">
                <i class="fas fa-spinner fa-spin"></i> 正在更新数据...
            </div>
        </div>
    </div>

    <script>
        // 更新数据函数
        async function updateData() {
            const updateBtn = document.getElementById('updateBtn');
            const loading = document.getElementById('loading');

            // 禁用按钮并显示加载状态
            updateBtn.disabled = true;
            loading.style.display = 'block';
            updateBtn.textContent = '🔄 更新中...';

            try {
                // 调用后端API更新数据
                const response = await fetch('/api/update-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('更新结果:', result);

                    if (result.status === 'success') {
                        console.log('收到更新结果:', result);

                        // 更新双色球数据
                        if (result.shuangseqiu && result.shuangseqiu.data) {
                            console.log('准备更新双色球数据:', result.shuangseqiu.data);
                            updateSSQData([result.shuangseqiu.data]);
                            console.log('双色球数据已更新');
                        } else {
                            console.warn('双色球数据不存在或格式错误:', result.shuangseqiu);
                        }

                        // 更新大乐透数据
                        if (result.daletou && result.daletou.data) {
                            console.log('准备更新大乐透数据:', result.daletou.data);
                            updateDLTData([result.daletou.data]);
                            console.log('大乐透数据已更新');
                        } else {
                            console.warn('大乐透数据不存在或格式错误:', result.daletou);
                        }

                        // 更新算法准确率
                        if (result.algorithms) {
                            updateAlgorithmAccuracy(result.algorithms);
                            console.log('算法准确率已更新');
                        }

                        // 更新预测数据
                        updatePredictions();

                        // 显示成功消息
                        const ssqCount = result.shuangseqiu?.saved_count || 0;
                        const dltCount = result.daletou?.saved_count || 0;
                        alert(`数据更新成功！\n双色球: ${ssqCount}条\n大乐透: ${dltCount}条`);
                    } else {
                        console.error('更新失败:', result);
                        alert(`数据更新失败: ${result.message || result.error || '未知错误'}`);
                    }
                } else {
                    const errorData = await response.json();
                    alert(`数据更新失败: ${errorData.message || errorData.error || '服务器错误'}`);
                }
            } catch (error) {
                console.error('更新数据时出错:', error);
                alert('网络错误，请检查连接');
            } finally {
                // 恢复按钮状态
                updateBtn.disabled = false;
                loading.style.display = 'none';
                updateBtn.textContent = '🔄 更新数据';
            }
        }

        // 更新双色球数据
        function updateSSQData(data) {
            if (data && data.length > 0) {
                const latest = data[0];

                // 验证数据完整性
                if (!latest.red_balls || !Array.isArray(latest.red_balls) || latest.red_balls.length !== 6) {
                    console.error('双色球红球数据无效:', latest.red_balls);
                    return;
                }

                if (!latest.blue_ball || latest.blue_ball < 1 || latest.blue_ball > 16) {
                    console.error('双色球蓝球数据无效:', latest.blue_ball);
                    return;
                }

                // 更新期号和日期
                document.getElementById('ssq-issue').textContent = `期号: ${latest.issue || '未知期号'}`;
                document.getElementById('ssq-date').textContent = `开奖日期: ${latest.date || '未知日期'}`;

                // 更新开奖号码
                const ballsContainer = document.getElementById('ssq-balls');
                ballsContainer.innerHTML = '';

                // 红球
                latest.red_balls.forEach(ball => {
                    const ballDiv = document.createElement('div');
                    ballDiv.className = 'ball red-ball';
                    ballDiv.textContent = ball.toString().padStart(2, '0');
                    ballsContainer.appendChild(ballDiv);
                });

                // 加号
                const plusDiv = document.createElement('div');
                plusDiv.className = 'plus';
                plusDiv.textContent = '+';
                ballsContainer.appendChild(plusDiv);

                // 蓝球
                const blueBallDiv = document.createElement('div');
                blueBallDiv.className = 'ball blue-ball';
                blueBallDiv.textContent = latest.blue_ball.toString().padStart(2, '0');
                ballsContainer.appendChild(blueBallDiv);

                console.log('双色球数据更新完成:', latest);
            } else {
                console.warn('双色球数据为空或格式错误');
            }
        }

        // 更新大乐透数据
        function updateDLTData(data) {
            if (data && data.length > 0) {
                const latest = data[0];

                // 验证数据完整性
                if (!latest.front_balls || !Array.isArray(latest.front_balls) || latest.front_balls.length !== 5) {
                    console.error('大乐透前区球数据无效:', latest.front_balls);
                    return;
                }

                if (!latest.back_balls || !Array.isArray(latest.back_balls) || latest.back_balls.length !== 2) {
                    console.error('大乐透后区球数据无效:', latest.back_balls);
                    return;
                }

                // 验证号码范围
                const frontValid = latest.front_balls.every(ball => ball >= 1 && ball <= 35);
                const backValid = latest.back_balls.every(ball => ball >= 1 && ball <= 12);

                if (!frontValid || !backValid) {
                    console.error('大乐透号码范围无效:', { front_balls: latest.front_balls, back_balls: latest.back_balls });
                    return;
                }

                // 更新期号和日期
                document.getElementById('dlt-issue').textContent = `期号: ${latest.issue || '未知期号'}`;
                document.getElementById('dlt-date').textContent = `开奖日期: ${latest.date || '未知日期'}`;

                // 更新开奖号码
                const ballsContainer = document.getElementById('dlt-balls');
                ballsContainer.innerHTML = '';

                // 前区球
                latest.front_balls.forEach(ball => {
                    const ballDiv = document.createElement('div');
                    ballDiv.className = 'ball front-ball';
                    ballDiv.textContent = ball.toString().padStart(2, '0');
                    ballsContainer.appendChild(ballDiv);
                });

                // 加号
                const plusDiv = document.createElement('div');
                plusDiv.className = 'plus';
                plusDiv.textContent = '+';
                ballsContainer.appendChild(plusDiv);

                // 后区球
                latest.back_balls.forEach(ball => {
                    const ballDiv = document.createElement('div');
                    ballDiv.className = 'ball back-ball';
                    ballDiv.textContent = ball.toString().padStart(2, '0');
                    ballsContainer.appendChild(ballDiv);
                });

                console.log('大乐透数据更新完成:', latest);
            } else {
                console.warn('大乐透数据为空或格式错误');
            }
        }

        // 更新算法准确率
        function updateAlgorithmAccuracy(algorithms) {
            if (algorithms.lstm) {
                document.getElementById('lstm-accuracy').textContent = `${algorithms.lstm.toFixed(1)}%`;
            }
            if (algorithms.random_forest) {
                document.getElementById('rf-accuracy').textContent = `${algorithms.random_forest.toFixed(1)}%`;
            }
            if (algorithms.svm) {
                document.getElementById('svm-accuracy').textContent = `${algorithms.svm.toFixed(1)}%`;
            }
            if (algorithms.ensemble) {
                document.getElementById('ensemble-accuracy').textContent = `${algorithms.ensemble.toFixed(1)}%`;
            }
        }

        // 更新预测数据
        async function updatePredictions() {
            try {
                const response = await fetch('/api/predictions');
                if (response.ok) {
                    const predictions = await response.json();

                    // 更新双色球预测
                    if (predictions.shuangseqiu) {
                        updateSSQPredictions(predictions.shuangseqiu);
                    }

                    // 更新大乐透预测
                    if (predictions.daletou) {
                        updateDLTPredictions(predictions.daletou);
                    }
                }
            } catch (error) {
                console.error('获取预测数据失败:', error);
            }
        }

        // 更新双色球预测
        function updateSSQPredictions(predictions) {
            const algorithms = ['lstm', 'rf', 'svm', 'ensemble'];

            algorithms.forEach(algo => {
                if (predictions[algo]) {
                    const container = document.getElementById(`ssq-${algo}-prediction`);
                    if (container) {
                        container.innerHTML = '';

                        // 红球
                        predictions[algo].red_balls.forEach(ball => {
                            const ballDiv = document.createElement('div');
                            ballDiv.className = 'prediction-ball red-ball';
                            ballDiv.textContent = ball.toString().padStart(2, '0');
                            container.appendChild(ballDiv);
                        });

                        // 加号
                        const plusDiv = document.createElement('div');
                        plusDiv.className = 'plus';
                        plusDiv.textContent = '+';
                        container.appendChild(plusDiv);

                        // 蓝球
                        const blueBallDiv = document.createElement('div');
                        blueBallDiv.className = 'prediction-ball blue-ball';
                        blueBallDiv.textContent = predictions[algo].blue_ball.toString().padStart(2, '0');
                        container.appendChild(blueBallDiv);
                    }
                }
            });
        }

        // 更新大乐透预测
        function updateDLTPredictions(predictions) {
            const algorithms = ['lstm', 'rf', 'svm', 'ensemble'];

            algorithms.forEach(algo => {
                if (predictions[algo]) {
                    const container = document.getElementById(`dlt-${algo}-prediction`);
                    if (container) {
                        container.innerHTML = '';

                        // 前区球
                        predictions[algo].front_balls.forEach(ball => {
                            const ballDiv = document.createElement('div');
                            ballDiv.className = 'prediction-ball front-ball';
                            ballDiv.textContent = ball.toString().padStart(2, '0');
                            container.appendChild(ballDiv);
                        });

                        // 加号
                        const plusDiv = document.createElement('div');
                        plusDiv.className = 'plus';
                        plusDiv.textContent = '+';
                        container.appendChild(plusDiv);

                        // 后区球
                        predictions[algo].back_balls.forEach(ball => {
                            const ballDiv = document.createElement('div');
                            ballDiv.className = 'prediction-ball back-ball';
                            ballDiv.textContent = ball.toString().padStart(2, '0');
                            container.appendChild(ballDiv);
                        });
                    }
                }
            });
        }

        // 更新倒计时
        function updateCountdown() {
            // 双色球开奖时间：每周二、四、日 21:15
            // 大乐透开奖时间：每周一、三、六 20:30

            const now = new Date();

            // 计算双色球下次开奖时间
            const ssqNext = getNextDrawTime([2, 4, 0], 21, 15); // 周二、四、日
            const ssqCountdown = getCountdownText(ssqNext);
            document.getElementById('ssq-countdown').textContent = `距离下次开奖: ${ssqCountdown}`;

            // 计算大乐透下次开奖时间
            const dltNext = getNextDrawTime([1, 3, 6], 20, 30); // 周一、三、六
            const dltCountdown = getCountdownText(dltNext);
            document.getElementById('dlt-countdown').textContent = `距离下次开奖: ${dltCountdown}`;
        }

        // 获取下次开奖时间
        function getNextDrawTime(weekdays, hour, minute) {
            const now = new Date();
            const today = now.getDay();

            for (let i = 0; i < 7; i++) {
                const checkDate = new Date(now);
                checkDate.setDate(now.getDate() + i);
                const checkDay = checkDate.getDay();

                if (weekdays.includes(checkDay)) {
                    checkDate.setHours(hour, minute, 0, 0);
                    if (checkDate > now) {
                        return checkDate;
                    }
                }
            }

            return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        }

        // 获取倒计时文本
        function getCountdownText(targetTime) {
            const now = new Date();
            const diff = targetTime - now;

            if (diff <= 0) return '已开奖';

            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            return `${days}天 ${hours}小时 ${minutes}分钟`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新倒计时
            updateCountdown();

            // 每分钟更新一次倒计时
            setInterval(updateCountdown, 60000);

            // 加载初始预测数据
            updatePredictions();
        });
    </script>
</body>
</html>
