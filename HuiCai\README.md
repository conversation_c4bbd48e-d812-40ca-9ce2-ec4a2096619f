# HuiCai 慧彩 - 彩票分析智能体系统

## 项目概述

HuiCai（慧彩）是一个专为中国彩票市场设计的智能分析系统，具备自主学习和进化能力。系统拒绝纯随机算法，专注于通过机器学习、深度学习和中国特色算法来分析彩票数据模式。

## 已实现功能

### ✅ 第一阶段功能（基础架构）

1. **基础架构**
   - 模块化设计，单一职责原则
   - 异步编程架构
   - 完整的配置管理系统
   - 日志管理和监控

2. **数据采集系统**
   - 爬虫管理器，支持多彩种并发爬取
   - 双色球专用爬虫（支持主备数据源）
   - 反爬虫机制（User-Agent轮换、请求延迟、重试机制）
   - 数据验证和存储

3. **增量学习框架**
   - 基于scikit-learn的增量学习器
   - 支持在线学习和模型更新
   - 特征工程和数据预处理
   - 模型性能评估和历史记录

4. **任务调度系统**
   - 基于APScheduler的定时任务
   - 自动数据爬取调度
   - 模型训练调度
   - 系统维护任务

5. **数据存储**
   - PostgreSQL数据库支持
   - Redis缓存系统
   - 数据模型设计
   - 异步数据库操作

6. **交互界面**
   - 命令行界面（CLI）
   - 实时状态监控
   - 手动操作支持

### ✅ 第二阶段功能（中国特色算法 + 反爬虫增强）

1. **中国特色算法模块**
   - 五行八卦分析算法
   - 生肖周期预测算法
   - 节气时令影响分析
   - 易经八卦数字映射
   - 吉凶数字评估系统
   - 综合文化分析引擎

2. **反爬虫系统增强**
   - 智能代理池管理
   - User-Agent动态轮换
   - 验证码自动识别
   - 智能延迟控制
   - 反爬虫检测与应对
   - 自适应重试机制

3. **爬虫系统扩展**
   - 大乐透专用爬虫
   - 福彩3D专用爬虫
   - 多数据源支持
   - 数据格式标准化

4. **预测算法融合**
   - 机器学习 + 传统文化双重预测
   - 综合置信度评估
   - 智能权重分配
   - 策略建议生成

## 安装和使用

### 环境要求
- Python 3.8+
- PostgreSQL 12+
- Redis 6+

### 快速开始

```bash
# 进入项目目录
cd HuiCai

# 安装依赖
pip install -r requirements.txt

# 配置数据库（首次运行会自动创建配置文件）
# 编辑 config/config.yaml 文件，配置数据库连接信息

# 启动系统
python main.py

# 第二阶段功能演示（无需数据库）
python demo_stage2.py
```

### 基本使用

启动系统后，您可以使用以下命令：

```bash
# 查看系统状态
HuiCai> status

# 爬取双色球最新数据
HuiCai> crawl latest shuangseqiu

# 传统机器学习预测
HuiCai> predict shuangseqiu

# 中国特色算法预测
HuiCai> predict chinese shuangseqiu

# 综合预测（ML + 传统文化）
HuiCai> predict comprehensive shuangseqiu

# 查看模型信息
HuiCai> model info shuangseqiu

# 查看定时任务
HuiCai> jobs list

# 查看帮助
HuiCai> help
```

## 系统架构

### 核心组件

1. **配置管理器** (`config_manager.py`)
   - 统一配置管理
   - 支持YAML格式
   - 动态配置加载

2. **日志管理器** (`log_manager.py`)
   - 多级别日志
   - 文件轮转
   - 控制台和文件双输出

3. **数据存储** (`data_storage.py`)
   - PostgreSQL连接池
   - Redis缓存
   - 异步数据操作

4. **爬虫系统**
   - 爬虫基类 (`base_crawler.py`)
   - 双色球爬虫 (`shuangseqiu_crawler.py`)
   - 爬虫管理器 (`crawler_manager.py`)

5. **增量学习器** (`incremental_learner.py`)
   - 在线学习算法
   - 模型自动更新
   - 性能评估

6. **任务调度器** (`scheduler.py`)
   - 定时任务管理
   - 自动化运维

7. **CLI界面** (`cli_interface.py`)
   - 交互式操作
   - 实时监控

### 数据流程

1. **数据采集**: 爬虫定时采集最新开奖数据
2. **数据存储**: 验证后存入PostgreSQL数据库
3. **特征提取**: 从历史数据中提取特征
4. **模型训练**: 增量学习器持续更新模型
5. **预测生成**: 基于训练好的模型生成预测

## 配置说明

系统首次运行时会自动创建配置文件 `config/config.yaml`，主要配置项：

```yaml
database:
  host: localhost
  port: 5432
  name: huicai
  user: huicai
  password: huicai123

redis:
  host: localhost
  port: 6379
  db: 0

crawler:
  request_delay: 1
  timeout: 30
  retry_times: 3

learning:
  batch_size: 32
  learning_rate: 0.001
```

## 支持的彩票类型

### 当前支持
- ✅ **双色球**: 完整实现，包括数据爬取、传统预测和文化分析
- ✅ **大乐透**: 完整实现，支持前区后区分离处理
- ✅ **福彩3D**: 完整实现，包含形态分析和位置统计

### 计划支持（后续阶段）
- 🔄 **快3**: 待实现
- 🔄 **时时彩**: 待实现
- 🔄 **排列三**: 待实现

## 开发计划

### ✅ 第一阶段（已完成）
- 基础架构和增量学习框架
- 中国彩票数据采集系统
- 双色球爬虫和预测

### ✅ 第二阶段（已完成）
- 中国特色算法模块（五行八卦、生肖节气等）
- 反爬虫系统增强（代理池、验证码识别等）
- 大乐透和福彩3D爬虫实现
- 综合预测算法融合

### 🔄 第三阶段（进行中）
- 深度学习模型集成（LSTM、Transformer）
- 强化学习机制
- 模型集成策略优化

### 📋 第四阶段（计划中）
- 智能决策系统
- 持续进化机制
- Web界面开发
- 移动端应用

## 注意事项

⚠️ **重要提醒**:
- 本系统仅用于数据分析和学术研究
- 彩票本质上是随机事件，任何预测都不能保证准确性
- 请理性对待彩票，不要过度投注
- 使用本系统的风险由用户自行承担

## 技术特点

1. **异步架构**: 全面采用asyncio，高并发处理
2. **模块化设计**: 单一职责，易于维护和扩展
3. **增量学习**: 模型持续更新，适应数据变化
4. **反爬虫机制**: 多重策略避免被封禁
5. **容错设计**: 完善的异常处理和重试机制
6. **监控完善**: 详细的日志和状态监控

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

---

**HuiCai 慧彩团队**  
专注于彩票数据分析和智能预测技术
