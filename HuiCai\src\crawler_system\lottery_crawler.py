#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票数据爬虫
从官方网站爬取最新的开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import time
import random
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import asyncio
import aiohttp


class LotteryCrawler:
    """彩票数据爬虫"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        self.config = config_manager
        self.logger = logger
        
        # 爬虫配置
        self.crawler_config = {
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
            ],
            'request_delay': (1, 3),  # 请求间隔（秒）
            'timeout': 30,
            'max_retries': 3,
            'proxy_enabled': False
        }
        
        # 数据源配置
        self.data_sources = {
            'shuangseqiu': {
                'name': '双色球',
                'url': 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
                'backup_urls': [
                    'http://www.lottery.gov.cn/historykj/history_ssq.jspx',
                    'https://datachart.500.com/ssq/history/newinc/history.php'
                ],
                'parser': self._parse_shuangseqiu_data
            },
            'daletou': {
                'name': '大乐透',
                'url': 'https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry',
                'backup_urls': [
                    'http://www.lottery.gov.cn/historykj/history_dlt.jspx'
                ],
                'parser': self._parse_daletou_data
            },
            'fucai3d': {
                'name': '福彩3D',
                'url': 'https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
                'backup_urls': [
                    'http://www.lottery.gov.cn/historykj/history_3d.jspx'
                ],
                'parser': self._parse_fucai3d_data
            }
        }
        
        # 反爬虫措施
        self.anti_crawler = {
            'session': requests.Session(),
            'last_request_time': 0,
            'request_count': 0,
            'daily_limit': 1000
        }
        
        self._setup_session()
    
    def _setup_session(self):
        """设置请求会话"""
        session = self.anti_crawler['session']
        
        # 设置默认headers
        session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def _get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.crawler_config['user_agents'])
    
    def _wait_between_requests(self):
        """请求间等待"""
        current_time = time.time()
        elapsed = current_time - self.anti_crawler['last_request_time']
        
        min_delay, max_delay = self.crawler_config['request_delay']
        required_delay = random.uniform(min_delay, max_delay)
        
        if elapsed < required_delay:
            time.sleep(required_delay - elapsed)
        
        self.anti_crawler['last_request_time'] = time.time()
        self.anti_crawler['request_count'] += 1
    
    def _make_request(self, url: str, params: Dict = None, 
                     headers: Dict = None) -> Optional[requests.Response]:
        """发起HTTP请求"""
        try:
            # 检查请求限制
            if self.anti_crawler['request_count'] >= self.crawler_config['daily_limit']:
                self.logger.warning("已达到每日请求限制")
                return None
            
            # 等待间隔
            self._wait_between_requests()
            
            # 设置headers
            request_headers = {
                'User-Agent': self._get_random_user_agent()
            }
            if headers:
                request_headers.update(headers)
            
            # 发起请求
            response = self.anti_crawler['session'].get(
                url,
                params=params,
                headers=request_headers,
                timeout=self.crawler_config['timeout']
            )
            
            response.raise_for_status()
            
            self.logger.debug(f"请求成功: {url}")
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {url}, {e}")
            return None
    
    def crawl_lottery_data(self, lottery_type: str, 
                          start_date: str = None, 
                          end_date: str = None,
                          max_pages: int = 10) -> Dict[str, Any]:
        """
        爬取彩票数据
        
        Args:
            lottery_type: 彩票类型
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            max_pages: 最大爬取页数
            
        Returns:
            爬取结果
        """
        try:
            if lottery_type not in self.data_sources:
                return {
                    'status': 'failed',
                    'error': f'不支持的彩票类型: {lottery_type}'
                }
            
            source_config = self.data_sources[lottery_type]
            
            self.logger.info(f"开始爬取{source_config['name']}数据")
            
            # 尝试主要数据源
            data = self._crawl_from_source(
                source_config['url'], 
                source_config['parser'],
                lottery_type,
                start_date,
                end_date,
                max_pages
            )
            
            # 如果主要数据源失败，尝试备用数据源
            if not data and source_config.get('backup_urls'):
                for backup_url in source_config['backup_urls']:
                    self.logger.info(f"尝试备用数据源: {backup_url}")
                    data = self._crawl_from_source(
                        backup_url,
                        source_config['parser'],
                        lottery_type,
                        start_date,
                        end_date,
                        max_pages
                    )
                    if data:
                        break
            
            if data:
                result = {
                    'status': 'success',
                    'lottery_type': lottery_type,
                    'data_count': len(data),
                    'data': data,
                    'crawl_time': datetime.now().isoformat(),
                    'source': source_config['name']
                }
                
                self.logger.info(f"{source_config['name']}数据爬取完成: {len(data)}条记录")
                return result
            else:
                return {
                    'status': 'failed',
                    'error': '所有数据源都无法获取数据',
                    'lottery_type': lottery_type
                }
                
        except Exception as e:
            self.logger.error(f"爬取{lottery_type}数据失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'lottery_type': lottery_type
            }
    
    def _crawl_from_source(self, url: str, parser_func, lottery_type: str,
                          start_date: str, end_date: str, max_pages: int) -> List[Dict]:
        """从指定数据源爬取数据"""
        try:
            all_data = []
            page = 1
            
            while page <= max_pages:
                # 构建请求参数
                params = self._build_request_params(
                    lottery_type, page, start_date, end_date
                )
                
                # 发起请求
                response = self._make_request(url, params)
                if not response:
                    break
                
                # 解析数据
                page_data = parser_func(response, lottery_type)
                if not page_data:
                    break
                
                all_data.extend(page_data)
                
                # 检查是否还有更多数据
                if len(page_data) == 0:
                    break
                
                page += 1
                
                # 随机延迟
                time.sleep(random.uniform(1, 2))
            
            return all_data
            
        except Exception as e:
            self.logger.error(f"从数据源爬取失败: {url}, {e}")
            return []
    
    def _build_request_params(self, lottery_type: str, page: int,
                            start_date: str, end_date: str) -> Dict:
        """构建请求参数"""
        params = {}
        
        if lottery_type == 'shuangseqiu':
            params = {
                'name': 'ssq',
                'issueCount': 30,
                'issueStart': '',
                'issueEnd': '',
                'dayStart': start_date or '',
                'dayEnd': end_date or '',
                'pageNo': page
            }
        elif lottery_type == 'daletou':
            params = {
                'gameNo': '85',
                'provinceId': '0',
                'pageSize': '30',
                'pageNo': page,
                'isVerify': '1'
            }
        elif lottery_type == 'fucai3d':
            params = {
                'name': 'fc3d',
                'issueCount': 30,
                'pageNo': page
            }
        
        return params
    
    def _parse_shuangseqiu_data(self, response: requests.Response, 
                               lottery_type: str) -> List[Dict]:
        """解析双色球数据"""
        try:
            # 尝试JSON解析
            if 'application/json' in response.headers.get('content-type', ''):
                data = response.json()
                return self._parse_shuangseqiu_json(data)
            else:
                # HTML解析
                soup = BeautifulSoup(response.text, 'html.parser')
                return self._parse_shuangseqiu_html(soup)
                
        except Exception as e:
            self.logger.error(f"解析双色球数据失败: {e}")
            return []
    
    def _parse_shuangseqiu_json(self, data: Dict) -> List[Dict]:
        """解析双色球JSON数据"""
        results = []
        
        try:
            if 'result' in data and isinstance(data['result'], list):
                for item in data['result']:
                    # 解析开奖号码
                    red_balls = []
                    blue_ball = None
                    
                    if 'lotteryDrawResult' in item:
                        numbers = item['lotteryDrawResult'].split(',')
                        if len(numbers) >= 7:
                            red_balls = [int(x) for x in numbers[:6]]
                            blue_ball = int(numbers[6])
                    
                    if red_balls and blue_ball:
                        results.append({
                            'draw_date': item.get('lotteryDrawTime', ''),
                            'period': item.get('lotteryDrawNum', ''),
                            'red_balls': red_balls,
                            'blue_ball': blue_ball,
                            'lottery_type': 'shuangseqiu'
                        })
            
        except Exception as e:
            self.logger.error(f"解析双色球JSON失败: {e}")
        
        return results
    
    def _parse_shuangseqiu_html(self, soup: BeautifulSoup) -> List[Dict]:
        """解析双色球HTML数据"""
        results = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'class': 'kj-table'}) or soup.find('table')
            
            if table:
                rows = table.find_all('tr')[1:]  # 跳过表头
                
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        # 提取期号和日期
                        period = cells[0].get_text(strip=True)
                        date = cells[1].get_text(strip=True)
                        
                        # 提取开奖号码
                        numbers_cell = cells[2]
                        number_spans = numbers_cell.find_all('span') or numbers_cell.find_all('em')
                        
                        if len(number_spans) >= 7:
                            red_balls = [int(span.get_text(strip=True)) for span in number_spans[:6]]
                            blue_ball = int(number_spans[6].get_text(strip=True))
                            
                            results.append({
                                'draw_date': date,
                                'period': period,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'lottery_type': 'shuangseqiu'
                            })
            
        except Exception as e:
            self.logger.error(f"解析双色球HTML失败: {e}")
        
        return results
    
    def _parse_daletou_data(self, response: requests.Response, 
                           lottery_type: str) -> List[Dict]:
        """解析大乐透数据"""
        try:
            data = response.json()
            results = []
            
            if 'value' in data and 'list' in data['value']:
                for item in data['value']['list']:
                    # 解析号码
                    front_balls = []
                    back_balls = []
                    
                    if 'lotteryDrawResult' in item:
                        numbers = item['lotteryDrawResult'].split(' ')
                        if len(numbers) >= 2:
                            front_part = numbers[0].split(',')
                            back_part = numbers[1].split(',')
                            
                            front_balls = [int(x) for x in front_part if x.isdigit()]
                            back_balls = [int(x) for x in back_part if x.isdigit()]
                    
                    if len(front_balls) == 5 and len(back_balls) == 2:
                        results.append({
                            'draw_date': item.get('lotteryDrawTime', ''),
                            'period': item.get('lotteryDrawNum', ''),
                            'front_balls': front_balls,
                            'back_balls': back_balls,
                            'lottery_type': 'daletou'
                        })
            
            return results
            
        except Exception as e:
            self.logger.error(f"解析大乐透数据失败: {e}")
            return []
    
    def _parse_fucai3d_data(self, response: requests.Response, 
                           lottery_type: str) -> List[Dict]:
        """解析福彩3D数据"""
        try:
            # 类似双色球的解析逻辑
            if 'application/json' in response.headers.get('content-type', ''):
                data = response.json()
                results = []
                
                if 'result' in data and isinstance(data['result'], list):
                    for item in data['result']:
                        if 'lotteryDrawResult' in item:
                            numbers_str = item['lotteryDrawResult']
                            numbers = [int(x) for x in numbers_str if x.isdigit()]
                            
                            if len(numbers) == 3:
                                results.append({
                                    'draw_date': item.get('lotteryDrawTime', ''),
                                    'period': item.get('lotteryDrawNum', ''),
                                    'numbers': numbers,
                                    'lottery_type': 'fucai3d'
                                })
                
                return results
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"解析福彩3D数据失败: {e}")
            return []
    
    def get_latest_data(self, lottery_type: str) -> Dict[str, Any]:
        """获取最新一期数据"""
        try:
            result = self.crawl_lottery_data(lottery_type, max_pages=1)
            
            if result['status'] == 'success' and result['data']:
                latest = result['data'][0]  # 假设第一条是最新的
                return {
                    'status': 'success',
                    'lottery_type': lottery_type,
                    'latest_data': latest,
                    'crawl_time': datetime.now().isoformat()
                }
            else:
                return {
                    'status': 'failed',
                    'error': '无法获取最新数据',
                    'lottery_type': lottery_type
                }
                
        except Exception as e:
            self.logger.error(f"获取最新{lottery_type}数据失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'lottery_type': lottery_type
            }
    
    def validate_crawled_data(self, data: List[Dict], lottery_type: str) -> Dict[str, Any]:
        """验证爬取的数据"""
        try:
            valid_count = 0
            invalid_items = []
            
            for i, item in enumerate(data):
                is_valid = True
                errors = []
                
                # 基础字段检查
                required_fields = ['draw_date', 'period']
                for field in required_fields:
                    if field not in item or not item[field]:
                        errors.append(f"缺少字段: {field}")
                        is_valid = False
                
                # 彩票特定验证
                if lottery_type == 'shuangseqiu':
                    if 'red_balls' not in item or len(item['red_balls']) != 6:
                        errors.append("红球数量不正确")
                        is_valid = False
                    
                    if 'blue_ball' not in item:
                        errors.append("缺少蓝球")
                        is_valid = False
                
                elif lottery_type == 'daletou':
                    if 'front_balls' not in item or len(item['front_balls']) != 5:
                        errors.append("前区数量不正确")
                        is_valid = False
                    
                    if 'back_balls' not in item or len(item['back_balls']) != 2:
                        errors.append("后区数量不正确")
                        is_valid = False
                
                elif lottery_type == 'fucai3d':
                    if 'numbers' not in item or len(item['numbers']) != 3:
                        errors.append("号码数量不正确")
                        is_valid = False
                
                if is_valid:
                    valid_count += 1
                else:
                    invalid_items.append({
                        'index': i,
                        'item': item,
                        'errors': errors
                    })
            
            return {
                'total_count': len(data),
                'valid_count': valid_count,
                'invalid_count': len(invalid_items),
                'validity_rate': valid_count / len(data) if data else 0,
                'invalid_items': invalid_items[:10]  # 只返回前10个无效项
            }
            
        except Exception as e:
            self.logger.error(f"验证爬取数据失败: {e}")
            return {
                'error': str(e)
            }
    
    def get_crawler_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        return {
            'request_count_today': self.anti_crawler['request_count'],
            'daily_limit': self.crawler_config['daily_limit'],
            'remaining_requests': self.crawler_config['daily_limit'] - self.anti_crawler['request_count'],
            'last_request_time': datetime.fromtimestamp(self.anti_crawler['last_request_time']).isoformat() if self.anti_crawler['last_request_time'] else None,
            'supported_lotteries': list(self.data_sources.keys()),
            'anti_crawler_enabled': True
        }
