#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和清洗模块
确保数据质量和完整性

Author: HuiCai Team
Date: 2025-01-15
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import logging
import re
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    cleaned_data: Optional[pd.DataFrame] = None
    statistics: Optional[Dict[str, Any]] = None


class DataValidator:
    """数据验证器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self._initialize_rules()
    
    def _initialize_rules(self):
        """初始化验证规则"""
        self.lottery_rules = {
            'shuangseqiu': {
                'red_ball_count': 6,
                'red_ball_range': (1, 33),
                'blue_ball_count': 1,
                'blue_ball_range': (1, 16),
                'required_columns': ['draw_date', 'period', 'red_balls', 'blue_ball']
            },
            'daletou': {
                'front_ball_count': 5,
                'front_ball_range': (1, 35),
                'back_ball_count': 2,
                'back_ball_range': (1, 12),
                'required_columns': ['draw_date', 'period', 'front_balls', 'back_balls']
            },
            'fucai3d': {
                'number_count': 3,
                'number_range': (0, 9),
                'required_columns': ['draw_date', 'period', 'numbers']
            }
        }
    
    def validate_lottery_data(self, data: pd.DataFrame, lottery_type: str) -> ValidationResult:
        """
        验证彩票数据
        
        Args:
            data: 待验证的数据
            lottery_type: 彩票类型
            
        Returns:
            验证结果
        """
        try:
            errors = []
            warnings = []
            
            if lottery_type not in self.lottery_rules:
                errors.append(f"不支持的彩票类型: {lottery_type}")
                return ValidationResult(False, errors, warnings)
            
            rules = self.lottery_rules[lottery_type]
            
            # 基础验证
            basic_validation = self._validate_basic_structure(data, rules)
            errors.extend(basic_validation['errors'])
            warnings.extend(basic_validation['warnings'])
            
            if errors:
                return ValidationResult(False, errors, warnings)
            
            # 数据内容验证
            if lottery_type == 'shuangseqiu':
                content_validation = self._validate_shuangseqiu_content(data)
            elif lottery_type == 'daletou':
                content_validation = self._validate_daletou_content(data)
            elif lottery_type == 'fucai3d':
                content_validation = self._validate_fucai3d_content(data)
            
            errors.extend(content_validation['errors'])
            warnings.extend(content_validation['warnings'])
            
            # 数据清洗
            cleaned_data = self._clean_data(data, lottery_type) if not errors else None
            
            # 生成统计信息
            statistics = self._generate_statistics(cleaned_data, lottery_type) if cleaned_data is not None else None
            
            is_valid = len(errors) == 0
            
            self.logger.info(f"数据验证完成: {lottery_type}, 有效: {is_valid}, 错误: {len(errors)}, 警告: {len(warnings)}")
            
            return ValidationResult(is_valid, errors, warnings, cleaned_data, statistics)
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return ValidationResult(False, [f"验证过程异常: {e}"], [])
    
    def _validate_basic_structure(self, data: pd.DataFrame, rules: Dict) -> Dict[str, List[str]]:
        """基础结构验证"""
        errors = []
        warnings = []
        
        # 检查数据是否为空
        if data.empty:
            errors.append("数据为空")
            return {'errors': errors, 'warnings': warnings}
        
        # 检查必需列
        missing_columns = set(rules['required_columns']) - set(data.columns)
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查数据类型
        if 'draw_date' in data.columns:
            try:
                pd.to_datetime(data['draw_date'])
            except:
                errors.append("draw_date列格式不正确")
        
        # 检查重复数据
        if 'period' in data.columns:
            duplicates = data['period'].duplicated().sum()
            if duplicates > 0:
                warnings.append(f"发现{duplicates}条重复期号数据")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_shuangseqiu_content(self, data: pd.DataFrame) -> Dict[str, List[str]]:
        """双色球内容验证"""
        errors = []
        warnings = []
        
        for idx, row in data.iterrows():
            # 验证红球
            if 'red_balls' in row:
                red_balls = row['red_balls']
                if isinstance(red_balls, (list, tuple)):
                    if len(red_balls) != 6:
                        errors.append(f"第{idx+1}行红球数量不正确: {len(red_balls)}")
                    
                    for ball in red_balls:
                        if not isinstance(ball, int) or ball < 1 or ball > 33:
                            errors.append(f"第{idx+1}行红球号码无效: {ball}")
                    
                    if len(set(red_balls)) != len(red_balls):
                        errors.append(f"第{idx+1}行红球有重复")
                else:
                    errors.append(f"第{idx+1}行红球格式错误")
            
            # 验证蓝球
            if 'blue_ball' in row:
                blue_ball = row['blue_ball']
                if not isinstance(blue_ball, int) or blue_ball < 1 or blue_ball > 16:
                    errors.append(f"第{idx+1}行蓝球号码无效: {blue_ball}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_daletou_content(self, data: pd.DataFrame) -> Dict[str, List[str]]:
        """大乐透内容验证"""
        errors = []
        warnings = []
        
        for idx, row in data.iterrows():
            # 验证前区
            if 'front_balls' in row:
                front_balls = row['front_balls']
                if isinstance(front_balls, (list, tuple)):
                    if len(front_balls) != 5:
                        errors.append(f"第{idx+1}行前区数量不正确: {len(front_balls)}")
                    
                    for ball in front_balls:
                        if not isinstance(ball, int) or ball < 1 or ball > 35:
                            errors.append(f"第{idx+1}行前区号码无效: {ball}")
                    
                    if len(set(front_balls)) != len(front_balls):
                        errors.append(f"第{idx+1}行前区有重复")
            
            # 验证后区
            if 'back_balls' in row:
                back_balls = row['back_balls']
                if isinstance(back_balls, (list, tuple)):
                    if len(back_balls) != 2:
                        errors.append(f"第{idx+1}行后区数量不正确: {len(back_balls)}")
                    
                    for ball in back_balls:
                        if not isinstance(ball, int) or ball < 1 or ball > 12:
                            errors.append(f"第{idx+1}行后区号码无效: {ball}")
                    
                    if len(set(back_balls)) != len(back_balls):
                        errors.append(f"第{idx+1}行后区有重复")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_fucai3d_content(self, data: pd.DataFrame) -> Dict[str, List[str]]:
        """福彩3D内容验证"""
        errors = []
        warnings = []
        
        for idx, row in data.iterrows():
            if 'numbers' in row:
                numbers = row['numbers']
                if isinstance(numbers, (list, tuple)):
                    if len(numbers) != 3:
                        errors.append(f"第{idx+1}行号码数量不正确: {len(numbers)}")
                    
                    for num in numbers:
                        if not isinstance(num, int) or num < 0 or num > 9:
                            errors.append(f"第{idx+1}行号码无效: {num}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _clean_data(self, data: pd.DataFrame, lottery_type: str) -> pd.DataFrame:
        """数据清洗"""
        cleaned_data = data.copy()
        
        # 日期格式化
        if 'draw_date' in cleaned_data.columns:
            cleaned_data['draw_date'] = pd.to_datetime(cleaned_data['draw_date'])
        
        # 排序
        if 'draw_date' in cleaned_data.columns:
            cleaned_data = cleaned_data.sort_values('draw_date')
        elif 'period' in cleaned_data.columns:
            cleaned_data = cleaned_data.sort_values('period')
        
        # 去重
        if 'period' in cleaned_data.columns:
            cleaned_data = cleaned_data.drop_duplicates(subset=['period'], keep='last')
        
        # 重置索引
        cleaned_data = cleaned_data.reset_index(drop=True)
        
        self.logger.info(f"数据清洗完成: {len(cleaned_data)}条记录")
        
        return cleaned_data
    
    def _generate_statistics(self, data: pd.DataFrame, lottery_type: str) -> Dict[str, Any]:
        """生成数据统计信息"""
        stats = {
            'total_records': len(data),
            'date_range': {},
            'data_quality': {}
        }
        
        # 日期范围
        if 'draw_date' in data.columns:
            stats['date_range'] = {
                'start_date': data['draw_date'].min().isoformat(),
                'end_date': data['draw_date'].max().isoformat(),
                'span_days': (data['draw_date'].max() - data['draw_date'].min()).days
            }
        
        # 数据质量
        stats['data_quality'] = {
            'completeness': (1 - data.isnull().sum().sum() / (len(data) * len(data.columns))) * 100,
            'missing_values': data.isnull().sum().to_dict()
        }
        
        # 彩票特定统计
        if lottery_type == 'shuangseqiu':
            stats['lottery_specific'] = self._shuangseqiu_statistics(data)
        elif lottery_type == 'daletou':
            stats['lottery_specific'] = self._daletou_statistics(data)
        elif lottery_type == 'fucai3d':
            stats['lottery_specific'] = self._fucai3d_statistics(data)
        
        return stats
    
    def _shuangseqiu_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """双色球统计"""
        stats = {}
        
        if 'red_balls' in data.columns:
            all_red_balls = []
            for red_balls in data['red_balls']:
                if isinstance(red_balls, (list, tuple)):
                    all_red_balls.extend(red_balls)
            
            stats['red_ball_frequency'] = pd.Series(all_red_balls).value_counts().to_dict()
            stats['red_ball_stats'] = {
                'most_common': pd.Series(all_red_balls).mode().tolist(),
                'least_common': pd.Series(all_red_balls).value_counts().tail(5).index.tolist()
            }
        
        if 'blue_ball' in data.columns:
            stats['blue_ball_frequency'] = data['blue_ball'].value_counts().to_dict()
            stats['blue_ball_stats'] = {
                'most_common': data['blue_ball'].mode().tolist(),
                'least_common': data['blue_ball'].value_counts().tail(5).index.tolist()
            }
        
        return stats
    
    def _daletou_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """大乐透统计"""
        stats = {}
        
        if 'front_balls' in data.columns:
            all_front_balls = []
            for front_balls in data['front_balls']:
                if isinstance(front_balls, (list, tuple)):
                    all_front_balls.extend(front_balls)
            
            stats['front_ball_frequency'] = pd.Series(all_front_balls).value_counts().to_dict()
        
        if 'back_balls' in data.columns:
            all_back_balls = []
            for back_balls in data['back_balls']:
                if isinstance(back_balls, (list, tuple)):
                    all_back_balls.extend(back_balls)
            
            stats['back_ball_frequency'] = pd.Series(all_back_balls).value_counts().to_dict()
        
        return stats
    
    def _fucai3d_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """福彩3D统计"""
        stats = {}
        
        if 'numbers' in data.columns:
            position_stats = [[], [], []]
            
            for numbers in data['numbers']:
                if isinstance(numbers, (list, tuple)) and len(numbers) == 3:
                    for i, num in enumerate(numbers):
                        position_stats[i].append(num)
            
            stats['position_frequency'] = {}
            for i in range(3):
                stats['position_frequency'][f'position_{i+1}'] = pd.Series(position_stats[i]).value_counts().to_dict()
        
        return stats
    
    def validate_prediction_input(self, input_data: Dict[str, Any]) -> ValidationResult:
        """验证预测输入数据"""
        errors = []
        warnings = []
        
        # 检查必需字段
        required_fields = ['lottery_type', 'method']
        for field in required_fields:
            if field not in input_data:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证彩票类型
        if 'lottery_type' in input_data:
            if input_data['lottery_type'] not in self.lottery_rules:
                errors.append(f"不支持的彩票类型: {input_data['lottery_type']}")
        
        # 验证方法
        valid_methods = ['traditional_ml', 'statistical', 'chinese', 'deep_learning', 'ensemble']
        if 'method' in input_data:
            if input_data['method'] not in valid_methods:
                warnings.append(f"未知的预测方法: {input_data['method']}")
        
        is_valid = len(errors) == 0
        
        return ValidationResult(is_valid, errors, warnings)
