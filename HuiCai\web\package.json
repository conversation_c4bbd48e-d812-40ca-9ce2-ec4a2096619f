{"name": "huicai-web", "version": "4.0.0", "description": "HuiCai 慧彩智能体系统 - Web界面", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.56", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "antd": "^5.1.0", "axios": "^1.2.0", "chart.js": "^4.1.0", "chartjs-adapter-date-fns": "^2.0.0", "date-fns": "^2.29.0", "react": "^18.2.0", "react-chartjs-2": "^5.1.0", "react-dom": "^18.2.0", "react-router-dom": "^6.6.0", "react-scripts": "5.0.1", "recharts": "^2.5.0", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.0.5", "socket.io-client": "^4.6.0", "moment": "^2.29.4", "lodash": "^4.17.21", "@types/lodash": "^4.14.191"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.30.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0"}, "proxy": "http://localhost:8000"}