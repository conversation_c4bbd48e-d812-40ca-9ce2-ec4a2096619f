@echo off
REM HuiCai 慧彩系统本地安装脚本 (Windows版本)
setlocal enabledelayedexpansion

echo ================================================== 
echo   HuiCai 慧彩智能体系统 - 本地安装程序 (Windows)
echo ==================================================
echo.

REM 检查是否在项目根目录
if not exist "main.py" (
    echo [ERROR] 请在HuiCai项目根目录下运行此脚本
    pause
    exit /b 1
)

echo [INFO] 检查系统要求...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [SUCCESS] Python版本检查通过: !PYTHON_VERSION!
)

REM 检查pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip未安装
    pause
    exit /b 1
) else (
    echo [SUCCESS] pip检查通过
)

REM 检查Node.js (可选)
node --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Node.js未安装，将跳过Web界面安装
    echo 如需Web界面，请安装Node.js: https://nodejs.org/
    set SKIP_WEB=true
) else (
    for /f "tokens=1" %%i in ('node --version 2^>^&1') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js版本检查通过: !NODE_VERSION!
    set SKIP_WEB=false
)

echo.
echo [INFO] 创建Python虚拟环境...

REM 创建虚拟环境
if not exist "venv" (
    python -m venv venv
    echo [SUCCESS] 虚拟环境创建完成
) else (
    echo [INFO] 虚拟环境已存在
)

REM 激活虚拟环境
call venv\Scripts\activate.bat
echo [SUCCESS] 虚拟环境已激活

REM 升级pip
python -m pip install --upgrade pip
echo [SUCCESS] pip已升级到最新版本

echo.
echo [INFO] 安装Python依赖包...

REM 检查requirements.txt
if not exist "requirements.txt" (
    echo [ERROR] requirements.txt文件不存在
    pause
    exit /b 1
)

REM 安装依赖
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Python依赖安装失败
    pause
    exit /b 1
) else (
    echo [SUCCESS] Python依赖安装完成
)

echo.
echo [INFO] 初始化数据库...

REM 创建数据目录
if not exist "data" mkdir data
if not exist "data\models" mkdir data\models
if not exist "data\logs" mkdir data\logs
if not exist "data\cache" mkdir data\cache

REM 运行数据库初始化脚本
if exist "scripts\init_database.py" (
    python scripts\init_database.py
    echo [SUCCESS] 数据库初始化完成
) else (
    echo [WARNING] 数据库初始化脚本不存在，将在首次运行时自动初始化
)

echo.
REM 安装Web界面依赖
if "!SKIP_WEB!"=="false" (
    echo [INFO] 安装Web界面依赖...
    if exist "web" (
        cd web
        npm install
        if errorlevel 1 (
            echo [WARNING] Web依赖安装失败
        ) else (
            echo [SUCCESS] Web依赖安装完成
        )
        cd ..
    ) else (
        echo [WARNING] web目录不存在，将在第四阶段创建
    )
) else (
    echo [WARNING] 跳过Web界面安装
)

echo.
echo [INFO] 创建启动脚本...

REM 创建启动脚本
(
echo @echo off
echo REM HuiCai 慧彩系统启动脚本
echo.
echo echo   _   _       _  _____      _ 
echo echo  ^| ^| ^| ^|     ^(^_^)/  __ \    ^(^_^)
echo echo  ^| ^|_^| ^|_   _ _^| /  \/^^ __ _ _ 
echo echo  ^|  _  ^| ^| ^| ^| ^| ^|    / _\` ^| ^|
echo echo  ^| ^| ^| ^| ^|_^| ^| ^| \__/\ ^(^_^| ^| ^|
echo echo  \_^| ^|_/\__,_^|_^\____/\__,_^|_^|
echo echo.
echo echo     慧彩智能体系统 v4.0
echo echo     本地部署版本
echo echo.
echo.
echo REM 检查虚拟环境
echo if not exist "venv" ^(
echo     echo [ERROR] 虚拟环境不存在，请先运行 install_local.bat
echo     pause
echo     exit /b 1
echo ^)
echo.
echo REM 激活虚拟环境
echo call venv\Scripts\activate.bat
echo.
echo echo [INFO] 启动后端服务...
echo start /b python main.py
echo.
echo timeout /t 3 /nobreak ^>nul
echo.
echo REM 启动Web界面^(如果存在^)
echo if exist "web" if "!SKIP_WEB!"=="false" ^(
echo     echo [INFO] 启动Web界面...
echo     cd web
echo     start /b npm start
echo     cd ..
echo ^)
echo.
echo echo.
echo echo [SUCCESS] HuiCai系统启动完成！
echo echo.
echo echo CLI界面: 当前终端
echo echo Web界面: http://localhost:3000 ^(如果已安装^)
echo echo API文档: http://localhost:8000/docs ^(如果已启用^)
echo echo.
echo echo 按任意键停止系统...
echo pause ^>nul
echo.
echo REM 停止进程
echo taskkill /f /im python.exe 2^>nul
echo taskkill /f /im node.exe 2^>nul
echo echo [INFO] 系统已停止
) > start_huicai.bat

echo [SUCCESS] 启动脚本创建完成: start_huicai.bat

REM 创建停止脚本
(
echo @echo off
echo REM HuiCai 慧彩系统停止脚本
echo.
echo echo [INFO] 正在停止HuiCai系统...
echo.
echo REM 停止Python进程
echo taskkill /f /im python.exe 2^>nul
echo.
echo REM 停止Node.js进程
echo taskkill /f /im node.exe 2^>nul
echo.
echo echo [SUCCESS] HuiCai系统已停止
echo pause
) > stop_huicai.bat

echo [SUCCESS] 停止脚本创建完成: stop_huicai.bat

echo.
echo [INFO] 创建配置文件...

REM 创建配置目录
if not exist "config" mkdir config

REM 创建本地配置文件
if not exist "config\local_config.yaml" (
(
echo # HuiCai 本地部署配置文件
echo.
echo # 数据库配置 ^(使用SQLite^)
echo database:
echo   type: sqlite
echo   path: data/huicai.db
echo   backup_enabled: true
echo   auto_vacuum: true
echo.
echo # 缓存配置 ^(使用内存缓存^)
echo cache:
echo   type: memory
echo   max_size: 512MB
echo   ttl: 3600
echo.
echo # Web服务配置
echo web:
echo   host: 127.0.0.1
echo   port: 8000
echo   debug: false
echo   auto_reload: false
echo.
echo # 深度学习配置
echo deep_learning:
echo   enabled: true
echo   device: cpu  # 本地部署默认使用CPU
echo   model_cache: true
echo   batch_size: 16  # 降低批次大小以适应本地环境
echo.
echo # 日志配置
echo logging:
echo   level: INFO
echo   file_enabled: true
echo   console_enabled: true
echo   max_file_size: 10MB
echo   backup_count: 5
echo.
echo # 本地优化配置
echo local_optimization:
echo   memory_limit: 4GB
echo   cpu_cores: auto
echo   cache_enabled: true
echo   compression_enabled: true
) > config\local_config.yaml
    echo [SUCCESS] 本地配置文件创建完成
) else (
    echo [INFO] 配置文件已存在
)

echo.
echo [INFO] 运行系统测试...

REM 测试Python模块导入
python -c "import sys; sys.path.append('src'); from management_layer.config_manager import ConfigManager; from management_layer.log_manager import LogManager; print('[SUCCESS] 核心模块导入成功')" 2>nul
if errorlevel 1 (
    echo [ERROR] 系统测试失败
    pause
    exit /b 1
) else (
    echo [SUCCESS] 系统测试通过
)

echo.
echo ================================================== 
echo [SUCCESS] HuiCai系统本地安装完成！
echo ==================================================
echo.
echo 安装摘要:
echo   ✅ Python环境: !PYTHON_VERSION!
echo   ✅ 虚拟环境: venv\
echo   ✅ 依赖包: 已安装
echo   ✅ 数据库: SQLite
echo   ✅ 配置文件: config\local_config.yaml
echo.
echo 启动系统:
echo   start_huicai.bat
echo.
echo 停止系统:
echo   stop_huicai.bat
echo.
echo 更多信息请查看: 第四阶段本地部署方案.md
echo.
pause
