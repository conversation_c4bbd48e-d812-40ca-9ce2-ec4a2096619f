#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai 纯Python Web界面启动脚本
无需Node.js，基于FastAPI + Jinja2模板

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    print("🔍 检查Python Web依赖...")
    
    required_packages = [
        ('fastapi', 'FastAPI Web框架'),
        ('uvicorn', 'ASGI服务器'),
        ('jinja2', 'Jinja2模板引擎'),
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package:<12} - {description}")
        except ImportError:
            print(f"   ❌ {package:<12} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install fastapi uvicorn jinja2")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def main():
    """主函数"""
    print("="*60)
    print("🌐 HuiCai 纯Python Web界面")
    print("="*60)
    print("✅ 无需Node.js，纯Python实现")
    print("✅ 基于FastAPI + Jinja2模板")
    print("✅ Bootstrap响应式设计")
    print("✅ 完整的AI彩票分析功能")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺失的包")
        input("按回车键退出...")
        return
    
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    try:
        # 导入并启动Web服务器
        from src.web_interface.web_server import main as start_web_server
        start_web_server()
        
    except ImportError as e:
        print(f"❌ 导入Web服务器失败: {e}")
        print("请确保 src/web_interface/web_server.py 文件存在")
        input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，Web服务器已停止")
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
