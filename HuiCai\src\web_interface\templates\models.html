{% extends "base.html" %}

{% block title %}模型管理 - HuiCai 慧彩智能体系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-robot text-primary me-2"></i>
            模型管理
        </h2>
    </div>
</div>

<div class="row">
    <!-- 模型列表 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list text-info me-2"></i>
                    AI模型列表
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>模型名称</th>
                                <th>彩票类型</th>
                                <th>准确率</th>
                                <th>状态</th>
                                <th>最后训练</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for model in models %}
                            <tr>
                                <td>
                                    <strong>{{ model.name }}</strong>
                                    <br><small class="text-muted">深度学习模型</small>
                                </td>
                                <td>{{ model.lottery }}</td>
                                <td>
                                    <span class="badge bg-success">{{ model.accuracy }}</span>
                                    <div class="progress mt-1" style="height: 4px;">
                                        <div class="progress-bar" style="width: {{ model.accuracy.rstrip('%') }}%"></div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ model.status }}</span>
                                </td>
                                <td>{{ model.last_trained }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="trainModel('{{ model.name }}')">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="viewModel('{{ model.name }}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="exportModel('{{ model.name }}')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模型操作面板 -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs text-warning me-2"></i>
                    模型操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="createModel()">
                        <i class="fas fa-plus me-2"></i>创建新模型
                    </button>
                    <button class="btn btn-outline-success" onclick="batchTrain()">
                        <i class="fas fa-play-circle me-2"></i>批量训练
                    </button>
                    <button class="btn btn-outline-info" onclick="compareModels()">
                        <i class="fas fa-chart-line me-2"></i>模型对比
                    </button>
                    <button class="btn btn-outline-warning" onclick="optimizeModels()">
                        <i class="fas fa-tools me-2"></i>模型优化
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 训练状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar text-success me-2"></i>
                    训练状态
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>LSTM模型</span>
                        <span>85%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: 85%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>随机森林</span>
                        <span>完成</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="mb-0">
                    <div class="d-flex justify-content-between">
                        <span>五行算法</span>
                        <span>待开始</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-secondary" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 性能统计 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-trophy text-warning me-2"></i>
                    性能排行
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>LSTM神经网络</strong>
                            <br><small class="text-muted">双色球</small>
                        </div>
                        <span class="badge bg-success">78.5%</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>随机森林</strong>
                            <br><small class="text-muted">大乐透</small>
                        </div>
                        <span class="badge bg-info">72.3%</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>五行算法</strong>
                            <br><small class="text-muted">福彩3D</small>
                        </div>
                        <span class="badge bg-warning">65.8%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 训练模型
    async function trainModel(modelName) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            showAlert(`开始训练模型: ${modelName}`, 'info');
            
            // 模拟训练过程
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            showAlert(`模型 ${modelName} 训练完成！`, 'success');
        } catch (error) {
            showAlert('模型训练失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 查看模型详情
    function viewModel(modelName) {
        showAlert(`查看模型详情: ${modelName}`, 'info');
        // 这里可以打开模型详情页面或模态框
    }
    
    // 导出模型
    function exportModel(modelName) {
        showAlert(`导出模型: ${modelName}`, 'info');
        
        // 模拟导出
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = 'data:application/octet-stream;base64,';
            link.download = `${modelName}_model.pkl`;
            link.click();
            showAlert('模型导出成功！', 'success');
        }, 1000);
    }
    
    // 创建新模型
    function createModel() {
        const modelName = prompt('请输入新模型名称:');
        if (modelName) {
            showAlert(`创建新模型: ${modelName}`, 'info');
            // 这里可以打开创建模型的表单
        }
    }
    
    // 批量训练
    async function batchTrain() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            showAlert('开始批量训练所有模型...', 'info');
            
            // 模拟批量训练
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            showAlert('批量训练完成！', 'success');
        } catch (error) {
            showAlert('批量训练失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 模型对比
    function compareModels() {
        showAlert('打开模型对比界面...', 'info');
        // 这里可以打开模型对比页面
    }
    
    // 模型优化
    async function optimizeModels() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            showAlert('开始优化模型...', 'info');
            
            // 模拟优化过程
            await new Promise(resolve => setTimeout(resolve, 4000));
            
            showAlert('模型优化完成！平均准确率提升3.2%', 'success');
        } catch (error) {
            showAlert('模型优化失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 模拟训练进度更新
    function updateTrainingProgress() {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach((bar, index) => {
            if (index === 0) { // LSTM模型
                const currentWidth = parseInt(bar.style.width) || 0;
                if (currentWidth < 100) {
                    bar.style.width = (currentWidth + 1) + '%';
                    bar.parentElement.previousElementSibling.querySelector('span:last-child').textContent = (currentWidth + 1) + '%';
                }
            }
        });
    }
    
    // 每2秒更新一次训练进度
    setInterval(updateTrainingProgress, 2000);
</script>
{% endblock %}
