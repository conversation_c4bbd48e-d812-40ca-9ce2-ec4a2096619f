#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行界面
提供交互式命令行操作界面

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, date, timedelta
import json


class CLIInterface:
    """命令行界面"""
    
    def __init__(self, huicai_system):
        """
        初始化CLI界面
        
        Args:
            huicai_system: HuiCai系统实例
        """
        self.system = huicai_system
        self.logger = None
        self.running = False
        
        # 命令映射
        self.commands = {
            'help': self._show_help,
            'status': self._show_status,
            'crawl': self._crawl_command,
            'predict': self._predict_command,
            'model': self._model_command,
            'jobs': self._jobs_command,
            'config': self._config_command,
            'exit': self._exit_command,
            'quit': self._exit_command
        }
    
    async def run(self):
        """运行CLI界面"""
        self.logger = self.system.logger
        self.running = True
        
        # 显示欢迎信息
        self._show_welcome()
        
        while self.running:
            try:
                # 显示提示符
                command_input = input("\nHuiCai> ").strip()
                
                if not command_input:
                    continue
                
                # 解析命令
                parts = command_input.split()
                command = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []
                
                # 执行命令
                if command in self.commands:
                    await self.commands[command](args)
                else:
                    print(f"未知命令: {command}. 输入 'help' 查看帮助.")
                    
            except KeyboardInterrupt:
                print("\n\n收到中断信号，正在退出...")
                break
            except EOFError:
                print("\n\n再见!")
                break
            except Exception as e:
                print(f"命令执行错误: {e}")
                if self.logger:
                    self.logger.error(f"CLI命令执行错误: {e}")
    
    def _show_welcome(self):
        """显示欢迎信息"""
        welcome_text = """
╔══════════════════════════════════════════════════════════════╗
║                    HuiCai 慧彩智能体系统                      ║
║                  Lottery Analysis AI Agent                   ║
╠══════════════════════════════════════════════════════════════╣
║  欢迎使用HuiCai慧彩系统！                                     ║
║  这是一个专为中国彩票市场设计的智能分析系统                    ║
║  具备自主学习和进化能力                                       ║
║                                                              ║
║  输入 'help' 查看可用命令                                     ║
║  输入 'status' 查看系统状态                                   ║
║  输入 'exit' 或 'quit' 退出系统                               ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(welcome_text)
    
    async def _show_help(self, args: List[str]):
        """显示帮助信息"""
        help_text = """
可用命令:

基础命令:
  help                    - 显示此帮助信息
  status                  - 显示系统状态
  exit/quit              - 退出系统

数据爬取:
  crawl latest <type>     - 爬取指定彩票的最新数据
  crawl all              - 爬取所有彩票的最新数据
  crawl history <type> <days> - 爬取指定天数的历史数据

预测功能:
  predict <type>         - 预测指定彩票的下一期号码
  predict all            - 预测所有彩票的下一期号码
  predict chinese <type> - 使用中国特色算法预测
  predict comprehensive <type> - 综合预测（ML+中国算法）

模型管理:
  model info <type>      - 显示指定彩票的模型信息
  model list             - 列出所有模型
  model train <type>     - 手动训练指定彩票的模型

任务管理:
  jobs list              - 显示所有定时任务
  jobs stats             - 显示任务统计信息

配置管理:
  config show            - 显示系统配置
  config lottery         - 显示彩票配置

支持的彩票类型:
  shuangseqiu           - 双色球
  daletou               - 大乐透 (待实现)
  fucai3d               - 福彩3D (待实现)

示例:
  crawl latest shuangseqiu
  predict shuangseqiu
  model info shuangseqiu
        """
        print(help_text)
    
    async def _show_status(self, args: List[str]):
        """显示系统状态"""
        try:
            print("\n" + "="*60)
            print("HuiCai 慧彩系统状态")
            print("="*60)
            
            # 系统基本信息
            config = self.system.config_manager.get('system', {})
            print(f"系统名称: {config.get('name', 'HuiCai')}")
            print(f"系统版本: {config.get('version', '1.0.0')}")
            print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 爬虫状态
            if self.system.crawler_manager:
                crawler_stats = self.system.crawler_manager.get_crawler_statistics()
                print(f"\n爬虫管理器:")
                print(f"  运行状态: {'运行中' if crawler_stats['is_running'] else '已停止'}")
                print(f"  任务队列: {crawler_stats['task_queue_size']}")
                print(f"  运行任务: {crawler_stats['running_tasks']}")
                
                print(f"  支持彩票:")
                for lottery_type, stats in crawler_stats['crawlers'].items():
                    print(f"    {lottery_type}: 请求{stats['request_count']}次, "
                          f"成功率{stats['success_rate']}%")
            
            # 学习器状态
            if self.system.incremental_learner:
                print(f"\n增量学习器:")
                print(f"  运行状态: {'运行中' if self.system.incremental_learner.is_running else '已停止'}")
                
                lottery_configs = self.system.config_manager.lottery_configs
                for lottery_type in lottery_configs.keys():
                    model_info = self.system.incremental_learner.get_model_info(lottery_type)
                    if model_info:
                        print(f"  {lottery_type}模型: 数据{model_info['data_count']}条, "
                              f"准确率{model_info['accuracy']:.4f}")
            
            # 调度器状态
            if self.system.scheduler:
                job_stats = self.system.scheduler.get_job_statistics()
                print(f"\n任务调度器:")
                print(f"  运行状态: {'运行中' if job_stats['running'] else '已停止'}")
                print(f"  总任务数: {job_stats['total_jobs']}")
                for job_type, count in job_stats['job_types'].items():
                    print(f"    {job_type}: {count}个")
            
            print("="*60)
            
        except Exception as e:
            print(f"获取系统状态失败: {e}")
    
    async def _crawl_command(self, args: List[str]):
        """爬取命令"""
        if not args:
            print("用法: crawl <latest|all|history> [参数]")
            return
        
        subcommand = args[0].lower()
        
        try:
            if subcommand == "latest":
                if len(args) < 2:
                    print("用法: crawl latest <彩票类型>")
                    return
                
                lottery_type = args[1]
                print(f"开始爬取 {lottery_type} 最新数据...")
                
                success = await self.system.crawler_manager.crawl_latest(lottery_type)
                if success:
                    print(f"✓ {lottery_type} 最新数据爬取任务已提交")
                else:
                    print(f"✗ {lottery_type} 爬取任务提交失败")
            
            elif subcommand == "all":
                print("开始爬取所有彩票最新数据...")
                
                count = await self.system.crawler_manager.crawl_all_latest()
                print(f"✓ 已提交 {count} 个爬取任务")
            
            elif subcommand == "history":
                if len(args) < 3:
                    print("用法: crawl history <彩票类型> <天数>")
                    return
                
                lottery_type = args[1]
                try:
                    days = int(args[2])
                except ValueError:
                    print("天数必须是数字")
                    return
                
                end_date = date.today()
                start_date = end_date - timedelta(days=days)
                
                print(f"开始爬取 {lottery_type} 最近{days}天历史数据...")
                
                success = await self.system.crawler_manager.crawl_historical(
                    lottery_type, start_date, end_date
                )
                if success:
                    print(f"✓ {lottery_type} 历史数据爬取任务已提交")
                else:
                    print(f"✗ {lottery_type} 历史数据爬取任务提交失败")
            
            else:
                print(f"未知子命令: {subcommand}")
                
        except Exception as e:
            print(f"爬取命令执行失败: {e}")
    
    async def _predict_command(self, args: List[str]):
        """预测命令"""
        try:
            if not args:
                print("用法: predict <all|chinese|comprehensive> [彩票类型]")
                return

            subcommand = args[0].lower()

            if subcommand == "all":
                # 预测所有彩票
                lottery_configs = self.system.config_manager.lottery_configs
                
                print("\n" + "="*60)
                print("HuiCai 慧彩预测结果")
                print("="*60)
                
                for lottery_type in lottery_configs.keys():
                    result = await self.system.incremental_learner.predict(lottery_type)
                    if result:
                        lottery_name = lottery_configs[lottery_type].get('name', lottery_type)
                        print(f"\n{lottery_name} ({lottery_type}):")
                        print(f"  预测日期: {result['prediction_date']}")
                        print(f"  增量预测: {result['incremental_prediction']}")
                        print(f"  主模型预测: {result['main_prediction']}")
                        print(f"  置信度: {result['confidence']:.4f}")
                        print(f"  模型准确率: {result['model_accuracy']:.4f}")
                    else:
                        print(f"\n{lottery_type}: 预测失败")
                
                print("="*60)
                print("注意: 预测结果仅供参考，请理性对待彩票")

            elif subcommand == "chinese":
                # 中国特色算法预测
                if len(args) < 2:
                    print("用法: predict chinese <彩票类型>")
                    return

                lottery_type = args[1]
                print(f"正在使用中国特色算法预测 {lottery_type}...")

                result = await self.system.incremental_learner.predict_with_chinese_algorithms(lottery_type)
                if result:
                    self._display_chinese_prediction_result(result)
                else:
                    print(f"✗ {lottery_type} 中国特色算法预测失败")

            elif subcommand == "comprehensive":
                # 综合预测
                if len(args) < 2:
                    print("用法: predict comprehensive <彩票类型>")
                    return

                lottery_type = args[1]
                print(f"正在进行 {lottery_type} 综合预测...")

                result = await self.system.incremental_learner.predict_with_chinese_algorithms(lottery_type)
                if result:
                    self._display_comprehensive_prediction_result(result)
                else:
                    print(f"✗ {lottery_type} 综合预测失败")

            else:
                # 传统预测指定彩票
                lottery_type = args[0]
                
                print(f"正在预测 {lottery_type}...")
                
                result = await self.system.incremental_learner.predict(lottery_type)
                if result:
                    lottery_config = self.system.config_manager.get_lottery_config(lottery_type)
                    lottery_name = lottery_config.get('name', lottery_type)
                    
                    print(f"\n{lottery_name} 预测结果:")
                    print(f"  预测日期: {result['prediction_date']}")
                    print(f"  增量预测: {result['incremental_prediction']}")
                    print(f"  主模型预测: {result['main_prediction']}")
                    print(f"  置信度: {result['confidence']:.4f}")
                    print(f"  模型准确率: {result['model_accuracy']:.4f}")
                    print(f"  训练数据量: {result['data_count']}")
                    print("\n注意: 预测结果仅供参考，请理性对待彩票")
                else:
                    print(f"✗ {lottery_type} 预测失败")
                    
        except Exception as e:
            print(f"预测命令执行失败: {e}")

    def _display_chinese_prediction_result(self, result: Dict[str, Any]):
        """显示中国特色算法预测结果"""
        try:
            lottery_type = result.get('lottery_type', '')
            lottery_config = self.system.config_manager.get_lottery_config(lottery_type)
            lottery_name = lottery_config.get('name', lottery_type)

            print(f"\n{lottery_name} 中国特色算法分析结果:")
            print("="*50)

            chinese_analysis = result.get('chinese_analysis', {})

            # 五行分析
            wuxing_analysis = chinese_analysis.get('wuxing_analysis', {})
            if wuxing_analysis:
                print(f"五行分析:")
                print(f"  主导五行: {wuxing_analysis.get('dominant_wuxing', '未知')}")
                print(f"  平衡度: {wuxing_analysis.get('balance_score', 0):.3f}")
                print(f"  推荐五行: {wuxing_analysis.get('next_favorable_wuxing', '未知')}")

            # 八卦分析
            bagua_analysis = chinese_analysis.get('bagua_analysis', {})
            if bagua_analysis:
                print(f"八卦分析:")
                print(f"  主卦: {bagua_analysis.get('main_bagua', '未知')}")
                print(f"  和谐度: {bagua_analysis.get('harmony_score', 0):.3f}")
                print(f"  推荐八卦: {bagua_analysis.get('next_favorable_bagua', '未知')}")

            # 吉凶分析
            fortune_analysis = chinese_analysis.get('fortune_analysis', {})
            if fortune_analysis:
                print(f"吉凶分析:")
                print(f"  运势等级: {fortune_analysis.get('fortune_level', '未知')}")
                print(f"  吉利比例: {fortune_analysis.get('lucky_ratio', 0):.3f}")
                print(f"  运势评分: {fortune_analysis.get('fortune_score', 0):.3f}")

            # 综合推荐
            comprehensive_rec = chinese_analysis.get('comprehensive_recommendation', {})
            if comprehensive_rec:
                print(f"综合推荐:")
                recommended = comprehensive_rec.get('recommended_numbers', [])
                if recommended:
                    print(f"  推荐号码: {recommended[:10]}")
                print(f"  置信度: {comprehensive_rec.get('confidence', 0):.3f}")
                print(f"  策略建议: {comprehensive_rec.get('strategy', '无')}")

            print("="*50)
            print("注意: 基于传统文化分析，仅供参考")

        except Exception as e:
            print(f"显示中国特色算法结果失败: {e}")

    def _display_comprehensive_prediction_result(self, result: Dict[str, Any]):
        """显示综合预测结果"""
        try:
            lottery_type = result.get('lottery_type', '')
            lottery_config = self.system.config_manager.get_lottery_config(lottery_type)
            lottery_name = lottery_config.get('name', lottery_type)

            print(f"\n{lottery_name} 综合预测结果:")
            print("="*60)

            # 机器学习预测
            ml_prediction = result.get('ml_prediction', {})
            if ml_prediction:
                print(f"机器学习预测:")
                print(f"  增量预测: {ml_prediction.get('incremental_prediction', '无')}")
                print(f"  主模型预测: {ml_prediction.get('main_prediction', '无')}")
                print(f"  置信度: {ml_prediction.get('confidence', 0):.4f}")
                print(f"  模型准确率: {ml_prediction.get('model_accuracy', 0):.4f}")

            # 中国特色算法（简化显示）
            chinese_analysis = result.get('chinese_analysis', {})
            if chinese_analysis:
                print(f"\n传统文化分析:")
                comprehensive_score = chinese_analysis.get('comprehensive_score', 0)
                print(f"  综合评分: {comprehensive_score:.3f}")

                wuxing = chinese_analysis.get('wuxing_analysis', {})
                if wuxing:
                    print(f"  主导五行: {wuxing.get('dominant_wuxing', '未知')}")

                fortune = chinese_analysis.get('fortune_analysis', {})
                if fortune:
                    print(f"  运势等级: {fortune.get('fortune_level', '未知')}")

            # 综合推荐
            comprehensive_rec = result.get('comprehensive_recommendation', {})
            if comprehensive_rec:
                print(f"\n最终推荐:")
                recommended = comprehensive_rec.get('recommended_numbers', [])
                if recommended:
                    print(f"  推荐号码: {recommended}")
                print(f"  综合置信度: {comprehensive_rec.get('confidence_score', 0):.4f}")
                print(f"  策略建议: {comprehensive_rec.get('strategy_advice', '无')}")
                print(f"  风险等级: {comprehensive_rec.get('risk_level', '未知')}")

            print("="*60)
            print("注意: 综合多种算法分析，仅供参考，请理性对待")

        except Exception as e:
            print(f"显示综合预测结果失败: {e}")

    async def _model_command(self, args: List[str]):
        """模型命令"""
        if not args:
            print("用法: model <info|list|train> [参数]")
            return
        
        subcommand = args[0].lower()
        
        try:
            if subcommand == "info":
                if len(args) < 2:
                    print("用法: model info <彩票类型>")
                    return
                
                lottery_type = args[1]
                model_info = self.system.incremental_learner.get_model_info(lottery_type)
                
                if model_info:
                    print(f"\n{lottery_type} 模型信息:")
                    print(f"  创建时间: {model_info['created_at']}")
                    print(f"  最后更新: {model_info['last_updated']}")
                    print(f"  版本: {model_info['version']}")
                    print(f"  训练数据量: {model_info['data_count']}")
                    print(f"  准确率: {model_info['accuracy']:.4f}")
                    print(f"  训练历史: {model_info['training_history_count']}次")
                    print(f"  预测历史: {model_info['prediction_history_count']}次")
                else:
                    print(f"✗ 未找到 {lottery_type} 模型")
            
            elif subcommand == "list":
                lottery_configs = self.system.config_manager.lottery_configs
                
                print("\n可用模型列表:")
                for lottery_type in lottery_configs.keys():
                    model_info = self.system.incremental_learner.get_model_info(lottery_type)
                    if model_info:
                        print(f"  ✓ {lottery_type}: 准确率{model_info['accuracy']:.4f}, "
                              f"数据{model_info['data_count']}条")
                    else:
                        print(f"  ✗ {lottery_type}: 模型未初始化")
            
            elif subcommand == "train":
                if len(args) < 2:
                    print("用法: model train <彩票类型>")
                    return
                
                lottery_type = args[1]
                print(f"开始训练 {lottery_type} 模型...")
                
                # 手动触发增量学习
                await self.system.incremental_learner._incremental_learn(lottery_type)
                print(f"✓ {lottery_type} 模型训练完成")
            
            else:
                print(f"未知子命令: {subcommand}")
                
        except Exception as e:
            print(f"模型命令执行失败: {e}")
    
    async def _jobs_command(self, args: List[str]):
        """任务命令"""
        if not args:
            args = ["list"]
        
        subcommand = args[0].lower()
        
        try:
            if subcommand == "list":
                jobs = self.system.scheduler.get_jobs()
                
                print(f"\n定时任务列表 (共{len(jobs)}个):")
                print("-" * 80)
                print(f"{'任务ID':<20} {'任务名称':<25} {'下次运行':<20} {'类型':<10}")
                print("-" * 80)
                
                for job in jobs:
                    next_run = job['next_run_time'].strftime('%m-%d %H:%M') if job['next_run_time'] else '未安排'
                    job_type = job.get('type', 'unknown')
                    print(f"{job['id']:<20} {job['name']:<25} {next_run:<20} {job_type:<10}")
            
            elif subcommand == "stats":
                stats = self.system.scheduler.get_job_statistics()
                
                print(f"\n任务统计信息:")
                print(f"  总任务数: {stats['total_jobs']}")
                print(f"  运行状态: {'运行中' if stats['running'] else '已停止'}")
                print(f"  任务类型分布:")
                for job_type, count in stats['job_types'].items():
                    print(f"    {job_type}: {count}个")
            
            else:
                print(f"未知子命令: {subcommand}")
                
        except Exception as e:
            print(f"任务命令执行失败: {e}")
    
    async def _config_command(self, args: List[str]):
        """配置命令"""
        if not args:
            args = ["show"]
        
        subcommand = args[0].lower()
        
        try:
            if subcommand == "show":
                config = self.system.config_manager.config
                print("\n系统配置:")
                print(json.dumps(config, indent=2, ensure_ascii=False, default=str))
            
            elif subcommand == "lottery":
                lottery_configs = self.system.config_manager.lottery_configs
                print("\n彩票配置:")
                print(json.dumps(lottery_configs, indent=2, ensure_ascii=False, default=str))
            
            else:
                print(f"未知子命令: {subcommand}")
                
        except Exception as e:
            print(f"配置命令执行失败: {e}")
    
    async def _exit_command(self, args: List[str]):
        """退出命令"""
        print("正在退出HuiCai系统...")
        self.running = False
