import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Space, Typography } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  PredictionOutlined,
  DatabaseOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchSystemStatus } from '../../store/slices/systemSlice';
import './AppLayout.css';

const { Header, Sider } = Layout;
const { Title } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  const { status } = useAppSelector((state) => state.system);

  useEffect(() => {
    // 定期获取系统状态
    const fetchStatus = () => {
      dispatch(fetchSystemStatus());
    };
    
    fetchStatus();
    const interval = setInterval(fetchStatus, 30000); // 每30秒更新一次
    
    return () => clearInterval(interval);
  }, [dispatch]);

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/prediction',
      icon: <PredictionOutlined />,
      label: '智能预测',
    },
    {
      key: '/data',
      icon: <DatabaseOutlined />,
      label: '数据管理',
    },
    {
      key: '/models',
      icon: <ExperimentOutlined />,
      label: '模型管理',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: '关于系统',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出系统',
      danger: true,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'about':
        // 显示关于对话框
        break;
      case 'logout':
        // 退出系统
        break;
      default:
        break;
    }
  };

  const getStatusColor = () => {
    if (!status) return 'default';
    switch (status.status) {
      case 'running': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = () => {
    if (!status) return '未知';
    switch (status.status) {
      case 'running': return '运行中';
      case 'stopped': return '已停止';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="dark"
        width={250}
      >
        <div className="logo">
          <div className="logo-icon">慧</div>
          {!collapsed && (
            <div className="logo-text">
              <Title level={4} style={{ color: 'white', margin: 0 }}>
                HuiCai
              </Title>
              <div style={{ color: '#888', fontSize: '12px' }}>
                智能预测系统
              </div>
            </div>
          )}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ marginTop: 16 }}
        />
      </Sider>
      
      <Layout>
        <Header className="app-header">
          <div className="header-left">
            <Space>
              <Badge 
                status={getStatusColor()} 
                text={getStatusText()}
              />
              {status && (
                <span style={{ color: '#666', fontSize: '12px' }}>
                  运行时间: {Math.floor(status.uptime / 3600)}小时
                </span>
              )}
            </Space>
          </div>
          
          <div className="header-right">
            <Space size="middle">
              <Badge count={0} size="small">
                <BellOutlined style={{ fontSize: '16px', color: '#666' }} />
              </Badge>
              
              <Dropdown
                menu={{
                  items: userMenuItems,
                  onClick: handleUserMenuClick,
                }}
                placement="bottomRight"
              >
                <Space style={{ cursor: 'pointer' }}>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <span>本地用户</span>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>
        
        {children}
      </Layout>
    </Layout>
  );
};

export default AppLayout;
