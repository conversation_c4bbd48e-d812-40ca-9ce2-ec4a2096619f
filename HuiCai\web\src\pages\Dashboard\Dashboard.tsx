import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Progress, Timeline, Table, Tag, Space } from 'antd';
import {
  TrophyOutlined,
  RocketOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchSystemInfo, fetchSystemStatus } from '../../store/slices/systemSlice';
import { fetchPredictionHistory } from '../../store/slices/predictionSlice';
import './Dashboard.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Dashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const { info, status } = useAppSelector((state) => state.system);
  const { history } = useAppSelector((state) => state.prediction);

  useEffect(() => {
    dispatch(fetchSystemInfo());
    dispatch(fetchSystemStatus());
    dispatch(fetchPredictionHistory({ limit: 10 }));
  }, [dispatch]);

  // 模拟数据
  const accuracyData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [
      {
        label: '预测准确率',
        data: [65, 68, 72, 70, 75, 78],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const modelDistributionData = {
    labels: ['LSTM', 'Transformer', '强化学习', '中国算法', '传统ML'],
    datasets: [
      {
        data: [25, 20, 15, 25, 15],
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
        ],
      },
    ],
  };

  const recentPredictions = [
    {
      key: '1',
      lottery: '双色球',
      date: '2025-01-15',
      numbers: '03,08,16,21,28,33+12',
      confidence: 0.78,
      status: 'pending',
    },
    {
      key: '2',
      lottery: '大乐透',
      date: '2025-01-14',
      numbers: '05,12,19,26,31+03,08',
      confidence: 0.72,
      status: 'verified',
    },
    {
      key: '3',
      lottery: '福彩3D',
      date: '2025-01-13',
      numbers: '5,8,9',
      confidence: 0.65,
      status: 'verified',
    },
  ];

  const predictionColumns = [
    {
      title: '彩票类型',
      dataIndex: 'lottery',
      key: 'lottery',
    },
    {
      title: '预测日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '预测号码',
      dataIndex: 'numbers',
      key: 'numbers',
      render: (numbers: string) => (
        <code style={{ background: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>
          {numbers}
        </code>
      ),
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      render: (confidence: number) => (
        <Progress 
          percent={Math.round(confidence * 100)} 
          size="small" 
          status={confidence > 0.7 ? 'success' : confidence > 0.5 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'verified' ? 'green' : 'orange'}>
          {status === 'verified' ? '已验证' : '待验证'}
        </Tag>
      ),
    },
  ];

  return (
    <div className="dashboard">
      <Row gutter={[16, 16]}>
        {/* 系统概览卡片 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={status?.status === 'running' ? '运行中' : '停止'}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: status?.status === 'running' ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃模型"
              value={status?.active_models || 0}
              prefix={<RocketOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="CPU使用率"
              value={status?.cpu_usage || 0}
              prefix={<DatabaseOutlined />}
              suffix="%"
            />
            <Progress 
              percent={status?.cpu_usage || 0} 
              size="small" 
              showInfo={false}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="内存使用率"
              value={status?.memory_usage || 0}
              prefix={<DatabaseOutlined />}
              suffix="%"
            />
            <Progress 
              percent={status?.memory_usage || 0} 
              size="small" 
              showInfo={false}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 预测准确率趋势 */}
        <Col xs={24} lg={16}>
          <Card title="预测准确率趋势" extra={<Tag color="blue">最近6个月</Tag>}>
            <Line 
              data={accuracyData} 
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'top' as const,
                  },
                  title: {
                    display: false,
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    max: 100,
                  },
                },
              }}
            />
          </Card>
        </Col>
        
        {/* 模型使用分布 */}
        <Col xs={24} lg={8}>
          <Card title="模型使用分布">
            <Doughnut 
              data={modelDistributionData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'bottom' as const,
                  },
                },
              }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 最近预测记录 */}
        <Col xs={24} lg={16}>
          <Card title="最近预测记录" extra={<a href="/prediction">查看全部</a>}>
            <Table 
              dataSource={recentPredictions}
              columns={predictionColumns}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        
        {/* 系统活动时间线 */}
        <Col xs={24} lg={8}>
          <Card title="系统活动">
            <Timeline
              items={[
                {
                  color: 'green',
                  children: (
                    <div>
                      <div>双色球预测完成</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>2分钟前</div>
                    </div>
                  ),
                },
                {
                  color: 'blue',
                  children: (
                    <div>
                      <div>LSTM模型训练完成</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>1小时前</div>
                    </div>
                  ),
                },
                {
                  color: 'orange',
                  children: (
                    <div>
                      <div>数据爬取任务启动</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>3小时前</div>
                    </div>
                  ),
                },
                {
                  children: (
                    <div>
                      <div>系统启动</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>今天 09:00</div>
                    </div>
                  ),
                },
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统信息 */}
      {info && (
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title="系统信息">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Statistic title="操作系统" value={info.platform} />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic title="Python版本" value={info.python_version} />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic title="CPU核心" value={info.cpu_count} suffix="核" />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic title="总内存" value={info.memory_total} suffix="GB" />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
