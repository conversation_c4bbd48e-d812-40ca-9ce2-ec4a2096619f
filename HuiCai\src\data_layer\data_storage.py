#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据存储管理器
负责数据库连接和数据存储操作

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import asyncpg
import redis.asyncio as redis
from typing import Dict, List, Any, Optional, Union
import json
from datetime import datetime, date
import logging
from pathlib import Path


class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化数据存储管理器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 数据库连接池
        self.pg_pool = None
        self.redis_client = None
        
        # 数据库配置
        self.db_config = config_manager.get('database', {})
        self.redis_config = config_manager.get('redis', {})
    
    async def initialize(self):
        """初始化数据存储"""
        await self._init_postgresql()
        await self._init_redis()
        await self._create_tables()
    
    async def _init_postgresql(self):
        """初始化PostgreSQL连接池"""
        try:
            self.pg_pool = await asyncpg.create_pool(
                host=self.db_config.get('host', 'localhost'),
                port=self.db_config.get('port', 5432),
                user=self.db_config.get('user', 'huicai'),
                password=self.db_config.get('password', 'huicai123'),
                database=self.db_config.get('name', 'huicai'),
                min_size=1,
                max_size=self.db_config.get('pool_size', 10)
            )
            self.logger.info("PostgreSQL连接池初始化成功")
        except Exception as e:
            self.logger.error(f"PostgreSQL连接池初始化失败: {e}")
            raise
    
    async def _init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_config.get('host', 'localhost'),
                port=self.redis_config.get('port', 6379),
                db=self.redis_config.get('db', 0),
                password=self.redis_config.get('password'),
                max_connections=self.redis_config.get('max_connections', 20),
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.logger.info("Redis连接初始化成功")
        except Exception as e:
            self.logger.error(f"Redis连接初始化失败: {e}")
            raise
    
    async def _create_tables(self):
        """创建数据库表"""
        sql_scripts = [
            # 彩票开奖记录表
            """
            CREATE TABLE IF NOT EXISTS lottery_draws (
                id SERIAL PRIMARY KEY,
                lottery_type VARCHAR(50) NOT NULL,
                draw_date DATE NOT NULL,
                draw_number VARCHAR(20) NOT NULL,
                numbers TEXT NOT NULL,
                extra_info JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, draw_date, draw_number)
            );
            """,
            
            # 创建索引
            """
            CREATE INDEX IF NOT EXISTS idx_lottery_draws_type_date 
            ON lottery_draws(lottery_type, draw_date);
            """,
            
            # 预测记录表
            """
            CREATE TABLE IF NOT EXISTS predictions (
                id SERIAL PRIMARY KEY,
                lottery_type VARCHAR(50) NOT NULL,
                prediction_date DATE NOT NULL,
                predicted_numbers TEXT NOT NULL,
                confidence_score FLOAT,
                algorithm_used VARCHAR(100),
                model_version VARCHAR(50),
                actual_numbers TEXT,
                accuracy_score FLOAT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """,
            
            # 模型训练记录表
            """
            CREATE TABLE IF NOT EXISTS model_training_logs (
                id SERIAL PRIMARY KEY,
                model_name VARCHAR(100) NOT NULL,
                lottery_type VARCHAR(50) NOT NULL,
                training_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_size INTEGER,
                training_accuracy FLOAT,
                validation_accuracy FLOAT,
                model_path TEXT,
                hyperparameters JSONB,
                training_duration INTEGER
            );
            """,
            
            # 系统日志表
            """
            CREATE TABLE IF NOT EXISTS system_logs (
                id SERIAL PRIMARY KEY,
                log_level VARCHAR(20) NOT NULL,
                module_name VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                extra_data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
        ]
        
        async with self.pg_pool.acquire() as conn:
            for sql in sql_scripts:
                try:
                    await conn.execute(sql)
                except Exception as e:
                    self.logger.error(f"创建表失败: {e}")
                    raise
        
        self.logger.info("数据库表创建完成")
    
    async def save_lottery_draw(self, lottery_type: str, draw_date: date, 
                               draw_number: str, numbers: List[int], 
                               extra_info: Optional[Dict] = None) -> bool:
        """
        保存彩票开奖记录
        
        Args:
            lottery_type: 彩票类型
            draw_date: 开奖日期
            draw_number: 期号
            numbers: 开奖号码
            extra_info: 额外信息
            
        Returns:
            bool: 保存是否成功
        """
        try:
            numbers_str = ','.join(map(str, numbers))
            extra_info_json = json.dumps(extra_info) if extra_info else None
            
            async with self.pg_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO lottery_draws 
                    (lottery_type, draw_date, draw_number, numbers, extra_info)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (lottery_type, draw_date, draw_number) 
                    DO UPDATE SET numbers = $4, extra_info = $5
                    """,
                    lottery_type, draw_date, draw_number, numbers_str, extra_info_json
                )
            
            self.logger.debug(f"保存开奖记录: {lottery_type} {draw_date} {draw_number}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存开奖记录失败: {e}")
            return False
    
    async def get_lottery_draws(self, lottery_type: str, 
                               start_date: Optional[date] = None,
                               end_date: Optional[date] = None,
                               limit: int = 1000) -> List[Dict]:
        """
        获取彩票开奖记录
        
        Args:
            lottery_type: 彩票类型
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            
        Returns:
            List[Dict]: 开奖记录列表
        """
        try:
            query = "SELECT * FROM lottery_draws WHERE lottery_type = $1"
            params = [lottery_type]
            
            if start_date:
                query += " AND draw_date >= $2"
                params.append(start_date)
            
            if end_date:
                query += f" AND draw_date <= ${len(params) + 1}"
                params.append(end_date)
            
            query += f" ORDER BY draw_date DESC LIMIT ${len(params) + 1}"
            params.append(limit)
            
            async with self.pg_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
            
            results = []
            for row in rows:
                result = dict(row)
                result['numbers'] = [int(x) for x in result['numbers'].split(',')]
                if result['extra_info']:
                    result['extra_info'] = json.loads(result['extra_info'])
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"获取开奖记录失败: {e}")
            return []
    
    async def cache_set(self, key: str, value: Any, expire: int = 3600):
        """设置缓存"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            await self.redis_client.setex(key, expire, value)
            return True
        except Exception as e:
            self.logger.error(f"设置缓存失败: {e}")
            return False
    
    async def cache_get(self, key: str) -> Any:
        """获取缓存"""
        try:
            value = await self.redis_client.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except:
                return value
                
        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            return None
    
    async def cache_delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False
    
    async def close(self):
        """关闭数据存储连接"""
        if self.pg_pool:
            await self.pg_pool.close()
            self.logger.info("PostgreSQL连接池已关闭")
        
        if self.redis_client:
            await self.redis_client.close()
            self.logger.info("Redis连接已关闭")
