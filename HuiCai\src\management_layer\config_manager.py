#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责加载和管理系统配置

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
import asyncio


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config
        """
        if config_dir is None:
            self.config_dir = Path(__file__).parent.parent.parent / "config"
        else:
            self.config_dir = Path(config_dir)
        
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置数据
        self.config = {}
        self.lottery_configs = {}
        self.algorithm_configs = {}
        self.crawler_configs = {}
        
        # 配置文件路径
        self.main_config_path = self.config_dir / "config.yaml"
        self.lottery_config_dir = self.config_dir / "lottery_configs"
        self.algorithm_config_dir = self.config_dir / "algorithm_configs"
        self.crawler_config_dir = self.config_dir / "crawler_configs"
        
        # 创建子目录
        self.lottery_config_dir.mkdir(exist_ok=True)
        self.algorithm_config_dir.mkdir(exist_ok=True)
        self.crawler_config_dir.mkdir(exist_ok=True)
    
    async def load_config(self):
        """加载所有配置"""
        await self._load_main_config()
        await self._load_lottery_configs()
        await self._load_algorithm_configs()
        await self._load_crawler_configs()
    
    async def _load_main_config(self):
        """加载主配置文件"""
        if not self.main_config_path.exists():
            await self._create_default_main_config()
        
        try:
            with open(self.main_config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f) or {}
        except Exception as e:
            raise Exception(f"加载主配置文件失败: {e}")
    
    async def _create_default_main_config(self):
        """创建默认主配置文件"""
        default_config = {
            'system': {
                'name': 'HuiCai',
                'version': '1.0.0',
                'debug': True,
                'timezone': 'Asia/Shanghai'
            },
            'database': {
                'type': 'postgresql',
                'host': 'localhost',
                'port': 5432,
                'name': 'huicai',
                'user': 'huicai',
                'password': 'huicai123',
                'pool_size': 10
            },
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'password': None,
                'max_connections': 20
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_path': 'data/logs/huicai.log',
                'max_file_size': '10MB',
                'backup_count': 5
            },
            'scheduler': {
                'timezone': 'Asia/Shanghai',
                'max_workers': 4,
                'job_defaults': {
                    'coalesce': False,
                    'max_instances': 1
                }
            },
            'crawler': {
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                ],
                'request_delay': 1,
                'timeout': 30,
                'retry_times': 3,
                'concurrent_limit': 5
            },
            'learning': {
                'model_save_path': 'data/models',
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 100,
                'validation_split': 0.2,
                'early_stopping_patience': 10
            }
        }
        
        with open(self.main_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
    
    async def _load_lottery_configs(self):
        """加载彩票配置"""
        self.lottery_configs = {}
        
        # 如果目录为空，创建默认配置
        if not any(self.lottery_config_dir.iterdir()):
            await self._create_default_lottery_configs()
        
        # 加载所有彩票配置文件
        for config_file in self.lottery_config_dir.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    lottery_name = config_file.stem
                    self.lottery_configs[lottery_name] = config_data
            except Exception as e:
                print(f"加载彩票配置文件 {config_file} 失败: {e}")
    
    async def _create_default_lottery_configs(self):
        """创建默认彩票配置"""
        # 双色球配置
        shuangseqiu_config = {
            'name': '双色球',
            'type': 'dual_color_ball',
            'red_ball_range': [1, 33],
            'red_ball_count': 6,
            'blue_ball_range': [1, 16],
            'blue_ball_count': 1,
            'draw_days': ['tue', 'thu', 'sun'],
            'draw_time': '21:15',
            'official_url': 'https://www.cwl.gov.cn',
            'data_source': {
                'primary': 'https://www.cwl.gov.cn/kjxx/ssq/',
                'backup': ['https://datachart.500.com/ssq/']
            }
        }
        
        # 大乐透配置
        daletou_config = {
            'name': '大乐透',
            'type': 'super_lotto',
            'front_range': [1, 35],
            'front_count': 5,
            'back_range': [1, 12],
            'back_count': 2,
            'draw_days': ['mon', 'wed', 'sat'],
            'draw_time': '20:30',
            'official_url': 'https://www.lottery.gov.cn',
            'data_source': {
                'primary': 'https://www.lottery.gov.cn/kjxx/dlt/',
                'backup': ['https://datachart.500.com/dlt/']
            }
        }
        
        # 福彩3D配置
        fucai3d_config = {
            'name': '福彩3D',
            'type': 'fucai_3d',
            'number_range': [0, 9],
            'number_count': 3,
            'draw_days': ['daily'],
            'draw_time': '20:30',
            'official_url': 'https://www.cwl.gov.cn',
            'data_source': {
                'primary': 'https://www.cwl.gov.cn/kjxx/fc3d/',
                'backup': ['https://datachart.500.com/fc3d/']
            }
        }

        # 保存配置文件
        configs = {
            'shuangseqiu': shuangseqiu_config,
            'daletou': daletou_config,
            'fucai3d': fucai3d_config
        }
        
        for name, config in configs.items():
            config_path = self.lottery_config_dir / f"{name}.yaml"
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    async def _load_algorithm_configs(self):
        """加载算法配置"""
        self.algorithm_configs = {}
        # 实现算法配置加载逻辑
        pass
    
    async def _load_crawler_configs(self):
        """加载爬虫配置"""
        self.crawler_configs = {}
        # 实现爬虫配置加载逻辑
        pass
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_lottery_config(self, lottery_name: str) -> Dict[str, Any]:
        """获取彩票配置"""
        return self.lottery_configs.get(lottery_name, {})
    
    def get_algorithm_config(self, algorithm_name: str) -> Dict[str, Any]:
        """获取算法配置"""
        return self.algorithm_configs.get(algorithm_name, {})
    
    def get_crawler_config(self, crawler_name: str) -> Dict[str, Any]:
        """获取爬虫配置"""
        return self.crawler_configs.get(crawler_name, {})
    
    async def save_config(self, config_data: Dict[str, Any]):
        """保存主配置"""
        self.config.update(config_data)
        with open(self.main_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        db_config = self.get('database', {})
        return (f"postgresql://{db_config.get('user')}:{db_config.get('password')}"
                f"@{db_config.get('host')}:{db_config.get('port')}/{db_config.get('name')}")
    
    def get_redis_url(self) -> str:
        """获取Redis连接URL"""
        redis_config = self.get('redis', {})
        password_part = f":{redis_config.get('password')}@" if redis_config.get('password') else ""
        return f"redis://{password_part}{redis_config.get('host')}:{redis_config.get('port')}/{redis_config.get('db')}"
