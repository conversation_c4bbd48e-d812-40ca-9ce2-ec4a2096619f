# HuiCai爬虫系统和本地数据库使用指南

## 🎯 系统概述

HuiCai系统现已集成完整的爬虫系统和本地化数据库，包括：

### 🕷️ **爬虫系统**
- **反爬虫模块**: 智能反检测技术
- **双色球专用爬虫**: 多数据源爬取双色球开奖数据
- **大乐透专用爬虫**: 多数据源爬取大乐透开奖数据
- **爬虫管理器**: 统一管理和调度

### 💾 **本地数据库**
- **双色球数据库**: 存储双色球开奖数据和统计信息
- **大乐透数据库**: 存储大乐透开奖数据和统计信息
- **双色球学习库**: 存储双色球AI模型参数和学习历史
- **大乐透学习库**: 存储大乐透AI模型参数和学习历史

## 🚀 快速开始

### 1. 初始化数据库
```bash
# 初始化四个本地数据库并填充示例数据
python init_databases.py
```

### 2. 测试爬虫系统
```bash
# 测试所有爬虫功能
python test_crawlers.py
```

### 3. 启动集成系统
```bash
# 启动包含爬虫和数据库功能的完整系统
python integrated_system.py
```

## 🕷️ 爬虫系统详解

### **反爬虫模块** (`anti_crawler.py`)

#### 核心功能
- **User-Agent轮换**: 9种不同的浏览器标识
- **请求节流**: 智能延迟控制，避免频率过高
- **代理支持**: 支持代理轮换和失败检测
- **重试机制**: 自动重试失败的请求
- **会话管理**: 连接池和超时控制

#### 使用示例
```python
from src.crawler_system.anti_crawler import create_anti_crawler_manager

# 创建反爬虫管理器
manager = create_anti_crawler_manager('moderate')

# 发起请求
response = manager.make_request('https://example.com')

# 获取统计信息
stats = manager.get_stats()
```

### **双色球爬虫** (`shuangseqiu_crawler.py`)

#### 数据源
1. **中国福利彩票官网**: 官方API接口
2. **500彩票网**: HTML页面解析
3. **新浪彩票**: HTML页面解析

#### 数据验证
- 红球: 6个不重复数字，范围1-33
- 蓝球: 1个数字，范围1-16
- 自动数据清洗和验证

#### 使用示例
```python
from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler

crawler = ShuangseqiuCrawler()

# 爬取最新10期数据
result = crawler.crawl_latest_data(10)

if result['status'] == 'success':
    print(f"成功爬取 {result['count']} 期数据")
    for draw in result['data']:
        print(f"期号: {draw['issue']}, 号码: {draw['red_balls']} + {draw['blue_ball']}")
```

### **大乐透爬虫** (`daletou_crawler.py`)

#### 数据源
1. **中国体育彩票官网**: 官方API接口
2. **500彩票网**: HTML页面解析
3. **新浪彩票**: HTML页面解析

#### 数据验证
- 前区球: 5个不重复数字，范围1-35
- 后区球: 2个不重复数字，范围1-12
- 自动数据清洗和验证

### **爬虫管理器** (`crawler_manager.py`)

#### 核心功能
- **统一管理**: 管理所有爬虫实例
- **定时调度**: 自动定时爬取最新数据
- **数据存储**: 自动保存到本地数据库
- **错误处理**: 智能错误恢复和重试
- **状态监控**: 实时监控爬虫状态

#### 调度策略
- 每2小时自动爬取一次
- 每天09:00和21:30定时爬取
- 支持手动触发爬取

## 💾 本地数据库详解

### **数据库架构**

#### 1. **双色球数据库** (`shuangseqiu_data.db`)
```sql
-- 开奖结果表
CREATE TABLE draw_results (
    id INTEGER PRIMARY KEY,
    issue TEXT UNIQUE,
    date TEXT,
    red_ball_1 INTEGER,
    red_ball_2 INTEGER,
    red_ball_3 INTEGER,
    red_ball_4 INTEGER,
    red_ball_5 INTEGER,
    red_ball_6 INTEGER,
    blue_ball INTEGER,
    sales_amount BIGINT,
    pool_amount BIGINT,
    source TEXT
);

-- 统计分析表
CREATE TABLE statistics (...);

-- 预测记录表
CREATE TABLE predictions (...);
```

#### 2. **大乐透数据库** (`daletou_data.db`)
```sql
-- 开奖结果表
CREATE TABLE draw_results (
    id INTEGER PRIMARY KEY,
    issue TEXT UNIQUE,
    date TEXT,
    front_ball_1 INTEGER,
    front_ball_2 INTEGER,
    front_ball_3 INTEGER,
    front_ball_4 INTEGER,
    front_ball_5 INTEGER,
    back_ball_1 INTEGER,
    back_ball_2 INTEGER,
    sales_amount BIGINT,
    pool_amount BIGINT,
    source TEXT
);
```

#### 3. **学习优化数据库** (`*_learning.db`)
```sql
-- 模型参数表
CREATE TABLE model_parameters (...);

-- 学习历史表
CREATE TABLE learning_history (...);

-- 优化策略表
CREATE TABLE optimization_strategies (...);

-- 知识库表
CREATE TABLE knowledge_base (...);
```

### **数据访问层**

#### 双色球数据操作
```python
from src.database.data_access_layer import ShuangseqiuDataAccess

ssq_data = ShuangseqiuDataAccess(db_manager)

# 插入开奖结果
ssq_data.insert_draw_result(
    issue="2024001",
    date="2024-01-01",
    red_balls=[1, 7, 12, 23, 28, 33],
    blue_ball=12
)

# 获取最新结果
latest = ssq_data.get_latest_results(10)

# 获取球号频率
frequency = ssq_data.get_ball_frequency('red', 365)
```

#### 学习数据操作
```python
from src.database.data_access_layer import LearningDataAccess

learning = LearningDataAccess(db_manager, 'shuangseqiu')

# 保存模型参数
learning.save_model_parameter('LSTM', 'learning_rate', 0.001)

# 记录学习历史
learning.record_learning_history(
    'LSTM', 'training',
    {'epoch': 10}, {'loss': 0.5},
    accuracy=0.75
)

# 更新知识库
learning.update_knowledge_base(
    'pattern', 'hot_numbers',
    {'numbers': [1, 7, 12], 'frequency': 0.15},
    confidence=0.8
)
```

## 🎛️ Web界面集成

### **新增API接口**

#### 1. 爬虫控制
```javascript
// 手动触发爬虫
fetch('/api/crawler/manual', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        lottery_type: 'shuangseqiu',
        count: 5
    })
});

// 获取爬虫状态
fetch('/api/crawler/status');
```

#### 2. 数据库信息
```javascript
// 获取数据库信息
fetch('/api/database/info');
```

#### 3. 系统状态（增强版）
```javascript
// 获取包含爬虫和数据库状态的系统信息
fetch('/api/system/status');
```

### **监控面板增强**

访问 `http://localhost:8000/monitoring` 查看：
- 爬虫运行状态
- 数据库使用情况
- 数据爬取统计
- 错误和异常监控

## 🔧 配置和优化

### **爬虫配置**
```python
# 反爬虫配置模式
ANTI_CRAWLER_CONFIGS = {
    'conservative': {  # 保守模式
        'min_delay': 2.0,
        'max_delay': 8.0,
        'max_retries': 5
    },
    'moderate': {      # 适中模式
        'min_delay': 1.0,
        'max_delay': 5.0,
        'max_retries': 3
    },
    'aggressive': {    # 激进模式
        'min_delay': 0.5,
        'max_delay': 2.0,
        'max_retries': 2
    }
}
```

### **数据库优化**
```python
# 定期优化数据库
db_manager.optimize_databases()

# 备份数据库
backup_path = db_manager.backup_database('shuangseqiu_data')

# 获取数据库信息
info = db_manager.get_database_info()
```

## 📊 使用统计

### **爬虫统计**
- 总爬取次数
- 成功/失败率
- 数据源使用情况
- 错误类型分析

### **数据库统计**
- 各数据库大小
- 记录数量
- 表结构信息
- 存储使用情况

## 🚨 注意事项

### **爬虫使用**
1. **遵守robots.txt**: 尊重网站爬虫协议
2. **合理频率**: 避免对目标网站造成压力
3. **数据验证**: 确保爬取数据的准确性
4. **错误处理**: 妥善处理网络异常

### **数据库维护**
1. **定期备份**: 重要数据及时备份
2. **性能优化**: 定期执行VACUUM和REINDEX
3. **存储监控**: 监控磁盘空间使用
4. **数据清理**: 定期清理过期数据

### **法律合规**
1. **仅供学习**: 本系统仅用于技术学习和研究
2. **数据使用**: 遵守相关法律法规
3. **商业用途**: 商业使用需获得相应授权

## 🎉 总结

HuiCai爬虫系统和本地数据库提供了：

- ✅ **完整的数据获取能力**: 多源爬虫，自动数据验证
- ✅ **本地化数据存储**: 四个专用数据库，高效存储
- ✅ **智能反爬虫技术**: 多重保护，稳定可靠
- ✅ **统一管理界面**: Web界面集成，操作便捷
- ✅ **学习优化支持**: 为AI模型提供数据和知识支持

这为HuiCai系统的AI分析和预测功能提供了强大的数据基础！🌟

---

**📧 技术支持**: 系统内置帮助和文档  
**🔗 项目地址**: HuiCai慧彩智能体系统  
**📅 更新日期**: 2025-01-15
