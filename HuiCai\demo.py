#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai 慧彩系统演示脚本
用于演示系统基本功能，无需数据库

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.management_layer.config_manager import ConfigManager
from src.management_layer.log_manager import LogManager


async def demo_config_manager():
    """演示配置管理器"""
    print("\n" + "="*50)
    print("演示配置管理器")
    print("="*50)
    
    try:
        config_manager = ConfigManager()
        await config_manager.load_config()
        
        print("✅ 配置管理器初始化成功")
        print(f"系统名称: {config_manager.get('system.name', 'HuiCai')}")
        print(f"系统版本: {config_manager.get('system.version', '1.0.0')}")
        
        # 显示彩票配置
        lottery_configs = config_manager.lottery_configs
        print(f"支持的彩票类型: {list(lottery_configs.keys())}")
        
        for lottery_type, config in lottery_configs.items():
            print(f"  {lottery_type}: {config.get('name', lottery_type)}")
        
        return config_manager
        
    except Exception as e:
        print(f"❌ 配置管理器演示失败: {e}")
        return None


async def demo_log_manager(config_manager):
    """演示日志管理器"""
    print("\n" + "="*50)
    print("演示日志管理器")
    print("="*50)
    
    try:
        log_manager = LogManager(config_manager)
        logger = log_manager.get_logger("Demo")
        
        print("✅ 日志管理器初始化成功")
        
        # 测试不同级别的日志
        logger.debug("这是调试信息")
        logger.info("这是信息日志")
        logger.warning("这是警告信息")
        logger.error("这是错误信息")
        
        print("✅ 日志输出测试完成")
        
        return log_manager
        
    except Exception as e:
        print(f"❌ 日志管理器演示失败: {e}")
        return None


async def demo_crawler_base():
    """演示爬虫基础功能"""
    print("\n" + "="*50)
    print("演示爬虫基础功能")
    print("="*50)
    
    try:
        from src.data_layer.crawlers.base_crawler import BaseCrawler
        from src.data_layer.crawlers.shuangseqiu_crawler import ShuangseqiuCrawler
        
        config_manager = ConfigManager()
        await config_manager.load_config()
        
        log_manager = LogManager(config_manager)
        logger = log_manager.get_logger("CrawlerDemo")
        
        # 创建双色球爬虫实例
        crawler = ShuangseqiuCrawler(config_manager, logger)
        await crawler.initialize()
        
        print("✅ 双色球爬虫初始化成功")
        print(f"爬虫类型: {crawler.lottery_type}")
        print(f"主要数据源: {crawler.primary_url}")
        print(f"备用数据源: {crawler.backup_urls}")
        
        # 获取统计信息
        stats = crawler.get_statistics()
        print(f"爬虫统计: {stats}")
        
        await crawler.close()
        print("✅ 爬虫演示完成")
        
    except Exception as e:
        print(f"❌ 爬虫演示失败: {e}")


async def demo_feature_extraction():
    """演示特征提取功能"""
    print("\n" + "="*50)
    print("演示特征提取功能")
    print("="*50)
    
    try:
        import numpy as np
        import pandas as pd
        from datetime import date, timedelta
        
        # 模拟一些双色球历史数据
        sample_data = []
        base_date = date.today() - timedelta(days=30)
        
        for i in range(10):
            draw_date = base_date + timedelta(days=i*3)
            # 生成模拟的双色球号码
            red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
            blue_ball = np.random.choice(range(1, 17), 1)[0]
            numbers = list(red_balls) + [blue_ball]
            
            sample_data.append({
                'draw_date': draw_date,
                'draw_number': f"2025{i+1:03d}",
                'numbers': numbers,
                'red_balls': red_balls,
                'blue_balls': [blue_ball]
            })
        
        df = pd.DataFrame(sample_data)
        print("✅ 生成模拟数据成功")
        print(f"数据量: {len(df)} 条")
        print("\n最近3期数据:")
        for _, row in df.tail(3).iterrows():
            print(f"  {row['draw_number']}: 红球{row['red_balls']} 蓝球{row['blue_balls']}")
        
        # 简单的特征提取演示
        print("\n特征提取演示:")
        for _, row in df.iterrows():
            numbers = row['numbers']
            features = {
                '平均值': np.mean(numbers),
                '标准差': np.std(numbers),
                '最大值': max(numbers),
                '最小值': min(numbers),
                '奇数个数': sum(1 for x in numbers if x % 2 == 1),
                '偶数个数': sum(1 for x in numbers if x % 2 == 0)
            }
            print(f"  {row['draw_number']}: {features}")
            break  # 只显示一个示例
        
        print("✅ 特征提取演示完成")
        
    except Exception as e:
        print(f"❌ 特征提取演示失败: {e}")


async def demo_cli_commands():
    """演示CLI命令功能"""
    print("\n" + "="*50)
    print("演示CLI命令功能")
    print("="*50)
    
    print("可用的CLI命令:")
    commands = [
        "help - 显示帮助信息",
        "status - 显示系统状态", 
        "crawl latest <type> - 爬取最新数据",
        "predict <type> - 预测下一期号码",
        "model info <type> - 显示模型信息",
        "jobs list - 显示定时任务",
        "config show - 显示配置信息"
    ]
    
    for cmd in commands:
        print(f"  {cmd}")
    
    print("\n支持的彩票类型:")
    print("  shuangseqiu - 双色球")
    print("  daletou - 大乐透 (计划中)")
    print("  fucai3d - 福彩3D (计划中)")
    
    print("\n使用示例:")
    print("  HuiCai> status")
    print("  HuiCai> crawl latest shuangseqiu") 
    print("  HuiCai> predict shuangseqiu")
    print("  HuiCai> model info shuangseqiu")


async def main():
    """主演示函数"""
    print("="*60)
    print("HuiCai 慧彩智能体系统 - 功能演示")
    print("="*60)
    print("这是第一阶段的功能演示，展示基础架构和核心组件")
    
    # 演示配置管理器
    config_manager = await demo_config_manager()
    if not config_manager:
        return
    
    # 演示日志管理器
    log_manager = await demo_log_manager(config_manager)
    if not log_manager:
        return
    
    # 演示爬虫功能
    await demo_crawler_base()
    
    # 演示特征提取
    await demo_feature_extraction()
    
    # 演示CLI命令
    await demo_cli_commands()
    
    print("\n" + "="*60)
    print("✅ HuiCai 慧彩系统第一阶段功能演示完成!")
    print("="*60)
    print("\n第一阶段已实现:")
    print("✅ 基础架构和配置管理")
    print("✅ 日志管理和监控")
    print("✅ 爬虫系统框架")
    print("✅ 增量学习框架")
    print("✅ 任务调度系统")
    print("✅ CLI交互界面")
    
    print("\n下一步:")
    print("🔄 安装PostgreSQL和Redis数据库")
    print("🔄 运行 python setup.py 初始化系统")
    print("🔄 运行 python main.py 启动完整系统")
    
    print("\n注意事项:")
    print("⚠️  本系统仅用于数据分析和学术研究")
    print("⚠️  彩票预测结果仅供参考，请理性对待")


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行演示
    asyncio.run(main())
