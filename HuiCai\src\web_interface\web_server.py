#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai 纯Python Web界面
使用FastAPI + Jinja2模板 + 静态文件，无需Node.js

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
from datetime import datetime

from fastapi import FastAPI, Request, HTTPException, Form
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.management_layer.config_manager import ConfigManager
    from src.management_layer.log_manager import LogManager
    from src.optimization.performance_optimizer import PerformanceOptimizer
except ImportError as e:
    print(f"警告: 导入模块失败 {e}")
    ConfigManager = None
    LogManager = None
    PerformanceOptimizer = None

# 创建FastAPI应用
app = FastAPI(
    title="HuiCai 慧彩智能体系统",
    description="基于Python的AI彩票分析预测系统",
    version="4.0.0"
)

# 设置模板和静态文件目录
templates_dir = Path(__file__).parent / "templates"
static_dir = Path(__file__).parent / "static"

# 确保目录存在
templates_dir.mkdir(exist_ok=True)
static_dir.mkdir(exist_ok=True)

templates = Jinja2Templates(directory=str(templates_dir))

# 挂载静态文件
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 全局变量
config_manager = None
logger = None
performance_optimizer = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global config_manager, logger, performance_optimizer
    
    try:
        if ConfigManager:
            config_manager = ConfigManager()
            await config_manager.load_config()
            
            if LogManager:
                log_manager = LogManager(config_manager)
                logger = log_manager.get_logger("WebInterface")
            
            if PerformanceOptimizer:
                performance_optimizer = PerformanceOptimizer(config_manager, logger)
        
        print("✅ HuiCai Web界面启动成功")
        
    except Exception as e:
        print(f"⚠️ Web界面启动警告: {e}")

# 路由定义

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """仪表板页面"""
    try:
        # 获取系统状态
        system_info = await get_system_info()
        
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "title": "仪表板",
            "system_info": system_info,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    except Exception as e:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": str(e)
        })

@app.get("/prediction", response_class=HTMLResponse)
async def prediction_page(request: Request):
    """预测页面"""
    lottery_types = [
        {"value": "shuangseqiu", "label": "双色球", "desc": "6红球+1蓝球"},
        {"value": "daletou", "label": "大乐透", "desc": "5前区+2后区"},
        {"value": "fucai3d", "label": "福彩3D", "desc": "3位数字"}
    ]
    
    methods = [
        {"value": "traditional", "label": "传统机器学习", "desc": "基于统计学习"},
        {"value": "chinese", "label": "中国特色算法", "desc": "五行八卦算法"},
        {"value": "deeplearning", "label": "深度学习", "desc": "神经网络预测"},
        {"value": "comprehensive", "label": "综合预测", "desc": "多算法融合"}
    ]
    
    return templates.TemplateResponse("prediction.html", {
        "request": request,
        "title": "智能预测",
        "lottery_types": lottery_types,
        "methods": methods
    })

@app.post("/api/predict")
async def make_prediction(
    lottery_type: str = Form(...),
    method: str = Form(...)
):
    """执行预测"""
    try:
        # 模拟预测逻辑
        import random
        
        if lottery_type == "shuangseqiu":
            red_balls = sorted(random.sample(range(1, 34), 6))
            blue_ball = random.randint(1, 16)
            numbers = red_balls + [blue_ball]
        elif lottery_type == "daletou":
            front_balls = sorted(random.sample(range(1, 36), 5))
            back_balls = sorted(random.sample(range(1, 13), 2))
            numbers = front_balls + back_balls
        elif lottery_type == "fucai3d":
            numbers = [random.randint(0, 9) for _ in range(3)]
        else:
            numbers = [1, 2, 3, 4, 5, 6, 7]
        
        confidence = random.uniform(0.5, 0.9)
        
        result = {
            "success": True,
            "lottery_type": lottery_type,
            "method": method,
            "numbers": numbers,
            "confidence": round(confidence, 3),
            "timestamp": datetime.now().isoformat(),
            "details": {
                "algorithm": f"{method}算法",
                "analysis": "基于历史数据分析和模式识别",
                "recommendation": "仅供参考，请理性投注"
            }
        }
        
        return JSONResponse(content=result)
        
    except Exception as e:
        return JSONResponse(content={
            "success": False,
            "error": str(e)
        }, status_code=500)

@app.get("/data", response_class=HTMLResponse)
async def data_management(request: Request):
    """数据管理页面"""
    # 模拟历史数据
    recent_draws = [
        {
            "date": "2025-01-15",
            "lottery": "双色球",
            "numbers": "03,08,16,21,28,33+12",
            "status": "已开奖"
        },
        {
            "date": "2025-01-14",
            "lottery": "大乐透", 
            "numbers": "05,12,19,26,31+03,08",
            "status": "已开奖"
        },
        {
            "date": "2025-01-13",
            "lottery": "福彩3D",
            "numbers": "5,8,9",
            "status": "已开奖"
        }
    ]
    
    return templates.TemplateResponse("data.html", {
        "request": request,
        "title": "数据管理",
        "recent_draws": recent_draws
    })

@app.get("/models", response_class=HTMLResponse)
async def model_management(request: Request):
    """模型管理页面"""
    models = [
        {
            "name": "LSTM神经网络",
            "lottery": "双色球",
            "accuracy": "78.5%",
            "status": "运行中",
            "last_trained": "2025-01-15"
        },
        {
            "name": "随机森林",
            "lottery": "大乐透",
            "accuracy": "72.3%", 
            "status": "运行中",
            "last_trained": "2025-01-14"
        },
        {
            "name": "五行算法",
            "lottery": "福彩3D",
            "accuracy": "65.8%",
            "status": "运行中",
            "last_trained": "2025-01-13"
        }
    ]
    
    return templates.TemplateResponse("models.html", {
        "request": request,
        "title": "模型管理",
        "models": models
    })

@app.get("/settings", response_class=HTMLResponse)
async def system_settings(request: Request):
    """系统设置页面"""
    settings = {
        "theme": "light",
        "language": "zh-CN",
        "auto_predict": True,
        "notification": True,
        "backup": True
    }
    
    return templates.TemplateResponse("settings.html", {
        "request": request,
        "title": "系统设置",
        "settings": settings
    })

@app.get("/api/system/info")
async def get_system_info():
    """获取系统信息API"""
    try:
        import platform
        import psutil
        
        info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": round(psutil.virtual_memory().total / (1024**3), 1),
            "memory_used": round(psutil.virtual_memory().used / (1024**3), 1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_free": round(psutil.disk_usage('.').free / (1024**3), 1),
            "uptime": "2小时30分钟",
            "status": "运行中",
            "active_models": 3,
            "predictions_today": 15
        }
        
        return info
        
    except Exception as e:
        return {
            "error": str(e),
            "status": "错误"
        }

@app.get("/api/performance/optimize")
async def optimize_performance():
    """性能优化API"""
    try:
        if performance_optimizer:
            result = await performance_optimizer.optimize_system()
            return {"success": True, "result": result}
        else:
            # 模拟优化结果
            return {
                "success": True,
                "result": {
                    "memory_freed": "128MB",
                    "cache_cleared": True,
                    "optimization_time": "2.3秒"
                }
            }
    except Exception as e:
        return {"success": False, "error": str(e)}

def main():
    """主函数"""
    print("="*60)
    print("🌐 HuiCai 纯Python Web界面")
    print("="*60)
    print("✅ 无需Node.js，纯Python实现")
    print("✅ 基于FastAPI + Jinja2模板")
    print("✅ 响应式设计，现代化界面")
    
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 启动服务器
    print("\n🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🛑 按 Ctrl+C 停止服务器")
    print("="*60)
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
