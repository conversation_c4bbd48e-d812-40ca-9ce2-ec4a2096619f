#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
初始化四个本地数据库并填充示例数据

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import logging
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.local_database_manager import LocalDatabaseManager
from src.database.data_access_layer import <PERSON><PERSON><PERSON><PERSON>uDataAcc<PERSON>, DaletouDataAccess, LearningDataAccess


def generate_sample_shuangseqiu_data(count: int = 50) -> list:
    """生成双色球示例数据"""
    data = []
    base_date = datetime.now() - timedelta(days=count * 3)  # 每3天一期
    
    for i in range(count):
        issue_date = base_date + timedelta(days=i * 3)
        issue = f"2024{str(i+1).zfill(3)}"
        
        # 生成红球（1-33，6个不重复）
        red_balls = sorted(random.sample(range(1, 34), 6))
        
        # 生成蓝球（1-16，1个）
        blue_ball = random.randint(1, 16)
        
        # 生成销售额和奖池
        sales_amount = random.randint(200000000, 500000000)  # 2-5亿
        pool_amount = random.randint(500000000, 1500000000)  # 5-15亿
        
        data.append({
            'issue': issue,
            'date': issue_date.strftime('%Y-%m-%d'),
            'red_balls': red_balls,
            'blue_ball': blue_ball,
            'sales_amount': sales_amount,
            'pool_amount': pool_amount,
            'source': '示例数据'
        })
    
    return data


def generate_sample_daletou_data(count: int = 50) -> list:
    """生成大乐透示例数据"""
    data = []
    base_date = datetime.now() - timedelta(days=count * 3)  # 每3天一期
    
    for i in range(count):
        issue_date = base_date + timedelta(days=i * 3)
        issue = f"24{str(i+1).zfill(3)}"
        
        # 生成前区球（1-35，5个不重复）
        front_balls = sorted(random.sample(range(1, 36), 5))
        
        # 生成后区球（1-12，2个不重复）
        back_balls = sorted(random.sample(range(1, 13), 2))
        
        # 生成销售额和奖池
        sales_amount = random.randint(150000000, 400000000)  # 1.5-4亿
        pool_amount = random.randint(300000000, 1000000000)  # 3-10亿
        
        data.append({
            'issue': issue,
            'date': issue_date.strftime('%Y-%m-%d'),
            'front_balls': front_balls,
            'back_balls': back_balls,
            'sales_amount': sales_amount,
            'pool_amount': pool_amount,
            'source': '示例数据'
        })
    
    return data


def init_sample_learning_data(learning_access: LearningDataAccess, lottery_type: str):
    """初始化学习数据"""
    
    # 保存示例模型参数
    models = ['LSTM', 'RandomForest', 'SVM', 'NeuralNetwork']
    
    for model in models:
        # 保存模型参数
        learning_access.save_model_parameter(
            model, 'learning_rate', 0.001, 'float'
        )
        learning_access.save_model_parameter(
            model, 'epochs', 100, 'int'
        )
        learning_access.save_model_parameter(
            model, 'batch_size', 32, 'int'
        )
        learning_access.save_model_parameter(
            model, 'hidden_layers', [128, 64, 32], 'json'
        )
        
        # 记录学习历史
        for epoch in range(1, 11):
            accuracy = 0.5 + (epoch * 0.03) + random.uniform(-0.02, 0.02)
            loss = 1.0 - (epoch * 0.08) + random.uniform(-0.05, 0.05)
            
            learning_access.record_learning_history(
                model, 'training',
                {'epoch': epoch, 'batch_size': 32},
                {'predictions': [1, 2, 3, 4, 5]},
                accuracy, loss, epoch
            )
    
    # 保存优化策略
    strategies = [
        {
            'name': f'{lottery_type}_frequency_analysis',
            'config': {
                'method': 'frequency_analysis',
                'window_size': 30,
                'weight_decay': 0.95
            },
            'score': random.uniform(0.6, 0.8)
        },
        {
            'name': f'{lottery_type}_pattern_recognition',
            'config': {
                'method': 'pattern_recognition',
                'pattern_length': 5,
                'similarity_threshold': 0.8
            },
            'score': random.uniform(0.65, 0.85)
        },
        {
            'name': f'{lottery_type}_ensemble_method',
            'config': {
                'method': 'ensemble',
                'models': ['LSTM', 'RandomForest', 'SVM'],
                'weights': [0.4, 0.3, 0.3]
            },
            'score': random.uniform(0.7, 0.9)
        }
    ]
    
    for strategy in strategies:
        learning_access.save_optimization_strategy(
            strategy['name'],
            strategy['config'],
            strategy['score']
        )
    
    # 更新知识库
    knowledge_items = [
        {
            'type': 'pattern',
            'key': 'hot_numbers',
            'value': {'numbers': [1, 7, 12, 23, 28], 'frequency': 0.15},
            'confidence': 0.8
        },
        {
            'type': 'pattern',
            'key': 'cold_numbers',
            'value': {'numbers': [2, 9, 15, 31], 'frequency': 0.05},
            'confidence': 0.7
        },
        {
            'type': 'trend',
            'key': 'recent_trend',
            'value': {'direction': 'increasing', 'strength': 0.6},
            'confidence': 0.65
        },
        {
            'type': 'correlation',
            'key': 'number_pairs',
            'value': {'pairs': [[1, 7], [12, 23], [28, 33]], 'correlation': 0.3},
            'confidence': 0.55
        }
    ]
    
    for item in knowledge_items:
        learning_access.update_knowledge_base(
            item['type'],
            item['key'],
            item['value'],
            item['confidence'],
            '系统分析'
        )


def main():
    """主函数"""
    print("🎯 HuiCai数据库初始化脚本")
    print("="*50)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 初始化数据库管理器
        print("📊 初始化数据库管理器...")
        db_manager = LocalDatabaseManager()
        
        # 初始化数据访问层
        print("🔗 初始化数据访问层...")
        ssq_data = ShuangseqiuDataAccess(db_manager)
        dlt_data = DaletouDataAccess(db_manager)
        ssq_learning = LearningDataAccess(db_manager, 'shuangseqiu')
        dlt_learning = LearningDataAccess(db_manager, 'daletou')
        
        # 生成并插入双色球示例数据
        print("🔴 生成双色球示例数据...")
        ssq_sample_data = generate_sample_shuangseqiu_data(50)
        ssq_inserted = ssq_data.batch_insert_draw_results(ssq_sample_data)
        print(f"  ✅ 插入了 {ssq_inserted} 条双色球开奖记录")
        
        # 生成并插入大乐透示例数据
        print("🔵 生成大乐透示例数据...")
        dlt_sample_data = generate_sample_daletou_data(50)
        dlt_inserted = dlt_data.batch_insert_draw_results(dlt_sample_data)
        print(f"  ✅ 插入了 {dlt_inserted} 条大乐透开奖记录")
        
        # 初始化双色球学习数据
        print("🧠 初始化双色球学习数据...")
        init_sample_learning_data(ssq_learning, 'shuangseqiu')
        print("  ✅ 双色球学习数据初始化完成")
        
        # 初始化大乐透学习数据
        print("🧠 初始化大乐透学习数据...")
        init_sample_learning_data(dlt_learning, 'daletou')
        print("  ✅ 大乐透学习数据初始化完成")
        
        # 优化数据库
        print("⚡ 优化数据库...")
        db_manager.optimize_databases()
        
        # 显示数据库信息
        print("\n📈 数据库信息:")
        db_info = db_manager.get_database_info()
        for db_name, info in db_info.items():
            if 'error' not in info:
                print(f"  📁 {db_name}:")
                print(f"    - 大小: {info['size_mb']} MB")
                print(f"    - 表数量: {len(info['tables'])}")
                print(f"    - 总记录数: {info['total_records']}")
                for table, count in info['table_counts'].items():
                    print(f"      • {table}: {count} 条记录")
            else:
                print(f"  ❌ {db_name}: {info['error']}")
        
        # 关闭连接
        db_manager.close_all_connections()
        
        print("\n✅ 数据库初始化完成！")
        print("="*50)
        print("📍 数据库文件位置: ./databases/")
        print("🔴 双色球数据库: shuangseqiu_data.db")
        print("🔵 大乐透数据库: daletou_data.db")
        print("🧠 双色球学习库: shuangseqiu_learning.db")
        print("🧠 大乐透学习库: daletou_learning.db")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        logging.error(f"数据库初始化异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
