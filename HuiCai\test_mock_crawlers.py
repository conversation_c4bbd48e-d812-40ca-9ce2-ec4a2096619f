#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟爬虫测试脚本
测试使用模拟数据源的爬虫功能

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_mock_ssq_crawler():
    """测试双色球模拟爬虫"""
    print("🔴 测试双色球模拟爬虫...")
    print("-" * 40)
    
    try:
        from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler
        
        # 创建爬虫实例
        crawler = ShuangseqiuCrawler()
        print(f"✅ 爬虫创建成功，数据源数量: {len(crawler.data_sources)}")
        print(f"📍 当前数据源: {crawler.data_sources[0]['name']}")
        
        # 测试爬取数据
        print("\n📡 爬取最新5期数据...")
        result = crawler.crawl_latest_data(5)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！")
            print(f"📊 数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            
            # 显示数据详情
            print("\n🎯 开奖数据:")
            for i, draw in enumerate(result['data'][:3]):  # 显示前3期
                issue = draw['issue']
                date = draw['date']
                red_balls = draw['red_balls']
                blue_ball = draw['blue_ball']
                
                red_str = ' '.join([f"{ball:02d}" for ball in red_balls])
                print(f"  {i+1}. {issue} ({date}): {red_str} + {blue_ball:02d}")
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
        
        crawler.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_dlt_crawler():
    """测试大乐透模拟爬虫"""
    print("\n🔵 测试大乐透模拟爬虫...")
    print("-" * 40)
    
    try:
        from src.crawler_system.daletou_crawler import DaletouCrawler
        
        # 创建爬虫实例
        crawler = DaletouCrawler()
        print(f"✅ 爬虫创建成功，数据源数量: {len(crawler.data_sources)}")
        print(f"📍 当前数据源: {crawler.data_sources[0]['name']}")
        
        # 测试爬取数据
        print("\n📡 爬取最新5期数据...")
        result = crawler.crawl_latest_data(5)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！")
            print(f"📊 数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            
            # 显示数据详情
            print("\n🎯 开奖数据:")
            for i, draw in enumerate(result['data'][:3]):  # 显示前3期
                issue = draw['issue']
                date = draw['date']
                front_balls = draw['front_balls']
                back_balls = draw['back_balls']
                
                front_str = ' '.join([f"{ball:02d}" for ball in front_balls])
                back_str = ' '.join([f"{ball:02d}" for ball in back_balls])
                print(f"  {i+1}. {issue} ({date}): {front_str} + {back_str}")
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
        
        crawler.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_manager():
    """测试爬虫管理器"""
    print("\n🎛️ 测试爬虫管理器...")
    print("-" * 40)
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        
        # 创建管理器
        manager = CrawlerManager()
        print("✅ 爬虫管理器创建成功")
        
        # 测试手动爬取双色球
        print("\n📡 手动爬取双色球数据...")
        result = manager.manual_crawl('shuangseqiu', 3)
        if result['status'] == 'success':
            print(f"✅ 双色球爬取成功，保存 {result.get('saved_count', 0)} 条记录")
        else:
            print(f"❌ 双色球爬取失败: {result.get('error', '未知错误')}")
        
        # 测试手动爬取大乐透
        print("\n📡 手动爬取大乐透数据...")
        result = manager.manual_crawl('daletou', 3)
        if result['status'] == 'success':
            print(f"✅ 大乐透爬取成功，保存 {result.get('saved_count', 0)} 条记录")
        else:
            print(f"❌ 大乐透爬取失败: {result.get('error', '未知错误')}")
        
        # 显示管理器状态
        print("\n📊 管理器状态:")
        status = manager.get_crawler_status()
        print(f"  - 调度器运行: {status.get('scheduler_running', False)}")
        print(f"  - 总爬取次数: {status.get('statistics', {}).get('total_crawls', 0)}")
        print(f"  - 成功次数: {status.get('statistics', {}).get('successful_crawls', 0)}")
        
        manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 HuiCai模拟爬虫测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success_count = 0
    total_tests = 3
    
    # 测试双色球爬虫
    if test_mock_ssq_crawler():
        success_count += 1
    
    # 测试大乐透爬虫
    if test_mock_dlt_crawler():
        success_count += 1
    
    # 测试爬虫管理器
    if test_crawler_manager():
        success_count += 1
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
        print("\n✅ 模拟爬虫系统功能正常:")
        print("  🔴 双色球爬虫 - 可生成模拟开奖数据")
        print("  🔵 大乐透爬虫 - 可生成模拟开奖数据")
        print("  🎛️ 爬虫管理器 - 可统一管理和调度")
        print("  💾 数据存储 - 可保存到本地数据库")
        print("\n📝 说明:")
        print("  - 由于官方API访问限制，系统使用模拟数据源")
        print("  - 模拟数据遵循真实彩票规则和格式")
        print("  - 可用于系统测试、开发和演示")
        print("  - 实际部署时可替换为真实数据源")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
