#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫管理器
集成反爬虫模块和专用爬虫，管理数据爬取和存储

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import threading

from .shuangseqiu_crawler import ShuangseqiuCrawler
from .daletou_crawler import DaletouCrawler
from ..database.local_database_manager import LocalDatabaseManager
from ..database.data_access_layer import ShuangseqiuDataAccess, DaletouDataAccess


class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库管理器
        self.db_manager = LocalDatabaseManager()
        
        # 初始化数据访问层
        self.ssq_data = ShuangseqiuDataAccess(self.db_manager)
        self.dlt_data = DaletouDataAccess(self.db_manager)
        
        # 初始化爬虫
        self.ssq_crawler = ShuangseqiuCrawler(self.config.get('shuangseqiu', {}))
        self.dlt_crawler = DaletouCrawler(self.config.get('daletou', {}))
        
        # 爬虫状态
        self.crawlers = {
            'shuangseqiu': {
                'crawler': self.ssq_crawler,
                'data_access': self.ssq_data,
                'last_crawl': None,
                'status': 'idle',
                'error_count': 0
            },
            'daletou': {
                'crawler': self.dlt_crawler,
                'data_access': self.dlt_data,
                'last_crawl': None,
                'status': 'idle',
                'error_count': 0
            }
        }
        
        # 调度器
        self.scheduler_thread = None
        self.scheduler_running = False
        
        # 统计信息
        self.stats = {
            'total_crawls': 0,
            'successful_crawls': 0,
            'failed_crawls': 0,
            'total_records': 0,
            'start_time': datetime.now()
        }
    
    def start_scheduler(self):
        """启动调度器"""
        if self.scheduler_running:
            self.logger.warning("调度器已在运行")
            return
        
        self.scheduler_running = True
        
        # 设置调度任务
        schedule.every(2).hours.do(self._scheduled_crawl_all)
        schedule.every().day.at("09:00").do(self._scheduled_crawl_all)
        schedule.every().day.at("21:30").do(self._scheduled_crawl_all)
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("爬虫调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        self.scheduler_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("爬虫调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"调度器运行异常: {e}")
                time.sleep(60)
    
    def _scheduled_crawl_all(self):
        """定时爬取所有彩票数据"""
        self.logger.info("开始定时爬取任务")
        
        try:
            # 爬取双色球
            self.crawl_lottery_data('shuangseqiu', count=10)
            
            # 爬取大乐透
            self.crawl_lottery_data('daletou', count=10)
            
        except Exception as e:
            self.logger.error(f"定时爬取任务异常: {e}")
    
    def crawl_lottery_data(self, lottery_type: str, count: int = 10) -> Dict[str, Any]:
        """爬取指定彩票数据"""
        if lottery_type not in self.crawlers:
            return {'status': 'error', 'message': f'不支持的彩票类型: {lottery_type}'}
        
        crawler_info = self.crawlers[lottery_type]
        crawler = crawler_info['crawler']
        data_access = crawler_info['data_access']
        
        try:
            # 更新状态
            crawler_info['status'] = 'crawling'
            self.stats['total_crawls'] += 1
            
            self.logger.info(f"开始爬取{lottery_type}数据，期数: {count}")
            
            # 执行爬取
            result = crawler.crawl_latest_data(count)
            
            if result['status'] == 'success':
                # 保存数据到数据库
                saved_count = self._save_crawled_data(lottery_type, result['data'])
                
                # 更新统计
                self.stats['successful_crawls'] += 1
                self.stats['total_records'] += saved_count
                crawler_info['last_crawl'] = datetime.now()
                crawler_info['error_count'] = 0
                crawler_info['status'] = 'idle'
                
                self.logger.info(f"{lottery_type}数据爬取成功，保存{saved_count}条记录")
                
                return {
                    'status': 'success',
                    'lottery_type': lottery_type,
                    'crawled_count': len(result['data']),
                    'saved_count': saved_count,
                    'source': result.get('source', ''),
                    'timestamp': datetime.now().isoformat()
                }
            
            else:
                # 爬取失败
                self.stats['failed_crawls'] += 1
                crawler_info['error_count'] += 1
                crawler_info['status'] = 'error'
                
                self.logger.error(f"{lottery_type}数据爬取失败: {result.get('error', '未知错误')}")
                
                return {
                    'status': 'failed',
                    'lottery_type': lottery_type,
                    'error': result.get('error', '未知错误'),
                    'timestamp': datetime.now().isoformat()
                }
        
        except Exception as e:
            self.stats['failed_crawls'] += 1
            crawler_info['error_count'] += 1
            crawler_info['status'] = 'error'
            
            self.logger.error(f"爬取{lottery_type}数据异常: {e}")
            
            return {
                'status': 'error',
                'lottery_type': lottery_type,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _save_crawled_data(self, lottery_type: str, data: List[Dict[str, Any]]) -> int:
        """保存爬取的数据"""
        if not data:
            return 0
        
        try:
            if lottery_type == 'shuangseqiu':
                return self.ssq_data.batch_insert_draw_results(data)
            elif lottery_type == 'daletou':
                return self.dlt_data.batch_insert_draw_results(data)
            else:
                return 0
                
        except Exception as e:
            self.logger.error(f"保存{lottery_type}数据失败: {e}")
            return 0
    
    def crawl_all_latest(self, count: int = 10) -> Dict[str, Any]:
        """爬取所有彩票的最新数据"""
        results = {}
        
        for lottery_type in self.crawlers.keys():
            results[lottery_type] = self.crawl_lottery_data(lottery_type, count)
        
        return {
            'status': 'completed',
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_crawler_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        status = {
            'scheduler_running': self.scheduler_running,
            'crawlers': {},
            'statistics': self.stats.copy()
        }
        
        # 计算运行时间
        runtime = datetime.now() - self.stats['start_time']
        status['statistics']['runtime_seconds'] = runtime.total_seconds()
        status['statistics']['runtime_string'] = str(runtime)
        
        # 获取各爬虫状态
        for lottery_type, crawler_info in self.crawlers.items():
            crawler_stats = crawler_info['crawler'].get_crawler_stats()
            
            status['crawlers'][lottery_type] = {
                'status': crawler_info['status'],
                'last_crawl': crawler_info['last_crawl'].isoformat() if crawler_info['last_crawl'] else None,
                'error_count': crawler_info['error_count'],
                'crawler_stats': crawler_stats
            }
        
        return status
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        summary = {}
        
        try:
            # 双色球数据摘要
            ssq_latest = self.ssq_data.get_latest_results(1)
            ssq_frequency = self.ssq_data.get_ball_frequency('red', 30)
            
            summary['shuangseqiu'] = {
                'latest_issue': ssq_latest[0] if ssq_latest else None,
                'red_ball_frequency_30days': ssq_frequency,
                'total_records': len(self.ssq_data.get_latest_results(10000))
            }
            
            # 大乐透数据摘要
            dlt_latest = self.dlt_data.get_latest_results(1)
            dlt_frequency = self.dlt_data.get_ball_frequency('front', 30)
            
            summary['daletou'] = {
                'latest_issue': dlt_latest[0] if dlt_latest else None,
                'front_ball_frequency_30days': dlt_frequency,
                'total_records': len(self.dlt_data.get_latest_results(10000))
            }
            
        except Exception as e:
            self.logger.error(f"获取数据摘要失败: {e}")
            summary['error'] = str(e)
        
        return summary
    
    def manual_crawl(self, lottery_type: str, count: int = 5) -> Dict[str, Any]:
        """手动触发爬取"""
        self.logger.info(f"手动触发{lottery_type}数据爬取")
        return self.crawl_lottery_data(lottery_type, count)
    
    def reset_crawler_errors(self, lottery_type: str = None):
        """重置爬虫错误计数"""
        if lottery_type:
            if lottery_type in self.crawlers:
                self.crawlers[lottery_type]['error_count'] = 0
                self.crawlers[lottery_type]['status'] = 'idle'
                self.logger.info(f"已重置{lottery_type}爬虫错误计数")
        else:
            for crawler_info in self.crawlers.values():
                crawler_info['error_count'] = 0
                crawler_info['status'] = 'idle'
            self.logger.info("已重置所有爬虫错误计数")
    
    def close(self):
        """关闭爬虫管理器"""
        self.logger.info("正在关闭爬虫管理器...")
        
        # 停止调度器
        self.stop_scheduler()
        
        # 关闭爬虫
        for crawler_info in self.crawlers.values():
            crawler_info['crawler'].close()
        
        # 关闭数据库连接
        self.db_manager.close_all_connections()
        
        self.logger.info("爬虫管理器已关闭")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    manager = CrawlerManager()
    
    # 测试手动爬取
    result = manager.manual_crawl('shuangseqiu', 3)
    print(f"双色球爬取结果: {result}")
    
    result = manager.manual_crawl('daletou', 3)
    print(f"大乐透爬取结果: {result}")
    
    # 显示状态
    status = manager.get_crawler_status()
    print(f"爬虫状态: {status}")
    
    # 显示数据摘要
    summary = manager.get_data_summary()
    print(f"数据摘要: {summary}")
    
    manager.close()
