#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强化学习模块
基于Q-Learning和Actor-Critic的彩票预测策略学习

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Dense, Input, Concatenate
from tensorflow.keras.optimizers import Adam
from collections import deque
import random
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime
import pickle
import os


class LotteryEnvironment:
    """彩票环境模拟器"""
    
    def __init__(self, historical_data: pd.DataFrame, lottery_type: str):
        """
        初始化彩票环境
        
        Args:
            historical_data: 历史开奖数据
            lottery_type: 彩票类型
        """
        self.historical_data = historical_data.copy()
        self.lottery_type = lottery_type
        self.current_step = 0
        self.max_steps = len(historical_data) - 1
        
        # 状态空间：最近几期的开奖号码和统计特征
        self.state_window = 10
        self.state_dim = self._calculate_state_dim()
        
        # 动作空间：预测的号码组合
        self.action_dim = self._calculate_action_dim()
        
        # 奖励机制
        self.reward_weights = {
            'exact_match': 100,      # 完全匹配
            'partial_match': 10,     # 部分匹配
            'pattern_match': 5,      # 模式匹配
            'wrong_prediction': -1   # 错误预测
        }
    
    def _calculate_state_dim(self) -> int:
        """计算状态维度"""
        # 基础特征：最近几期的统计信息
        basic_features = 15  # 均值、方差、最大值、最小值等
        # 号码分布特征
        distribution_features = 10
        # 趋势特征
        trend_features = 5
        
        return basic_features + distribution_features + trend_features
    
    def _calculate_action_dim(self) -> int:
        """计算动作维度"""
        if self.lottery_type == 'shuangseqiu':
            return 33 + 16  # 红球33个 + 蓝球16个
        elif self.lottery_type == 'daletou':
            return 35 + 12  # 前区35个 + 后区12个
        elif self.lottery_type == 'fucai3d':
            return 10 * 3   # 3个位置，每个位置10个数字
        else:
            return 50
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_step = self.state_window
        return self._get_state()
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态"""
        if self.current_step < self.state_window:
            # 如果步数不够，用零填充
            state = np.zeros(self.state_dim)
        else:
            # 提取最近几期的数据
            recent_data = self.historical_data.iloc[
                self.current_step - self.state_window:self.current_step
            ]
            
            state = self._extract_features(recent_data)
        
        return state
    
    def _extract_features(self, data: pd.DataFrame) -> np.ndarray:
        """从数据中提取特征"""
        features = []
        
        # 收集所有号码
        all_numbers = []
        for _, row in data.iterrows():
            numbers = row['numbers']
            if isinstance(numbers, str):
                numbers = eval(numbers)
            all_numbers.extend(numbers)
        
        if not all_numbers:
            return np.zeros(self.state_dim)
        
        # 基础统计特征
        features.extend([
            np.mean(all_numbers),
            np.std(all_numbers),
            np.max(all_numbers),
            np.min(all_numbers),
            len(set(all_numbers)),
            np.median(all_numbers),
            np.percentile(all_numbers, 25),
            np.percentile(all_numbers, 75),
            sum(1 for x in all_numbers if x % 2 == 1) / len(all_numbers),  # 奇数比例
            sum(1 for x in all_numbers if x % 2 == 0) / len(all_numbers),  # 偶数比例
        ])
        
        # 数字分布特征
        max_num = max(all_numbers) if all_numbers else 50
        for i in range(0, max_num, max_num // 10):
            count = sum(1 for x in all_numbers if i <= x < i + max_num // 10)
            features.append(count / len(all_numbers))
        
        # 趋势特征
        if len(data) >= 2:
            recent_avg = np.mean([np.mean(eval(row['numbers']) if isinstance(row['numbers'], str) else row['numbers']) 
                                 for _, row in data.tail(3).iterrows()])
            earlier_avg = np.mean([np.mean(eval(row['numbers']) if isinstance(row['numbers'], str) else row['numbers']) 
                                  for _, row in data.head(3).iterrows()])
            features.extend([
                recent_avg - earlier_avg,  # 趋势方向
                abs(recent_avg - earlier_avg),  # 趋势强度
                recent_avg,
                earlier_avg,
                (recent_avg + earlier_avg) / 2
            ])
        else:
            features.extend([0, 0, 0, 0, 0])
        
        # 确保特征维度正确
        while len(features) < self.state_dim:
            features.append(0)
        
        return np.array(features[:self.state_dim])
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行动作"""
        if self.current_step >= self.max_steps:
            return self._get_state(), 0, True, {}
        
        # 获取真实的开奖号码
        actual_numbers = self.historical_data.iloc[self.current_step]['numbers']
        if isinstance(actual_numbers, str):
            actual_numbers = eval(actual_numbers)
        
        # 计算奖励
        reward = self._calculate_reward(action, actual_numbers)
        
        # 移动到下一步
        self.current_step += 1
        
        # 检查是否结束
        done = self.current_step >= self.max_steps
        
        # 获取新状态
        next_state = self._get_state()
        
        info = {
            'actual_numbers': actual_numbers,
            'predicted_numbers': self._action_to_numbers(action),
            'reward_breakdown': self._get_reward_breakdown(action, actual_numbers)
        }
        
        return next_state, reward, done, info
    
    def _calculate_reward(self, action: np.ndarray, actual_numbers: List[int]) -> float:
        """计算奖励"""
        predicted_numbers = self._action_to_numbers(action)
        
        # 完全匹配奖励
        if set(predicted_numbers) == set(actual_numbers):
            return self.reward_weights['exact_match']
        
        # 部分匹配奖励
        matches = len(set(predicted_numbers) & set(actual_numbers))
        partial_reward = matches * self.reward_weights['partial_match']
        
        # 模式匹配奖励（基于统计特征的相似性）
        pattern_reward = self._calculate_pattern_reward(predicted_numbers, actual_numbers)
        
        # 错误预测惩罚
        wrong_penalty = self.reward_weights['wrong_prediction']
        
        total_reward = partial_reward + pattern_reward + wrong_penalty
        
        return total_reward
    
    def _calculate_pattern_reward(self, predicted: List[int], actual: List[int]) -> float:
        """计算模式匹配奖励"""
        try:
            # 比较统计特征的相似性
            pred_mean = np.mean(predicted)
            actual_mean = np.mean(actual)
            
            pred_std = np.std(predicted)
            actual_std = np.std(actual)
            
            # 计算相似度
            mean_similarity = 1 - abs(pred_mean - actual_mean) / max(pred_mean, actual_mean, 1)
            std_similarity = 1 - abs(pred_std - actual_std) / max(pred_std, actual_std, 1)
            
            pattern_score = (mean_similarity + std_similarity) / 2
            
            return pattern_score * self.reward_weights['pattern_match']
            
        except:
            return 0
    
    def _action_to_numbers(self, action: np.ndarray) -> List[int]:
        """将动作转换为号码"""
        # 简化：选择概率最高的几个号码
        if self.lottery_type == 'shuangseqiu':
            # 红球区域
            red_probs = action[:33]
            red_indices = np.argsort(red_probs)[-6:]  # 选择6个红球
            red_balls = [i + 1 for i in red_indices]
            
            # 蓝球区域
            blue_probs = action[33:49]
            blue_index = np.argmax(blue_probs)
            blue_ball = blue_index + 1
            
            return sorted(red_balls) + [blue_ball]
            
        elif self.lottery_type == 'fucai3d':
            # 3个位置，每个位置选择概率最高的数字
            numbers = []
            for i in range(3):
                pos_probs = action[i*10:(i+1)*10]
                pos_number = np.argmax(pos_probs)
                numbers.append(pos_number)
            return numbers
            
        else:
            # 通用处理
            indices = np.argsort(action)[-7:]  # 选择7个最高概率的
            return [i + 1 for i in indices]
    
    def _get_reward_breakdown(self, action: np.ndarray, actual_numbers: List[int]) -> Dict:
        """获取奖励分解"""
        predicted_numbers = self._action_to_numbers(action)
        matches = len(set(predicted_numbers) & set(actual_numbers))
        
        return {
            'matches': matches,
            'predicted': predicted_numbers,
            'actual': actual_numbers,
            'exact_match': set(predicted_numbers) == set(actual_numbers)
        }


class DQNAgent:
    """Deep Q-Network智能体"""
    
    def __init__(self, state_dim: int, action_dim: int, learning_rate: float = 0.001):
        """
        初始化DQN智能体
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            learning_rate: 学习率
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.learning_rate = learning_rate
        
        # 超参数
        self.epsilon = 1.0  # 探索率
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.gamma = 0.95  # 折扣因子
        self.batch_size = 32
        
        # 经验回放缓冲区
        self.memory = deque(maxlen=10000)
        
        # 构建网络
        self.q_network = self._build_network()
        self.target_network = self._build_network()
        self.update_target_network()
    
    def _build_network(self) -> Model:
        """构建Q网络"""
        inputs = Input(shape=(self.state_dim,))
        
        x = Dense(256, activation='relu')(inputs)
        x = Dense(256, activation='relu')(x)
        x = Dense(128, activation='relu')(x)
        
        outputs = Dense(self.action_dim, activation='linear')(x)
        
        model = Model(inputs=inputs, outputs=outputs)
        model.compile(optimizer=Adam(learning_rate=self.learning_rate), loss='mse')
        
        return model
    
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.set_weights(self.q_network.get_weights())
    
    def remember(self, state: np.ndarray, action: np.ndarray, reward: float, 
                 next_state: np.ndarray, done: bool):
        """存储经验"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state: np.ndarray) -> np.ndarray:
        """选择动作"""
        if np.random.random() <= self.epsilon:
            # 探索：随机动作
            action = np.random.random(self.action_dim)
        else:
            # 利用：基于Q值选择
            q_values = self.q_network.predict(state.reshape(1, -1), verbose=0)[0]
            action = np.zeros(self.action_dim)
            
            # 将Q值转换为概率分布
            q_values_exp = np.exp(q_values - np.max(q_values))
            action = q_values_exp / np.sum(q_values_exp)
        
        return action
    
    def replay(self):
        """经验回放训练"""
        if len(self.memory) < self.batch_size:
            return
        
        batch = random.sample(self.memory, self.batch_size)
        states = np.array([e[0] for e in batch])
        actions = np.array([e[1] for e in batch])
        rewards = np.array([e[2] for e in batch])
        next_states = np.array([e[3] for e in batch])
        dones = np.array([e[4] for e in batch])
        
        # 计算目标Q值
        target_q_values = self.target_network.predict(next_states, verbose=0)
        max_target_q_values = np.max(target_q_values, axis=1)
        
        targets = rewards + (self.gamma * max_target_q_values * (1 - dones))
        
        # 获取当前Q值
        current_q_values = self.q_network.predict(states, verbose=0)
        
        # 更新Q值（简化处理）
        for i in range(self.batch_size):
            # 这里简化了动作到Q值的映射
            action_index = np.argmax(actions[i])
            current_q_values[i][action_index] = targets[i]
        
        # 训练网络
        self.q_network.fit(states, current_q_values, epochs=1, verbose=0)
        
        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay


class ReinforcementLearningPredictor:
    """强化学习预测器"""
    
    def __init__(self, config_manager, logger: logging.Logger, lottery_type: str):
        """
        初始化强化学习预测器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
            lottery_type: 彩票类型
        """
        self.config_manager = config_manager
        self.logger = logger
        self.lottery_type = lottery_type
        
        # 强化学习配置
        self.rl_config = config_manager.get('deep_learning', {}).get('reinforcement_learning', {})
        
        # 训练参数
        self.episodes = self.rl_config.get('episodes', 1000)
        self.learning_rate = self.rl_config.get('learning_rate', 0.001)
        self.update_target_freq = self.rl_config.get('update_target_freq', 100)
        
        # 组件
        self.env = None
        self.agent = None
        
        # 训练历史
        self.training_history = []
        
        # 模型保存路径
        self.model_save_path = config_manager.get('learning.model_save_path', 'data/models')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.info(f"强化学习预测器初始化完成: {lottery_type}")
    
    def train(self, historical_data: pd.DataFrame) -> Dict[str, Any]:
        """
        训练强化学习模型
        
        Args:
            historical_data: 历史数据
            
        Returns:
            Dict: 训练结果
        """
        try:
            self.logger.info(f"开始强化学习训练: {self.lottery_type}")
            
            # 创建环境
            self.env = LotteryEnvironment(historical_data, self.lottery_type)
            
            # 创建智能体
            self.agent = DQNAgent(
                state_dim=self.env.state_dim,
                action_dim=self.env.action_dim,
                learning_rate=self.learning_rate
            )
            
            # 训练统计
            episode_rewards = []
            episode_accuracies = []
            
            for episode in range(self.episodes):
                state = self.env.reset()
                total_reward = 0
                correct_predictions = 0
                total_predictions = 0
                
                while True:
                    # 选择动作
                    action = self.agent.act(state)
                    
                    # 执行动作
                    next_state, reward, done, info = self.env.step(action)
                    
                    # 存储经验
                    self.agent.remember(state, action, reward, next_state, done)
                    
                    # 更新统计
                    total_reward += reward
                    total_predictions += 1
                    if info.get('reward_breakdown', {}).get('matches', 0) > 0:
                        correct_predictions += 1
                    
                    state = next_state
                    
                    if done:
                        break
                
                # 经验回放训练
                if len(self.agent.memory) > self.agent.batch_size:
                    self.agent.replay()
                
                # 更新目标网络
                if episode % self.update_target_freq == 0:
                    self.agent.update_target_network()
                
                # 记录统计
                episode_rewards.append(total_reward)
                accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
                episode_accuracies.append(accuracy)
                
                # 打印进度
                if episode % 100 == 0:
                    avg_reward = np.mean(episode_rewards[-100:])
                    avg_accuracy = np.mean(episode_accuracies[-100:])
                    self.logger.info(f"Episode {episode}: 平均奖励={avg_reward:.2f}, 平均准确率={avg_accuracy:.4f}, Epsilon={self.agent.epsilon:.4f}")
            
            # 保存训练结果
            training_result = {
                'timestamp': datetime.now(),
                'lottery_type': self.lottery_type,
                'model_type': 'Reinforcement Learning (DQN)',
                'episodes': self.episodes,
                'final_epsilon': self.agent.epsilon,
                'average_reward': np.mean(episode_rewards[-100:]),
                'average_accuracy': np.mean(episode_accuracies[-100:]),
                'total_samples': len(historical_data),
                'model_params': {
                    'state_dim': self.env.state_dim,
                    'action_dim': self.env.action_dim,
                    'learning_rate': self.learning_rate,
                    'gamma': self.agent.gamma,
                    'epsilon_decay': self.agent.epsilon_decay
                }
            }
            
            self.training_history.append(training_result)
            
            # 保存模型
            self._save_model_components()
            
            self.logger.info(f"强化学习训练完成: 平均准确率={training_result['average_accuracy']:.4f}")
            return training_result
            
        except Exception as e:
            self.logger.error(f"强化学习训练失败: {e}")
            raise
    
    def predict(self, recent_data: pd.DataFrame, num_predictions: int = 1) -> Dict[str, Any]:
        """
        使用强化学习模型进行预测
        
        Args:
            recent_data: 最近的数据
            num_predictions: 预测数量
            
        Returns:
            Dict: 预测结果
        """
        try:
            if self.agent is None:
                raise ValueError("模型未训练或加载")
            
            # 创建临时环境用于预测
            temp_env = LotteryEnvironment(recent_data, self.lottery_type)
            state = temp_env._get_state()
            
            predictions = []
            for _ in range(num_predictions):
                # 使用训练好的智能体进行预测（不探索）
                old_epsilon = self.agent.epsilon
                self.agent.epsilon = 0  # 关闭探索
                
                action = self.agent.act(state)
                predicted_numbers = temp_env._action_to_numbers(action)
                
                # 计算置信度（基于Q值）
                q_values = self.agent.q_network.predict(state.reshape(1, -1), verbose=0)[0]
                confidence = float(np.max(tf.nn.softmax(q_values)))
                
                predictions.append({
                    'predicted_numbers': predicted_numbers,
                    'confidence': confidence,
                    'action_distribution': action.tolist()
                })
                
                self.agent.epsilon = old_epsilon
            
            result = {
                'lottery_type': self.lottery_type,
                'prediction_date': datetime.now(),
                'model_type': 'Reinforcement Learning (DQN)',
                'predictions': predictions,
                'model_accuracy': self.training_history[-1]['average_accuracy'] if self.training_history else 0.0
            }
            
            self.logger.info(f"强化学习预测完成: {len(predictions)} 个预测")
            return result
            
        except Exception as e:
            self.logger.error(f"强化学习预测失败: {e}")
            return {}
    
    def _save_model_components(self):
        """保存模型组件"""
        try:
            # 保存Q网络
            q_network_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_q_network.h5')
            self.agent.q_network.save(q_network_path)
            
            # 保存目标网络
            target_network_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_target_network.h5')
            self.agent.target_network.save(target_network_path)
            
            # 保存智能体参数
            agent_params = {
                'epsilon': self.agent.epsilon,
                'epsilon_min': self.agent.epsilon_min,
                'epsilon_decay': self.agent.epsilon_decay,
                'gamma': self.agent.gamma,
                'batch_size': self.agent.batch_size,
                'state_dim': self.agent.state_dim,
                'action_dim': self.agent.action_dim
            }
            
            params_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_params.pkl')
            with open(params_path, 'wb') as f:
                pickle.dump(agent_params, f)
            
            # 保存训练历史
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_history.pkl')
            with open(history_path, 'wb') as f:
                pickle.dump(self.training_history, f)
            
            self.logger.info("强化学习模型组件保存完成")
            
        except Exception as e:
            self.logger.error(f"保存强化学习模型组件失败: {e}")
    
    def load_model_components(self) -> bool:
        """加载模型组件"""
        try:
            # 加载智能体参数
            params_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_params.pkl')
            if not os.path.exists(params_path):
                return False
            
            with open(params_path, 'rb') as f:
                agent_params = pickle.load(f)
            
            # 创建智能体
            self.agent = DQNAgent(
                state_dim=agent_params['state_dim'],
                action_dim=agent_params['action_dim'],
                learning_rate=self.learning_rate
            )
            
            # 恢复参数
            self.agent.epsilon = agent_params['epsilon']
            self.agent.epsilon_min = agent_params['epsilon_min']
            self.agent.epsilon_decay = agent_params['epsilon_decay']
            self.agent.gamma = agent_params['gamma']
            self.agent.batch_size = agent_params['batch_size']
            
            # 加载网络权重
            q_network_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_q_network.h5')
            target_network_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_target_network.h5')
            
            if os.path.exists(q_network_path):
                self.agent.q_network = tf.keras.models.load_model(q_network_path)
            
            if os.path.exists(target_network_path):
                self.agent.target_network = tf.keras.models.load_model(target_network_path)
            
            # 加载训练历史
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_rl_history.pkl')
            if os.path.exists(history_path):
                with open(history_path, 'rb') as f:
                    self.training_history = pickle.load(f)
            
            self.logger.info("强化学习模型组件加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载强化学习模型组件失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.training_history:
            return {}
        
        latest_training = self.training_history[-1]
        
        return {
            'model_type': 'Reinforcement Learning (DQN)',
            'lottery_type': self.lottery_type,
            'last_training': latest_training['timestamp'],
            'average_accuracy': latest_training['average_accuracy'],
            'average_reward': latest_training['average_reward'],
            'episodes_trained': latest_training['episodes'],
            'model_parameters': latest_training['model_params'],
            'total_trainings': len(self.training_history)
        }
