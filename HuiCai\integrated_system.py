#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai集成系统
Web界面 + 仪表盘 + 系统主入口一体化

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
import json
import random
import time
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
import logging

# FastAPI和Web相关
from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 系统组件导入
try:
    from src.management_layer.config_manager import ConfigManager
    from src.management_layer.log_manager import LogManager
    from src.crawler_system.crawler_manager import CrawlerManager
    from src.database.local_database_manager import LocalDatabaseManager
    from src.database.data_access_layer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ataAcc<PERSON>, DaletouDataAccess
except ImportError:
    # 如果导入失败，创建简化版本
    class ConfigManager:
        def get(self, key, default=None): return default
        def set(self, key, value): pass
        def get_config(self): return {}

    class LogManager:
        def __init__(self, config): pass
        def get_logger(self, name): return logging.getLogger(name)

    class CrawlerManager:
        def __init__(self): pass
        def get_crawler_status(self): return {}
        def manual_crawl(self, lottery_type, count): return {'status': 'error', 'error': '爬虫模块未安装'}
        def close(self): pass

    class LocalDatabaseManager:
        def __init__(self): pass
        def get_database_info(self): return {}
        def close_all_connections(self): pass

# 创建FastAPI应用
app = FastAPI(
    title="HuiCai 慧彩智能体系统",
    description="AI彩票分析预测系统 - 集成版",
    version="4.0.0"
)

# 全局变量
config_manager = ConfigManager()
log_manager = LogManager(config_manager)
logger = log_manager.get_logger("IntegratedSystem")

# 初始化爬虫管理器和数据库管理器
try:
    crawler_manager = CrawlerManager()
    db_manager = LocalDatabaseManager()
    logger.info("爬虫管理器和数据库管理器初始化成功")
except Exception as e:
    logger.error(f"初始化爬虫管理器或数据库管理器失败: {e}")
    crawler_manager = None
    db_manager = None

# 模拟系统状态
system_status = {
    'startup_time': datetime.now(),
    'prediction_count': 0,
    'active_models': 2,
    'system_health': 'excellent',
    'cpu_usage': 0.0,
    'memory_usage': 0.0,
    'disk_usage': 0.0
}

# 预测历史
prediction_history = []

# 模拟数据
lottery_types = [
    {'value': 'shuangseqiu', 'label': '双色球', 'desc': '6红球+1蓝球'},
    {'value': 'daletou', 'label': '大乐透', 'desc': '5前区+2后区'}
]

prediction_methods = [
    {'value': 'comprehensive', 'label': '综合预测', 'desc': '多算法融合'},
    {'value': 'traditional', 'label': '传统ML', 'desc': '机器学习算法'},
    {'value': 'chinese', 'label': '中国算法', 'desc': '五行八卦算法'},
    {'value': 'deeplearning', 'label': '深度学习', 'desc': 'LSTM神经网络'}
]

# 模拟模型数据
models_data = [
    {'name': 'LSTM神经网络', 'lottery': '双色球', 'accuracy': '78.5%', 'status': '运行中', 'last_trained': '2小时前'},
    {'name': '随机森林', 'lottery': '大乐透', 'accuracy': '72.3%', 'status': '运行中', 'last_trained': '1小时前'}
]

# 模拟开奖数据
recent_draws = [
    {'date': '2025-01-15', 'lottery': '双色球', 'numbers': '03,08,16,21,28,33+12', 'status': '已验证'},
    {'date': '2025-01-14', 'lottery': '大乐透', 'numbers': '05,12,19,26,31+03,08', 'status': '已验证'}
]

def update_system_metrics():
    """更新系统指标"""
    import psutil
    try:
        system_status['cpu_usage'] = psutil.cpu_percent()
        system_status['memory_usage'] = psutil.virtual_memory().percent
        system_status['disk_usage'] = psutil.disk_usage('.').percent
    except:
        system_status['cpu_usage'] = random.uniform(10, 30)
        system_status['memory_usage'] = random.uniform(40, 60)
        system_status['disk_usage'] = random.uniform(20, 40)

def generate_prediction(lottery_type: str, method: str) -> Dict[str, Any]:
    """生成预测结果"""
    global system_status, prediction_history
    
    # 更新预测计数
    system_status['prediction_count'] += 1
    
    # 生成预测号码
    if lottery_type == 'shuangseqiu':
        red_balls = sorted(random.sample(range(1, 34), 6))
        blue_ball = random.randint(1, 16)
        numbers = red_balls + [blue_ball]
    elif lottery_type == 'daletou':
        front_balls = sorted(random.sample(range(1, 36), 5))
        back_balls = sorted(random.sample(range(1, 13), 2))
        numbers = front_balls + back_balls
    else:
        numbers = []
    
    # 生成置信度
    confidence = random.uniform(0.6, 0.9)
    
    # 创建预测结果
    result = {
        'success': True,
        'lottery_type': lottery_type,
        'method': method,
        'numbers': numbers,
        'confidence': confidence,
        'timestamp': datetime.now().isoformat(),
        'details': {
            'algorithm': f'{method}算法',
            'analysis': f'基于历史数据分析，{method}算法预测结果',
            'recommendation': '建议结合多种方法综合判断，理性投注'
        }
    }
    
    # 添加到历史记录
    prediction_history.insert(0, result)
    if len(prediction_history) > 50:
        prediction_history = prediction_history[:50]
    
    return result

# HTML模板
BASE_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            min-height: 100vh;
        }
        
        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .stat-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .number-ball {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            background: var(--warning-gradient);
        }
        
        .red-ball {
            background: var(--warning-gradient);
        }
        
        .blue-ball {
            background: var(--info-gradient);
        }
        
        .normal-ball {
            background: var(--success-gradient);
        }
        
        .btn-predict {
            background: var(--primary-gradient);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-predict:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
        }
        
        .nav-pills .nav-link {
            border-radius: 20px;
            margin: 0 5px;
        }
        
        .nav-pills .nav-link.active {
            background: var(--primary-gradient);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-running { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .stat-number { font-size: 2rem; }
            .number-ball { width: 40px; height: 40px; margin: 3px; }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>HuiCai 慧彩智能体
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>仪表盘</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/prediction"><i class="fas fa-magic me-1"></i>智能预测</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/data"><i class="fas fa-database me-1"></i>数据管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/models"><i class="fas fa-robot me-1"></i>模型管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/monitoring"><i class="fas fa-chart-line me-1"></i>系统监控</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-circle text-success me-1"></i>系统运行正常
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // 全局工具函数
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        function showLoading(btn) {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
        }
        
        function hideLoading(btn, originalText) {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
        
        function formatNumber(num) {
            return num.toString().padStart(2, '0');
        }
        
        // 定期更新系统状态
        setInterval(async function() {
            try {
                const response = await fetch('/api/system/status');
                const status = await response.json();
                updateSystemStatus(status);
            } catch (error) {
                console.error('更新系统状态失败:', error);
            }
        }, 30000); // 每30秒更新一次
        
        function updateSystemStatus(status) {
            // 更新状态指示器
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                if (status.system_health === 'excellent') {
                    indicator.className = 'status-indicator status-running';
                } else if (status.system_health === 'good') {
                    indicator.className = 'status-indicator status-warning';
                } else {
                    indicator.className = 'status-indicator status-error';
                }
            });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
"""

# ==================== 路由定义 ====================

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """仪表盘主页"""
    update_system_metrics()

    # 计算运行时间
    uptime = datetime.now() - system_status['startup_time']
    uptime_str = f"{uptime.days}天 {uptime.seconds//3600}小时 {(uptime.seconds%3600)//60}分钟"

    dashboard_html = BASE_TEMPLATE.replace("{% block content %}", f"""
    <!-- 系统状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{system_status['prediction_count']}</div>
                <div class="stat-label">
                    <i class="fas fa-magic me-1"></i>预测次数
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{system_status['active_models']}</div>
                <div class="stat-label">
                    <i class="fas fa-robot me-1"></i>活跃模型
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{system_status['cpu_usage']:.1f}%</div>
                <div class="stat-label">
                    <i class="fas fa-microchip me-1"></i>CPU使用率
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{system_status['memory_usage']:.1f}%</div>
                <div class="stat-label">
                    <i class="fas fa-memory me-1"></i>内存使用率
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 快速预测 -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-magic text-primary me-2"></i>
                        快速预测
                    </h5>
                </div>
                <div class="card-body">
                    <form id="quickPredictForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">彩票类型</label>
                                <select class="form-select" name="lottery_type" required>
                                    <option value="">请选择彩票类型</option>
                                    <option value="shuangseqiu">双色球</option>
                                    <option value="daletou">大乐透</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">预测方法</label>
                                <select class="form-select" name="method" required>
                                    <option value="">请选择预测方法</option>
                                    <option value="comprehensive">综合预测</option>
                                    <option value="traditional">传统ML</option>
                                    <option value="chinese">中国算法</option>
                                    <option value="deeplearning">深度学习</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-predict text-white">
                            <i class="fas fa-magic me-2"></i>开始预测
                        </button>
                    </form>

                    <!-- 预测结果显示区域 -->
                    <div id="predictionResult" class="mt-4" style="display: none;">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-star me-2"></i>预测结果</h6>
                            <div id="numbersDisplay" class="my-3"></div>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>置信度:</strong> <span id="confidenceValue"></span></small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>预测时间:</strong> <span id="predictionTime"></span></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近预测历史 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        最近预测
                    </h6>
                </div>
                <div class="card-body">
                    <div id="recentPredictions">
                        <p class="text-muted text-center">暂无预测记录</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="col-lg-4">
            <!-- 系统状态 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-server text-success me-2"></i>
                        系统状态
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>运行时间</span>
                            <span class="text-muted">{uptime_str}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>CPU使用率</span>
                            <span>{system_status['cpu_usage']:.1f}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {system_status['cpu_usage']:.1f}%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>内存使用率</span>
                            <span>{system_status['memory_usage']:.1f}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: {system_status['memory_usage']:.1f}%"></div>
                        </div>
                    </div>
                    <div class="mb-0">
                        <div class="d-flex justify-content-between mb-1">
                            <span>磁盘使用率</span>
                            <span>{system_status['disk_usage']:.1f}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: {system_status['disk_usage']:.1f}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型状态 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-robot text-primary me-2"></i>
                        模型状态
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>LSTM神经网络</strong>
                                <br><small class="text-muted">双色球预测</small>
                            </div>
                            <span class="badge bg-success">78.5%</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>随机森林</strong>
                                <br><small class="text-muted">大乐透预测</small>
                            </div>
                            <span class="badge bg-info">72.3%</span>
                        </div>

                    </div>
                </div>
            </div>

            <!-- 最新开奖 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-trophy text-warning me-2"></i>
                        最新开奖
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <strong>双色球</strong>
                                <small class="text-muted">2025-01-15</small>
                            </div>
                            <div class="mt-1">
                                <code class="bg-light p-1 rounded">03,08,16,21,28,33+12</code>
                            </div>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <strong>大乐透</strong>
                                <small class="text-muted">2025-01-14</small>
                            </div>
                            <div class="mt-1">
                                <code class="bg-light p-1 rounded">05,12,19,26,31+03,08</code>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    """)

    # 添加JavaScript代码
    dashboard_html += """
    <script>
        // 工具函数
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showLoading(btn) {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
        }

        function hideLoading(btn, originalText) {
            btn.disabled = false;
            btn.innerHTML = originalText;
        }

        function formatNumber(num) {
            return num.toString().padStart(2, '0');
        }

        // 快速预测表单处理
        document.getElementById('quickPredictForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const lotteryType = formData.get('lottery_type');
            const method = formData.get('method');

            if (!lotteryType || !method) {
                showAlert('请选择彩票类型和预测方法', 'warning');
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            showLoading(submitBtn);

            try {
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        lottery_type: lotteryType,
                        method: method
                    })
                });

                const result = await response.json();

                if (result.success) {
                    displayPredictionResult(result);
                    showAlert('预测完成！', 'success');
                    loadRecentPredictions();
                } else {
                    showAlert('预测失败：' + (result.error || '未知错误'), 'danger');
                }
            } catch (error) {
                showAlert('预测请求失败：' + error.message, 'danger');
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        function displayPredictionResult(result) {
            const resultDiv = document.getElementById('predictionResult');
            const numbersDisplay = document.getElementById('numbersDisplay');
            const confidenceValue = document.getElementById('confidenceValue');
            const predictionTime = document.getElementById('predictionTime');

            // 显示号码
            let numbersHtml = '';
            if (result.lottery_type === 'shuangseqiu') {
                // 红球
                for (let i = 0; i < 6; i++) {
                    numbersHtml += `<div class="number-ball red-ball">${formatNumber(result.numbers[i])}</div>`;
                }
                // 蓝球
                numbersHtml += `<div class="number-ball blue-ball">${formatNumber(result.numbers[6])}</div>`;
            } else if (result.lottery_type === 'daletou') {
                // 前区
                for (let i = 0; i < 5; i++) {
                    numbersHtml += `<div class="number-ball red-ball">${formatNumber(result.numbers[i])}</div>`;
                }
                // 后区
                for (let i = 5; i < 7; i++) {
                    numbersHtml += `<div class="number-ball blue-ball">${formatNumber(result.numbers[i])}</div>`;
                }
            }

            numbersDisplay.innerHTML = numbersHtml;
            confidenceValue.textContent = (result.confidence * 100).toFixed(1) + '%';
            predictionTime.textContent = new Date(result.timestamp).toLocaleString();

            resultDiv.style.display = 'block';
        }

        async function loadRecentPredictions() {
            try {
                const response = await fetch('/api/predictions/recent');
                const predictions = await response.json();

                const container = document.getElementById('recentPredictions');

                if (predictions.length === 0) {
                    container.innerHTML = '<p class="text-muted text-center">暂无预测记录</p>';
                    return;
                }

                let html = '';
                predictions.slice(0, 5).forEach(pred => {
                    const date = new Date(pred.timestamp).toLocaleString();
                    const confidence = (pred.confidence * 100).toFixed(1);

                    html += `
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <small><strong>${getLotteryName(pred.lottery_type)}</strong></small>
                                <small class="text-muted">${date}</small>
                            </div>
                            <div class="mt-1">
                                <small>置信度: ${confidence}% | 方法: ${getMethodName(pred.method)}</small>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            } catch (error) {
                console.error('加载预测历史失败:', error);
            }
        }

        function getLotteryName(type) {
            const names = {
                'shuangseqiu': '双色球',
                'daletou': '大乐透'
            };
            return names[type] || type;
        }

        function getMethodName(method) {
            const names = {
                'comprehensive': '综合预测',
                'traditional': '传统ML',
                'chinese': '中国算法',
                'deeplearning': '深度学习'
            };
            return names[method] || method;
        }

        // 页面加载时加载预测历史
        document.addEventListener('DOMContentLoaded', function() {
            loadRecentPredictions();
        });
    </script>
    </body>
    </html>"""

    return HTMLResponse(content=dashboard_html)

# ==================== API路由 ====================

@app.post("/api/predict")
async def api_predict(request: Request):
    """预测API"""
    try:
        data = await request.json()
        lottery_type = data.get('lottery_type')
        method = data.get('method')

        if not lottery_type or not method:
            raise HTTPException(status_code=400, detail="缺少必需参数")

        # 生成预测
        result = generate_prediction(lottery_type, method)
        return result

    except Exception as e:
        logger.error(f"预测API错误: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/predictions/recent")
async def api_recent_predictions():
    """获取最近预测"""
    return prediction_history[:10]  # 返回最近10条

@app.get("/api/system/status")
async def api_system_status():
    """系统状态API"""
    update_system_metrics()
    uptime = datetime.now() - system_status['startup_time']

    status = {
        **system_status,
        'uptime_seconds': uptime.total_seconds(),
        'uptime_string': f"{uptime.days}天 {uptime.seconds//3600}小时 {(uptime.seconds%3600)//60}分钟"
    }

    # 添加爬虫状态
    if crawler_manager:
        try:
            crawler_status = crawler_manager.get_crawler_status()
            status['crawler_status'] = crawler_status
        except Exception as e:
            status['crawler_error'] = str(e)

    # 添加数据库状态
    if db_manager:
        try:
            db_info = db_manager.get_database_info()
            status['database_info'] = db_info
        except Exception as e:
            status['database_error'] = str(e)

    return status

@app.post("/api/crawler/manual")
async def api_manual_crawl(request: Request):
    """手动触发爬虫"""
    try:
        data = await request.json()
        lottery_type = data.get('lottery_type', 'shuangseqiu')
        count = data.get('count', 5)

        if not crawler_manager:
            return {"success": False, "error": "爬虫管理器未初始化"}

        result = crawler_manager.manual_crawl(lottery_type, count)
        return {"success": True, "result": result}

    except Exception as e:
        logger.error(f"手动爬虫API错误: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/crawler/status")
async def api_crawler_status():
    """获取爬虫状态"""
    if not crawler_manager:
        return {"error": "爬虫管理器未初始化"}

    try:
        return crawler_manager.get_crawler_status()
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/database/info")
async def api_database_info():
    """获取数据库信息"""
    if not db_manager:
        return {"error": "数据库管理器未初始化"}

    try:
        return db_manager.get_database_info()
    except Exception as e:
        return {"error": str(e)}

@app.get("/monitoring", response_class=HTMLResponse)
async def monitoring_page():
    """系统监控页面"""
    update_system_metrics()

    monitoring_html = BASE_TEMPLATE.replace("{% block content %}", f"""
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-chart-line text-primary me-2"></i>系统监控</h2>
        </div>
    </div>

    <!-- 实时指标 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">CPU使用率</h5>
                    <h2 class="text-primary">{system_status['cpu_usage']:.1f}%</h2>
                    <div class="progress">
                        <div class="progress-bar" style="width: {system_status['cpu_usage']:.1f}%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">内存使用率</h5>
                    <h2 class="text-info">{system_status['memory_usage']:.1f}%</h2>
                    <div class="progress">
                        <div class="progress-bar bg-info" style="width: {system_status['memory_usage']:.1f}%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">磁盘使用率</h5>
                    <h2 class="text-warning">{system_status['disk_usage']:.1f}%</h2>
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: {system_status['disk_usage']:.1f}%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">系统健康</h5>
                    <h2 class="text-success">优秀</h2>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 95%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">CPU和内存使用趋势</h6>
                </div>
                <div class="card-body">
                    <canvas id="systemChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">预测活动统计</h6>
                </div>
                <div class="card-body">
                    <canvas id="predictionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    """).replace("{% endblock %}", "").replace("{% block extra_js %}", """
    <script>
        // 系统监控图表
        const systemCtx = document.getElementById('systemChart').getContext('2d');
        const systemChart = new Chart(systemCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU使用率',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }, {
                    label: '内存使用率',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 预测统计图表
        const predictionCtx = document.getElementById('predictionChart').getContext('2d');
        const predictionChart = new Chart(predictionCtx, {
            type: 'doughnut',
            data: {
                labels: ['双色球', '大乐透'],
                datasets: [{
                    data: [60, 40],
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)'
                    ]
                }]
            },
            options: {
                responsive: true
            }
        });

        // 定期更新图表数据
        setInterval(async function() {
            try {
                const response = await fetch('/api/system/status');
                const status = await response.json();

                const now = new Date().toLocaleTimeString();

                // 更新系统图表
                systemChart.data.labels.push(now);
                systemChart.data.datasets[0].data.push(status.cpu_usage);
                systemChart.data.datasets[1].data.push(status.memory_usage);

                // 保持最近20个数据点
                if (systemChart.data.labels.length > 20) {
                    systemChart.data.labels.shift();
                    systemChart.data.datasets[0].data.shift();
                    systemChart.data.datasets[1].data.shift();
                }

                systemChart.update();

            } catch (error) {
                console.error('更新图表数据失败:', error);
            }
        }, 5000); // 每5秒更新一次
    </script>
    """).replace("{% endblock %}", "")

    return HTMLResponse(content=monitoring_html)

@app.get("/models", response_class=HTMLResponse)
async def models_page():
    """模型管理页面"""
    models_html = BASE_TEMPLATE.replace("{% block content %}", """
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-robot text-primary me-2"></i>模型管理</h2>
        </div>
    </div>

    <!-- 模型列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">AI模型列表</h6>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>训练新模型
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>模型名称</th>
                                    <th>彩票类型</th>
                                    <th>算法类型</th>
                                    <th>准确率</th>
                                    <th>状态</th>
                                    <th>最后训练</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>LSTM神经网络</strong></td>
                                    <td><span class="badge bg-primary">双色球</span></td>
                                    <td>深度学习</td>
                                    <td><span class="text-success">78.5%</span></td>
                                    <td><span class="badge bg-success">运行中</span></td>
                                    <td>2小时前</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>随机森林</strong></td>
                                    <td><span class="badge bg-info">大乐透</span></td>
                                    <td>机器学习</td>
                                    <td><span class="text-success">72.3%</span></td>
                                    <td><span class="badge bg-success">运行中</span></td>
                                    <td>1小时前</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning me-1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模型性能统计 -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">模型准确率对比</h6>
                </div>
                <div class="card-body">
                    <canvas id="accuracyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">训练进度</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>LSTM神经网络</span>
                            <span>100%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>随机森林</span>
                            <span>100%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 100%"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    """).replace("{% endblock %}", "").replace("{% block extra_js %}", """
    <script>
        // 准确率对比图表
        const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
        const accuracyChart = new Chart(accuracyCtx, {
            type: 'bar',
            data: {
                labels: ['LSTM神经网络', '随机森林'],
                datasets: [{
                    label: '准确率 (%)',
                    data: [78.5, 72.3],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(54, 162, 235, 0.8)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    </script>
    """).replace("{% endblock %}", "")

    return HTMLResponse(content=models_html)

# ==================== 系统启动器 ====================

class IntegratedSystemLauncher:
    """集成系统启动器"""

    def __init__(self):
        self.running = False
        self.startup_time = None

    async def start_system(self, host: str = "127.0.0.1", port: int = 8000):
        """启动集成系统"""
        try:
            self.startup_time = datetime.now()
            self.running = True

            print("🚀 启动HuiCai集成系统...")
            print(f"📅 启动时间: {self.startup_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*60)

            # 系统健康检查
            await self._health_check()

            # 启动Web服务器
            print(f"🌐 启动Web服务器: http://{host}:{port}")

            config = uvicorn.Config(
                app,
                host=host,
                port=port,
                log_level="info",
                access_log=False
            )
            server = uvicorn.Server(config)

            print("✅ 系统启动完成!")
            print(f"📱 访问地址: http://{host}:{port}")
            print(f"📚 API文档: http://{host}:{port}/docs")
            print(f"📊 监控面板: http://{host}:{port}/monitoring")
            print("="*60)

            await server.serve()

        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            raise

    async def _health_check(self):
        """系统健康检查"""
        print("🔍 执行系统健康检查...")

        checks = [
            ("配置管理", self._check_config),
            ("日志系统", self._check_logging),
            ("内存资源", self._check_memory),
            ("预测引擎", self._check_prediction_engine)
        ]

        for check_name, check_func in checks:
            try:
                result = check_func()
                if result:
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ⚠️ {check_name} - 警告")
            except Exception as e:
                print(f"  ❌ {check_name} - 失败: {e}")

        print("✅ 健康检查完成")

    def _check_config(self) -> bool:
        """检查配置"""
        try:
            return config_manager is not None
        except:
            return False

    def _check_logging(self) -> bool:
        """检查日志系统"""
        try:
            logger.info("日志系统测试")
            return True
        except:
            return False

    def _check_memory(self) -> bool:
        """检查内存"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return memory.available > 256 * 1024 * 1024  # 至少256MB可用内存
        except:
            return True  # 如果无法检查，假设正常

    def _check_prediction_engine(self) -> bool:
        """检查预测引擎"""
        try:
            # 测试预测功能
            test_result = generate_prediction('shuangseqiu', 'comprehensive')
            return test_result.get('success', False)
        except:
            return False

# ==================== 主入口 ====================

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='HuiCai慧彩智能体集成系统')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')

    args = parser.parse_args()

    print("🎯 HuiCai慧彩智能体集成系统 v4.0.0")
    print("🏆 Web界面 + 仪表盘 + 系统主入口一体化")
    print("="*60)

    # 创建并启动系统
    launcher = IntegratedSystemLauncher()

    try:
        await launcher.start_system(args.host, args.port)
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统正在关闭...")
    except Exception as e:
        print(f"❌ 系统运行异常: {e}")
    finally:
        print("✅ 系统已安全关闭")

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    # 运行主程序
    asyncio.run(main())
