{% extends "base.html" %}

{% block title %}智能预测 - HuiCai 慧彩智能体系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-magic text-primary me-2"></i>
            智能预测
        </h2>
    </div>
</div>

<div class="row">
    <!-- 预测控制面板 -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs text-info me-2"></i>
                    预测设置
                </h5>
            </div>
            <div class="card-body">
                <form id="predictionForm">
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-dice me-1"></i>彩票类型
                        </label>
                        <select class="form-select" name="lottery_type" required>
                            {% for lottery in lottery_types %}
                            <option value="{{ lottery.value }}">
                                {{ lottery.label }} - {{ lottery.desc }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-brain me-1"></i>预测方法
                        </label>
                        <select class="form-select" name="method" required>
                            {% for method in methods %}
                            <option value="{{ method.value }}">
                                {{ method.label }} - {{ method.desc }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>开始预测
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 预测说明 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-warning me-2"></i>
                    预测说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        基于历史数据和AI算法
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        多种算法融合预测
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        实时置信度评估
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        仅供参考，理性投注
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 预测结果区域 -->
    <div class="col-lg-8">
        <!-- 预测结果卡片 -->
        <div id="predictionResult" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star text-warning me-2"></i>
                    预测结果
                </h5>
            </div>
            <div class="card-body">
                <div id="resultContent">
                    <!-- 预测结果将在这里显示 -->
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loadingCard" class="card mb-4" style="display: none;">
            <div class="card-body text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>正在进行智能预测分析...</h5>
                <p class="text-muted">请稍候，AI正在处理数据</p>
            </div>
        </div>
        
        <!-- 历史预测记录 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history text-secondary me-2"></i>
                    历史预测记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>彩票类型</th>
                                <th>预测号码</th>
                                <th>方法</th>
                                <th>置信度</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="historyTable">
                            <tr>
                                <td>2025-01-15 10:30</td>
                                <td>双色球</td>
                                <td>
                                    <span class="number-ball red-ball">03</span>
                                    <span class="number-ball red-ball">08</span>
                                    <span class="number-ball red-ball">16</span>
                                    <span class="number-ball red-ball">21</span>
                                    <span class="number-ball red-ball">28</span>
                                    <span class="number-ball red-ball">33</span>
                                    <span class="number-ball blue-ball">12</span>
                                </td>
                                <td><span class="badge bg-primary">综合预测</span></td>
                                <td>78%</td>
                                <td><span class="badge bg-warning">待验证</span></td>
                            </tr>
                            <tr>
                                <td>2025-01-14 15:20</td>
                                <td>大乐透</td>
                                <td>
                                    <span class="number-ball red-ball">05</span>
                                    <span class="number-ball red-ball">12</span>
                                    <span class="number-ball red-ball">19</span>
                                    <span class="number-ball red-ball">26</span>
                                    <span class="number-ball red-ball">31</span>
                                    <span class="number-ball blue-ball">03</span>
                                    <span class="number-ball blue-ball">08</span>
                                </td>
                                <td><span class="badge bg-info">深度学习</span></td>
                                <td>72%</td>
                                <td><span class="badge bg-success">已验证</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 预测表单提交
    document.getElementById('predictionForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // 显示加载状态
        document.getElementById('loadingCard').style.display = 'block';
        document.getElementById('predictionResult').style.display = 'none';
        showLoading(submitBtn);
        
        try {
            const response = await fetch('/api/predict', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                displayPredictionResult(result);
                addToHistory(result);
                showAlert('预测完成！', 'success');
            } else {
                showAlert('预测失败：' + result.error, 'danger');
            }
        } catch (error) {
            showAlert('预测失败：' + error.message, 'danger');
        } finally {
            document.getElementById('loadingCard').style.display = 'none';
            hideLoading(submitBtn, originalText);
        }
    });
    
    // 显示预测结果
    function displayPredictionResult(result) {
        const resultCard = document.getElementById('predictionResult');
        const resultContent = document.getElementById('resultContent');
        
        // 生成号码球
        let numbersHtml = '';
        result.numbers.forEach((num, index) => {
            let ballClass = 'normal-ball';
            if (result.lottery_type === 'shuangseqiu') {
                ballClass = index < 6 ? 'red-ball' : 'blue-ball';
            } else if (result.lottery_type === 'daletou') {
                ballClass = index < 5 ? 'red-ball' : 'blue-ball';
            }
            numbersHtml += `<span class="number-ball ${ballClass}">${formatNumber(num)}</span>`;
        });
        
        // 置信度颜色
        const confidence = Math.round(result.confidence * 100);
        let confidenceClass = 'success';
        if (confidence < 50) confidenceClass = 'danger';
        else if (confidence < 70) confidenceClass = 'warning';
        
        resultContent.innerHTML = `
            <div class="row">
                <div class="col-12 text-center mb-4">
                    <h4 class="mb-3">推荐号码</h4>
                    <div class="mb-3">
                        ${numbersHtml}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="stat-card">
                        <div class="stat-number text-${confidenceClass}">${confidence}%</div>
                        <div class="stat-label">置信度</div>
                        <div class="progress mt-2">
                            <div class="progress-bar bg-${confidenceClass}" style="width: ${confidence}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 text-center">
                    <div class="stat-card">
                        <div class="stat-number text-primary">${getLotteryName(result.lottery_type)}</div>
                        <div class="stat-label">彩票类型</div>
                    </div>
                </div>
                
                <div class="col-md-4 text-center">
                    <div class="stat-card">
                        <div class="stat-number text-info">${getMethodName(result.method)}</div>
                        <div class="stat-label">预测方法</div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>分析详情</h6>
                        <p class="mb-1"><strong>算法：</strong>${result.details.algorithm}</p>
                        <p class="mb-1"><strong>分析：</strong>${result.details.analysis}</p>
                        <p class="mb-0"><strong>建议：</strong>${result.details.recommendation}</p>
                    </div>
                </div>
            </div>
        `;
        
        resultCard.style.display = 'block';
        resultCard.scrollIntoView({ behavior: 'smooth' });
    }
    
    // 添加到历史记录
    function addToHistory(result) {
        const historyTable = document.getElementById('historyTable');
        const now = new Date().toLocaleString('zh-CN');
        
        // 生成号码球HTML
        let numbersHtml = '';
        result.numbers.forEach((num, index) => {
            let ballClass = 'normal-ball';
            if (result.lottery_type === 'shuangseqiu') {
                ballClass = index < 6 ? 'red-ball' : 'blue-ball';
            } else if (result.lottery_type === 'daletou') {
                ballClass = index < 5 ? 'red-ball' : 'blue-ball';
            }
            numbersHtml += `<span class="number-ball ${ballClass}">${formatNumber(num)}</span>`;
        });
        
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>${now}</td>
            <td>${getLotteryName(result.lottery_type)}</td>
            <td>${numbersHtml}</td>
            <td><span class="badge bg-primary">${getMethodName(result.method)}</span></td>
            <td>${Math.round(result.confidence * 100)}%</td>
            <td><span class="badge bg-warning">待验证</span></td>
        `;
        
        historyTable.insertBefore(newRow, historyTable.firstChild);
        
        // 限制历史记录数量
        if (historyTable.children.length > 10) {
            historyTable.removeChild(historyTable.lastChild);
        }
    }
    
    // 获取彩票类型名称
    function getLotteryName(type) {
        const names = {
            'shuangseqiu': '双色球',
            'daletou': '大乐透',
            'fucai3d': '福彩3D'
        };
        return names[type] || type;
    }
    
    // 获取方法名称
    function getMethodName(method) {
        const names = {
            'traditional': '传统ML',
            'chinese': '中国算法',
            'deeplearning': '深度学习',
            'comprehensive': '综合预测'
        };
        return names[method] || method;
    }
</script>
{% endblock %}
