#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai系统完整测试套件
包含单元测试、集成测试、性能测试等

Author: HuiCai Team
Date: 2025-01-15
"""

import unittest
import asyncio
import sys
import os
import tempfile
import shutil
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入要测试的模块
try:
    from src.management_layer.config_manager import ConfigManager
    from src.management_layer.log_manager import LogManager
    from src.data_layer.data_validator import DataValidator
    from src.algorithm_layer.traditional_ml import TraditionalMLAlgorithms
    from src.algorithm_layer.statistical_algorithms import StatisticalAlgorithms
    from src.algorithm_layer.ensemble_algorithms import EnsembleAlgorithms
    from src.optimization.performance_optimizer import PerformanceOptimizer
    from src.monitoring.system_monitor import SystemMonitor
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_load_default_config(self):
        """测试加载默认配置"""
        config = self.config_manager.get_config()
        self.assertIsInstance(config, dict)
        self.assertIn('system', config)
        self.assertIn('database', config)
    
    def test_get_config_value(self):
        """测试获取配置值"""
        value = self.config_manager.get('system.name', 'default')
        self.assertIsNotNone(value)
    
    def test_set_config_value(self):
        """测试设置配置值"""
        test_value = "test_value"
        self.config_manager.set('test.key', test_value)
        retrieved_value = self.config_manager.get('test.key')
        self.assertEqual(retrieved_value, test_value)


class TestDataValidator(unittest.TestCase):
    """数据验证器测试"""
    
    def setUp(self):
        logger = logging.getLogger('test')
        self.validator = DataValidator(logger)
    
    def test_validate_shuangseqiu_data(self):
        """测试双色球数据验证"""
        # 创建测试数据
        test_data = pd.DataFrame([
            {
                'draw_date': '2025-01-15',
                'period': '2025008',
                'red_balls': [1, 5, 12, 18, 25, 33],
                'blue_ball': 8
            },
            {
                'draw_date': '2025-01-14',
                'period': '2025007',
                'red_balls': [3, 8, 16, 21, 28, 30],
                'blue_ball': 12
            }
        ])
        
        result = self.validator.validate_lottery_data(test_data, 'shuangseqiu')
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)
        self.assertIsNotNone(result.cleaned_data)
    
    def test_validate_invalid_shuangseqiu_data(self):
        """测试无效双色球数据验证"""
        # 创建无效测试数据
        test_data = pd.DataFrame([
            {
                'draw_date': '2025-01-15',
                'period': '2025008',
                'red_balls': [1, 5, 12, 18, 25],  # 只有5个红球
                'blue_ball': 8
            }
        ])
        
        result = self.validator.validate_lottery_data(test_data, 'shuangseqiu')
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)
    
    def test_validate_daletou_data(self):
        """测试大乐透数据验证"""
        test_data = pd.DataFrame([
            {
                'draw_date': '2025-01-15',
                'period': '25008',
                'front_balls': [5, 12, 19, 26, 31],
                'back_balls': [3, 8]
            }
        ])
        
        result = self.validator.validate_lottery_data(test_data, 'daletou')
        self.assertTrue(result.is_valid)
    
    def test_validate_fucai3d_data(self):
        """测试福彩3D数据验证"""
        test_data = pd.DataFrame([
            {
                'draw_date': '2025-01-15',
                'period': '2025015',
                'numbers': [5, 8, 9]
            }
        ])
        
        result = self.validator.validate_lottery_data(test_data, 'fucai3d')
        self.assertTrue(result.is_valid)


class TestTraditionalMLAlgorithms(unittest.TestCase):
    """传统机器学习算法测试"""
    
    def setUp(self):
        logger = logging.getLogger('test')
        self.ml_algorithms = TraditionalMLAlgorithms(logger)
        
        # 创建测试数据
        self.test_data = self._create_test_data()
    
    def _create_test_data(self):
        """创建测试数据"""
        data = []
        for i in range(100):
            red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
            blue_ball = np.random.randint(1, 17)
            data.append({
                'draw_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                'period': f'2025{100-i:03d}',
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        return pd.DataFrame(data)
    
    def test_extract_features(self):
        """测试特征提取"""
        features = self.ml_algorithms.extract_features(self.test_data, 'shuangseqiu')
        self.assertIsInstance(features, pd.DataFrame)
        self.assertGreater(len(features.columns), 0)
        self.assertEqual(len(features), len(self.test_data))
    
    def test_model_initialization(self):
        """测试模型初始化"""
        self.assertIn('random_forest', self.ml_algorithms.models)
        self.assertIn('gradient_boosting', self.ml_algorithms.models)
        self.assertIn('svm', self.ml_algorithms.models)
    
    def test_prediction_generation(self):
        """测试预测生成"""
        # 创建简单的特征数据
        features = pd.DataFrame({
            'feature1': np.random.rand(10),
            'feature2': np.random.rand(10)
        })
        
        # 模拟预测
        predictions = np.random.rand(10, 7)  # 6红球+1蓝球
        probabilities = np.random.rand(10, 7)
        
        recommended_numbers = self.ml_algorithms._generate_recommendations(
            predictions, probabilities, 'shuangseqiu'
        )
        
        self.assertEqual(len(recommended_numbers), 7)  # 6红球+1蓝球
        self.assertTrue(all(1 <= num <= 33 for num in recommended_numbers[:6]))  # 红球范围
        self.assertTrue(1 <= recommended_numbers[6] <= 16)  # 蓝球范围


class TestStatisticalAlgorithms(unittest.TestCase):
    """统计算法测试"""
    
    def setUp(self):
        logger = logging.getLogger('test')
        self.stat_algorithms = StatisticalAlgorithms(logger)
        self.test_data = self._create_test_data()
    
    def _create_test_data(self):
        """创建测试数据"""
        data = []
        for i in range(50):
            red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
            blue_ball = np.random.randint(1, 17)
            data.append({
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        return pd.DataFrame(data)
    
    def test_frequency_analysis(self):
        """测试频率分析"""
        result = self.stat_algorithms.analyze_frequency(self.test_data, 'shuangseqiu')
        
        self.assertIn('red_frequency', result)
        self.assertIn('blue_frequency', result)
        self.assertIn('total_periods', result)
        
        # 检查红球频率统计
        red_freq = result['red_frequency']
        self.assertEqual(len(red_freq), 33)  # 33个红球号码
        
        # 检查蓝球频率统计
        blue_freq = result['blue_frequency']
        self.assertEqual(len(blue_freq), 16)  # 16个蓝球号码
    
    def test_pattern_analysis(self):
        """测试模式分析"""
        result = self.stat_algorithms.analyze_patterns(self.test_data, 'shuangseqiu')
        
        self.assertIn('odd_even_patterns', result)
        self.assertIn('big_small_patterns', result)
        self.assertIn('zone_patterns', result)
        self.assertIn('sum_patterns', result)
    
    def test_frequency_prediction(self):
        """测试频率预测"""
        result = self.stat_algorithms.predict_by_frequency(self.test_data, 'shuangseqiu')
        
        self.assertEqual(result['lottery_type'], 'shuangseqiu')
        self.assertEqual(result['method'], 'frequency_analysis')
        self.assertEqual(len(result['numbers']), 7)  # 6红球+1蓝球
        self.assertIsInstance(result['confidence'], float)


class TestEnsembleAlgorithms(unittest.TestCase):
    """集成算法测试"""
    
    def setUp(self):
        logger = logging.getLogger('test')
        self.ensemble = EnsembleAlgorithms(logger)
    
    def test_weighted_ensemble_prediction(self):
        """测试加权集成预测"""
        # 创建模拟预测结果
        predictions = {
            'traditional_ml': {
                'numbers': [1, 5, 12, 18, 25, 33, 8],
                'confidence': 0.75,
                'method': 'random_forest'
            },
            'statistical': {
                'numbers': [3, 8, 16, 21, 28, 30, 12],
                'confidence': 0.68,
                'method': 'frequency_analysis'
            },
            'chinese': {
                'numbers': [2, 7, 14, 19, 26, 32, 5],
                'confidence': 0.60,
                'method': 'wu_xing'
            }
        }
        
        result = self.ensemble.weighted_ensemble_prediction(predictions, 'shuangseqiu')
        
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['lottery_type'], 'shuangseqiu')
        self.assertEqual(result['method'], 'weighted_ensemble')
        self.assertEqual(len(result['numbers']), 7)
        self.assertIsInstance(result['confidence'], float)
        self.assertEqual(result['algorithm_count'], 3)
    
    def test_voting_ensemble_prediction(self):
        """测试投票集成预测"""
        predictions = {
            'algorithm1': {
                'numbers': [1, 5, 12, 18, 25, 33, 8],
                'confidence': 0.8
            },
            'algorithm2': {
                'numbers': [1, 8, 12, 21, 25, 30, 8],
                'confidence': 0.7
            }
        }
        
        result = self.ensemble.voting_ensemble_prediction(predictions, 'shuangseqiu')
        
        self.assertEqual(result['lottery_type'], 'shuangseqiu')
        self.assertEqual(result['method'], 'voting_ensemble')
        self.assertIn('vote_distribution', result)
        self.assertIn('consistency_score', result)
    
    def test_fallback_prediction(self):
        """测试备用预测"""
        result = self.ensemble._generate_fallback_prediction('shuangseqiu')
        
        self.assertEqual(result['lottery_type'], 'shuangseqiu')
        self.assertEqual(result['method'], 'fallback_random')
        self.assertEqual(len(result['numbers']), 7)


class TestPerformanceOptimizer(unittest.TestCase):
    """性能优化器测试"""
    
    def setUp(self):
        config_manager = ConfigManager()
        logger = logging.getLogger('test')
        self.optimizer = PerformanceOptimizer(config_manager, logger)
    
    def test_memory_optimization(self):
        """测试内存优化"""
        result = self.optimizer.optimize_memory()
        self.assertIn('memory_freed_mb', result)
        self.assertIn('optimization_time', result)
    
    def test_cache_optimization(self):
        """测试缓存优化"""
        result = self.optimizer.optimize_cache()
        self.assertIn('cache_cleared', result)
        self.assertIn('cache_size_mb', result)
    
    def test_system_optimization(self):
        """测试系统优化"""
        # 这是一个异步方法，需要特殊处理
        async def run_test():
            result = await self.optimizer.optimize_system()
            self.assertIn('memory_optimization', result)
            self.assertIn('cache_optimization', result)
            self.assertIn('total_time', result)
        
        # 运行异步测试
        asyncio.run(run_test())


class TestSystemMonitor(unittest.TestCase):
    """系统监控器测试"""
    
    def setUp(self):
        config_manager = ConfigManager()
        logger = logging.getLogger('test')
        self.monitor = SystemMonitor(config_manager, logger)
    
    def test_collect_metrics(self):
        """测试指标收集"""
        metrics = self.monitor.collect_metrics()
        
        self.assertIsNotNone(metrics.timestamp)
        self.assertIsInstance(metrics.cpu_percent, float)
        self.assertIsInstance(metrics.memory_percent, float)
        self.assertIsInstance(metrics.disk_percent, float)
        self.assertGreaterEqual(metrics.cpu_percent, 0)
        self.assertLessEqual(metrics.cpu_percent, 100)
    
    def test_alert_rules(self):
        """测试告警规则"""
        rules = self.monitor.alert_rules
        self.assertGreater(len(rules), 0)
        
        # 检查默认规则
        rule_names = [rule.name for rule in rules]
        self.assertIn("CPU使用率过高", rule_names)
        self.assertIn("内存使用率过高", rule_names)
    
    def test_current_status(self):
        """测试获取当前状态"""
        # 先收集一些指标
        metrics = self.monitor.collect_metrics()
        self.monitor.metrics_history.append(metrics)
        
        status = self.monitor.get_current_status()
        self.assertIn('timestamp', status)
        self.assertIn('cpu_percent', status)
        self.assertIn('memory_percent', status)
        self.assertIn('monitoring_status', status)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        self.config_manager = ConfigManager()
        self.logger = logging.getLogger('integration_test')
    
    def test_full_prediction_pipeline(self):
        """测试完整预测流程"""
        # 1. 创建测试数据
        test_data = self._create_comprehensive_test_data()
        
        # 2. 数据验证
        validator = DataValidator(self.logger)
        validation_result = validator.validate_lottery_data(test_data, 'shuangseqiu')
        self.assertTrue(validation_result.is_valid)
        
        # 3. 传统ML预测
        ml_algorithms = TraditionalMLAlgorithms(self.logger)
        features = ml_algorithms.extract_features(validation_result.cleaned_data, 'shuangseqiu')
        self.assertGreater(len(features), 0)
        
        # 4. 统计算法预测
        stat_algorithms = StatisticalAlgorithms(self.logger)
        freq_result = stat_algorithms.predict_by_frequency(validation_result.cleaned_data, 'shuangseqiu')
        self.assertEqual(freq_result['lottery_type'], 'shuangseqiu')
        
        # 5. 集成预测
        ensemble = EnsembleAlgorithms(self.logger)
        predictions = {
            'statistical': freq_result
        }
        ensemble_result = ensemble.weighted_ensemble_prediction(predictions, 'shuangseqiu')
        self.assertEqual(ensemble_result['lottery_type'], 'shuangseqiu')
    
    def _create_comprehensive_test_data(self):
        """创建综合测试数据"""
        data = []
        for i in range(200):  # 更多数据用于集成测试
            red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
            blue_ball = np.random.randint(1, 17)
            data.append({
                'draw_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                'period': f'2025{200-i:03d}',
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        return pd.DataFrame(data)


def run_all_tests():
    """运行所有测试"""
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestConfigManager,
        TestDataValidator,
        TestTraditionalMLAlgorithms,
        TestStatisticalAlgorithms,
        TestEnsembleAlgorithms,
        TestPerformanceOptimizer,
        TestSystemMonitor,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要")
    print("="*60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {(result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100:.1f}%")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
