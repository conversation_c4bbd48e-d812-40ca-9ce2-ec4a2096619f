#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LSTM时序预测模型
用于彩票号码的时序模式学习和预测

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Embedding, Attention, MultiHeadAttention
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from sklearn.preprocessing import MinMaxScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, accuracy_score
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, date
import pickle
import os


class LSTMPredictor:
    """LSTM时序预测器"""
    
    def __init__(self, config_manager, logger: logging.Logger, lottery_type: str):
        """
        初始化LSTM预测器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
            lottery_type: 彩票类型
        """
        self.config_manager = config_manager
        self.logger = logger
        self.lottery_type = lottery_type
        
        # 深度学习配置
        self.dl_config = config_manager.get('deep_learning', {})
        self.lstm_config = self.dl_config.get('lstm', {})
        
        # 模型参数
        self.sequence_length = self.lstm_config.get('sequence_length', 20)
        self.hidden_units = self.lstm_config.get('hidden_units', 128)
        self.num_layers = self.lstm_config.get('num_layers', 2)
        self.dropout_rate = self.lstm_config.get('dropout_rate', 0.2)
        self.learning_rate = self.lstm_config.get('learning_rate', 0.001)
        self.batch_size = self.lstm_config.get('batch_size', 32)
        self.epochs = self.lstm_config.get('epochs', 100)
        
        # 彩票特定配置
        self.lottery_config = config_manager.get_lottery_config(lottery_type)
        self.max_number = self._get_max_number()
        
        # 模型组件
        self.model = None
        self.scaler = MinMaxScaler()
        self.label_encoder = LabelEncoder()
        
        # 训练历史
        self.training_history = []
        
        # 模型保存路径
        self.model_save_path = config_manager.get('learning.model_save_path', 'data/models')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.info(f"LSTM预测器初始化完成: {lottery_type}")
    
    def _get_max_number(self) -> int:
        """获取彩票的最大号码"""
        if self.lottery_type == 'shuangseqiu':
            return max(self.lottery_config.get('red_ball_range', [1, 33])[1],
                      self.lottery_config.get('blue_ball_range', [1, 16])[1])
        elif self.lottery_type == 'daletou':
            return max(self.lottery_config.get('front_range', [1, 35])[1],
                      self.lottery_config.get('back_range', [1, 12])[1])
        elif self.lottery_type == 'fucai3d':
            return self.lottery_config.get('number_range', [0, 9])[1]
        else:
            return 50  # 默认值
    
    def build_model(self, input_shape: Tuple[int, int], output_dim: int) -> Model:
        """
        构建LSTM模型
        
        Args:
            input_shape: 输入形状 (sequence_length, features)
            output_dim: 输出维度
            
        Returns:
            Model: 构建的模型
        """
        try:
            model = Sequential([
                # 第一层LSTM
                LSTM(
                    self.hidden_units,
                    return_sequences=True,
                    input_shape=input_shape,
                    name='lstm_1'
                ),
                Dropout(self.dropout_rate, name='dropout_1'),
                
                # 第二层LSTM
                LSTM(
                    self.hidden_units // 2,
                    return_sequences=False,
                    name='lstm_2'
                ),
                Dropout(self.dropout_rate, name='dropout_2'),
                
                # 全连接层
                Dense(64, activation='relu', name='dense_1'),
                Dropout(self.dropout_rate / 2, name='dropout_3'),
                
                Dense(32, activation='relu', name='dense_2'),
                
                # 输出层
                Dense(output_dim, activation='softmax', name='output')
            ])
            
            # 编译模型
            model.compile(
                optimizer=Adam(learning_rate=self.learning_rate),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info(f"LSTM模型构建完成: {model.summary()}")
            return model
            
        except Exception as e:
            self.logger.error(f"构建LSTM模型失败: {e}")
            raise
    
    def build_attention_model(self, input_shape: Tuple[int, int], output_dim: int) -> Model:
        """
        构建带注意力机制的LSTM模型
        
        Args:
            input_shape: 输入形状
            output_dim: 输出维度
            
        Returns:
            Model: 带注意力的模型
        """
        try:
            # 输入层
            inputs = tf.keras.Input(shape=input_shape, name='input')
            
            # LSTM层
            lstm_out = LSTM(
                self.hidden_units,
                return_sequences=True,
                name='lstm_layer'
            )(inputs)
            
            # 多头注意力机制
            attention_out = MultiHeadAttention(
                num_heads=8,
                key_dim=self.hidden_units // 8,
                name='multi_head_attention'
            )(lstm_out, lstm_out)
            
            # 残差连接
            residual = tf.keras.layers.Add(name='residual')([lstm_out, attention_out])
            
            # 层归一化
            normalized = tf.keras.layers.LayerNormalization(name='layer_norm')(residual)
            
            # 全局平均池化
            pooled = tf.keras.layers.GlobalAveragePooling1D(name='global_avg_pool')(normalized)
            
            # 全连接层
            dense1 = Dense(64, activation='relu', name='dense_1')(pooled)
            dropout1 = Dropout(self.dropout_rate, name='dropout_1')(dense1)
            
            dense2 = Dense(32, activation='relu', name='dense_2')(dropout1)
            dropout2 = Dropout(self.dropout_rate / 2, name='dropout_2')(dense2)
            
            # 输出层
            outputs = Dense(output_dim, activation='softmax', name='output')(dropout2)
            
            # 创建模型
            model = Model(inputs=inputs, outputs=outputs, name='lstm_attention_model')
            
            # 编译模型
            model.compile(
                optimizer=Adam(learning_rate=self.learning_rate),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("带注意力机制的LSTM模型构建完成")
            return model
            
        except Exception as e:
            self.logger.error(f"构建注意力LSTM模型失败: {e}")
            raise
    
    def prepare_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备时序数据
        
        Args:
            data: 历史开奖数据
            
        Returns:
            Tuple: (X, y) 训练数据
        """
        try:
            # 提取号码序列
            numbers_sequence = []
            for _, row in data.iterrows():
                numbers = row['numbers']
                if isinstance(numbers, list):
                    numbers_sequence.extend(numbers)
                else:
                    # 如果是字符串，尝试解析
                    try:
                        numbers = eval(numbers) if isinstance(numbers, str) else numbers
                        numbers_sequence.extend(numbers)
                    except:
                        continue
            
            if len(numbers_sequence) < self.sequence_length + 1:
                raise ValueError(f"数据量不足，需要至少 {self.sequence_length + 1} 个数字")
            
            # 归一化
            numbers_array = np.array(numbers_sequence).reshape(-1, 1)
            scaled_numbers = self.scaler.fit_transform(numbers_array).flatten()
            
            # 创建时序样本
            X, y = [], []
            for i in range(len(scaled_numbers) - self.sequence_length):
                X.append(scaled_numbers[i:i + self.sequence_length])
                y.append(numbers_sequence[i + self.sequence_length])
            
            X = np.array(X)
            y = np.array(y)
            
            # 重塑X为3D数组 (samples, timesteps, features)
            X = X.reshape(X.shape[0], X.shape[1], 1)
            
            # 编码标签
            y_encoded = self.label_encoder.fit_transform(y)
            
            self.logger.info(f"时序数据准备完成: X.shape={X.shape}, y.shape={y_encoded.shape}")
            return X, y_encoded
            
        except Exception as e:
            self.logger.error(f"准备时序数据失败: {e}")
            raise
    
    def prepare_multi_feature_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备多特征时序数据
        
        Args:
            data: 历史开奖数据
            
        Returns:
            Tuple: (X, y) 多特征训练数据
        """
        try:
            sequences = []
            targets = []
            
            for i in range(len(data) - self.sequence_length):
                # 提取序列窗口
                window_data = data.iloc[i:i + self.sequence_length]
                target_data = data.iloc[i + self.sequence_length]
                
                # 构建特征序列
                sequence_features = []
                for _, row in window_data.iterrows():
                    numbers = row['numbers']
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    
                    # 基础特征
                    features = [
                        np.mean(numbers),           # 平均值
                        np.std(numbers),            # 标准差
                        max(numbers),               # 最大值
                        min(numbers),               # 最小值
                        len(set(numbers)),          # 唯一数字个数
                        sum(numbers),               # 和值
                        max(numbers) - min(numbers), # 跨度
                        sum(1 for x in numbers if x % 2 == 1), # 奇数个数
                        sum(1 for x in numbers if x % 2 == 0), # 偶数个数
                    ]
                    
                    # 添加号码本身（归一化）
                    normalized_numbers = [x / self.max_number for x in numbers]
                    features.extend(normalized_numbers)
                    
                    sequence_features.append(features)
                
                sequences.append(sequence_features)
                
                # 目标：下一期的第一个号码（简化）
                target_numbers = target_data['numbers']
                if isinstance(target_numbers, str):
                    target_numbers = eval(target_numbers)
                targets.append(target_numbers[0])
            
            X = np.array(sequences)
            y = np.array(targets)
            
            # 编码目标
            y_encoded = self.label_encoder.fit_transform(y)
            
            self.logger.info(f"多特征时序数据准备完成: X.shape={X.shape}, y.shape={y_encoded.shape}")
            return X, y_encoded
            
        except Exception as e:
            self.logger.error(f"准备多特征时序数据失败: {e}")
            raise
    
    def train(self, data: pd.DataFrame, use_attention: bool = False, 
              use_multi_features: bool = True) -> Dict[str, Any]:
        """
        训练LSTM模型
        
        Args:
            data: 训练数据
            use_attention: 是否使用注意力机制
            use_multi_features: 是否使用多特征
            
        Returns:
            Dict: 训练结果
        """
        try:
            self.logger.info(f"开始训练LSTM模型: {self.lottery_type}")
            
            # 准备数据
            if use_multi_features:
                X, y = self.prepare_multi_feature_sequences(data)
            else:
                X, y = self.prepare_sequences(data)
            
            # 分割训练和验证集
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # 构建模型
            input_shape = (X.shape[1], X.shape[2])
            output_dim = len(np.unique(y))
            
            if use_attention:
                self.model = self.build_attention_model(input_shape, output_dim)
            else:
                self.model = self.build_model(input_shape, output_dim)
            
            # 回调函数
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True,
                    verbose=1
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7,
                    verbose=1
                ),
                ModelCheckpoint(
                    filepath=os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_best.h5'),
                    monitor='val_loss',
                    save_best_only=True,
                    verbose=1
                )
            ]
            
            # 训练模型
            history = self.model.fit(
                X_train, y_train,
                batch_size=self.batch_size,
                epochs=self.epochs,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1
            )
            
            # 评估模型
            train_loss, train_acc = self.model.evaluate(X_train, y_train, verbose=0)
            val_loss, val_acc = self.model.evaluate(X_val, y_val, verbose=0)
            
            # 保存训练历史
            training_result = {
                'timestamp': datetime.now(),
                'lottery_type': self.lottery_type,
                'use_attention': use_attention,
                'use_multi_features': use_multi_features,
                'train_samples': len(X_train),
                'val_samples': len(X_val),
                'train_loss': float(train_loss),
                'train_accuracy': float(train_acc),
                'val_loss': float(val_loss),
                'val_accuracy': float(val_acc),
                'epochs_trained': len(history.history['loss']),
                'best_val_loss': float(min(history.history['val_loss'])),
                'model_params': {
                    'sequence_length': self.sequence_length,
                    'hidden_units': self.hidden_units,
                    'num_layers': self.num_layers,
                    'dropout_rate': self.dropout_rate,
                    'learning_rate': self.learning_rate
                }
            }
            
            self.training_history.append(training_result)
            
            # 保存模型和相关组件
            self._save_model_components()
            
            self.logger.info(f"LSTM模型训练完成: 验证准确率={val_acc:.4f}")
            return training_result
            
        except Exception as e:
            self.logger.error(f"LSTM模型训练失败: {e}")
            raise
    
    def predict(self, recent_data: pd.DataFrame, num_predictions: int = 1) -> Dict[str, Any]:
        """
        使用LSTM模型进行预测
        
        Args:
            recent_data: 最近的数据
            num_predictions: 预测数量
            
        Returns:
            Dict: 预测结果
        """
        try:
            if self.model is None:
                raise ValueError("模型未训练或加载")
            
            # 准备输入数据
            if len(recent_data) < self.sequence_length:
                raise ValueError(f"数据不足，需要至少 {self.sequence_length} 期数据")
            
            # 使用最近的数据作为输入
            last_sequence = recent_data.tail(self.sequence_length)
            
            # 准备特征（与训练时保持一致）
            sequence_features = []
            for _, row in last_sequence.iterrows():
                numbers = row['numbers']
                if isinstance(numbers, str):
                    numbers = eval(numbers)
                
                features = [
                    np.mean(numbers),
                    np.std(numbers),
                    max(numbers),
                    min(numbers),
                    len(set(numbers)),
                    sum(numbers),
                    max(numbers) - min(numbers),
                    sum(1 for x in numbers if x % 2 == 1),
                    sum(1 for x in numbers if x % 2 == 0),
                ]
                
                normalized_numbers = [x / self.max_number for x in numbers]
                features.extend(normalized_numbers)
                
                sequence_features.append(features)
            
            # 重塑为模型输入格式
            X_pred = np.array([sequence_features])
            
            # 预测
            predictions = []
            for _ in range(num_predictions):
                pred_proba = self.model.predict(X_pred, verbose=0)
                pred_class = np.argmax(pred_proba[0])
                
                # 解码预测结果
                predicted_number = self.label_encoder.inverse_transform([pred_class])[0]
                confidence = float(np.max(pred_proba[0]))
                
                predictions.append({
                    'predicted_number': int(predicted_number),
                    'confidence': confidence,
                    'probability_distribution': pred_proba[0].tolist()
                })
                
                # 为下一次预测更新输入（简化处理）
                # 这里可以实现更复杂的序列更新逻辑
            
            result = {
                'lottery_type': self.lottery_type,
                'prediction_date': datetime.now(),
                'model_type': 'LSTM',
                'predictions': predictions,
                'input_sequence_length': self.sequence_length,
                'model_accuracy': self.training_history[-1]['val_accuracy'] if self.training_history else 0.0
            }
            
            self.logger.info(f"LSTM预测完成: {len(predictions)} 个预测")
            return result
            
        except Exception as e:
            self.logger.error(f"LSTM预测失败: {e}")
            return {}
    
    def _save_model_components(self):
        """保存模型组件"""
        try:
            # 保存模型
            model_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_model.h5')
            self.model.save(model_path)
            
            # 保存预处理器
            scaler_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_scaler.pkl')
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            encoder_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_encoder.pkl')
            with open(encoder_path, 'wb') as f:
                pickle.dump(self.label_encoder, f)
            
            # 保存训练历史
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_history.pkl')
            with open(history_path, 'wb') as f:
                pickle.dump(self.training_history, f)
            
            self.logger.info("LSTM模型组件保存完成")
            
        except Exception as e:
            self.logger.error(f"保存LSTM模型组件失败: {e}")
    
    def load_model_components(self) -> bool:
        """加载模型组件"""
        try:
            # 加载模型
            model_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_model.h5')
            if os.path.exists(model_path):
                self.model = tf.keras.models.load_model(model_path)
            else:
                return False
            
            # 加载预处理器
            scaler_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_scaler.pkl')
            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
            
            encoder_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_encoder.pkl')
            if os.path.exists(encoder_path):
                with open(encoder_path, 'rb') as f:
                    self.label_encoder = pickle.load(f)
            
            # 加载训练历史
            history_path = os.path.join(self.model_save_path, f'{self.lottery_type}_lstm_history.pkl')
            if os.path.exists(history_path):
                with open(history_path, 'rb') as f:
                    self.training_history = pickle.load(f)
            
            self.logger.info("LSTM模型组件加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载LSTM模型组件失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.training_history:
            return {}
        
        latest_training = self.training_history[-1]
        
        return {
            'model_type': 'LSTM',
            'lottery_type': self.lottery_type,
            'last_training': latest_training['timestamp'],
            'validation_accuracy': latest_training['val_accuracy'],
            'validation_loss': latest_training['val_loss'],
            'epochs_trained': latest_training['epochs_trained'],
            'model_parameters': latest_training['model_params'],
            'total_trainings': len(self.training_history)
        }
