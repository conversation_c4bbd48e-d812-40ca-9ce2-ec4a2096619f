#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai 慧彩 - 彩票分析智能体系统
主程序入口

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.management_layer.config_manager import ConfigManager
from src.management_layer.log_manager import LogManager
from src.management_layer.scheduler import Scheduler
from src.data_layer.crawler_manager import CrawlerManager
from src.learning_layer.incremental_learner import IncrementalLearner
from src.interface_layer.cli_interface import C<PERSON>IInterface
from src.optimization.performance_optimizer import PerformanceOptimizer


class HuiCaiSystem:
    """HuiCai 慧彩智能体系统主类"""
    
    def __init__(self):
        self.config_manager = None
        self.log_manager = None
        self.scheduler = None
        self.crawler_manager = None
        self.incremental_learner = None
        self.cli_interface = None
        self.performance_optimizer = None
        self.logger = None
        
    async def initialize(self):
        """初始化系统"""
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager()
            await self.config_manager.load_config()
            
            # 初始化日志管理器
            self.log_manager = LogManager(self.config_manager)
            self.logger = self.log_manager.get_logger("HuiCaiSystem")
            self.logger.info("HuiCai 慧彩系统启动中...")
            
            # 初始化任务调度器
            self.scheduler = Scheduler(self.config_manager, self.logger)
            
            # 初始化爬虫管理器
            self.crawler_manager = CrawlerManager(self.config_manager, self.logger)
            
            # 初始化增量学习器
            self.incremental_learner = IncrementalLearner(self.config_manager, self.logger)
            
            # 初始化性能优化器
            self.performance_optimizer = PerformanceOptimizer(self.config_manager, self.logger)

            # 初始化CLI界面
            self.cli_interface = CLIInterface(self)

            self.logger.info("HuiCai 慧彩系统初始化完成")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}")
            else:
                print(f"系统初始化失败: {e}")
            raise
    
    async def start(self):
        """启动系统"""
        try:
            self.logger.info("启动HuiCai 慧彩系统...")
            
            # 启动爬虫管理器
            await self.crawler_manager.start()
            
            # 启动增量学习器
            await self.incremental_learner.start()
            
            # 设置调度器组件引用
            self.scheduler.set_components(self.crawler_manager, self.incremental_learner)

            # 启动任务调度器
            await self.scheduler.start()
            
            self.logger.info("HuiCai 慧彩系统启动成功")
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            raise
    
    async def stop(self):
        """停止系统"""
        try:
            self.logger.info("停止HuiCai 慧彩系统...")
            
            # 停止任务调度器
            if self.scheduler:
                await self.scheduler.stop()
            
            # 停止增量学习器
            if self.incremental_learner:
                await self.incremental_learner.stop()

            # 停止性能优化器
            if self.performance_optimizer:
                await self.performance_optimizer.close()

            # 停止爬虫管理器
            if self.crawler_manager:
                await self.crawler_manager.stop()
            
            self.logger.info("HuiCai 慧彩系统已停止")
            
        except Exception as e:
            self.logger.error(f"系统停止失败: {e}")
            raise
    
    async def run_cli(self):
        """运行CLI界面"""
        if self.cli_interface:
            await self.cli_interface.run()


async def main():
    """主函数"""
    system = HuiCaiSystem()
    
    try:
        # 初始化系统
        await system.initialize()
        
        # 启动系统
        await system.start()
        
        # 运行CLI界面
        await system.run_cli()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止系统...")
    except Exception as e:
        print(f"系统运行错误: {e}")
    finally:
        # 停止系统
        await system.stop()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())
