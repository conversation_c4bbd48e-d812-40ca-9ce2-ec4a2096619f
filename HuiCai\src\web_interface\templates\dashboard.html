{% extends "base.html" %}

{% block title %}仪表板 - HuiCai 慧彩智能体系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt text-primary me-2"></i>
            系统仪表板
            <small class="text-muted ms-3">
                <i class="fas fa-clock me-1"></i>
                <span class="current-time">{{ current_time }}</span>
            </small>
        </h2>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ system_info.active_models or 3 }}</div>
            <div class="stat-label">
                <i class="fas fa-robot me-1"></i>活跃模型
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ system_info.predictions_today or 15 }}</div>
            <div class="stat-label">
                <i class="fas fa-magic me-1"></i>今日预测
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ "%.1f"|format(system_info.memory_percent or 65.5) }}%</div>
            <div class="stat-label">
                <i class="fas fa-memory me-1"></i>内存使用
            </div>
            <div class="progress mt-2">
                <div class="progress-bar" style="width: {{ system_info.memory_percent or 65.5 }}%"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ system_info.uptime or "2小时" }}</div>
            <div class="stat-label">
                <i class="fas fa-clock me-1"></i>运行时间
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="row">
    <!-- 左侧：系统信息 -->
    <div class="col-lg-8">
        <!-- 系统状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><i class="fas fa-desktop me-2"></i>操作系统</td>
                                <td>{{ system_info.platform or "Windows 11" }}</td>
                            </tr>
                            <tr>
                                <td><i class="fab fa-python me-2"></i>Python版本</td>
                                <td>{{ system_info.python_version or "3.12.10" }}</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-microchip me-2"></i>CPU核心</td>
                                <td>{{ system_info.cpu_count or 4 }} 核</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><i class="fas fa-memory me-2"></i>总内存</td>
                                <td>{{ "%.1f"|format(system_info.memory_total or 7.9) }} GB</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-hdd me-2"></i>可用磁盘</td>
                                <td>{{ "%.1f"|format(system_info.disk_free or 47.9) }} GB</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-circle text-success me-2"></i>状态</td>
                                <td><span class="badge bg-success">{{ system_info.status or "运行中" }}</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 预测准确率图表 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line text-success me-2"></i>
                    预测准确率趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="accuracyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 右侧：快速操作和最近活动 -->
    <div class="col-lg-4">
        <!-- 快速操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt text-warning me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/prediction" class="btn btn-primary">
                        <i class="fas fa-magic me-2"></i>开始预测
                    </a>
                    <button class="btn btn-outline-info" onclick="optimizeSystem()">
                        <i class="fas fa-tools me-2"></i>系统优化
                    </button>
                    <button class="btn btn-outline-success" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-2"></i>刷新数据
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 最近预测 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history text-primary me-2"></i>
                    最近预测
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>双色球</strong><br>
                            <small class="text-muted">2分钟前</small>
                        </div>
                        <span class="badge bg-success rounded-pill">78%</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>大乐透</strong><br>
                            <small class="text-muted">1小时前</small>
                        </div>
                        <span class="badge bg-info rounded-pill">72%</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>福彩3D</strong><br>
                            <small class="text-muted">3小时前</small>
                        </div>
                        <span class="badge bg-warning rounded-pill">65%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统日志 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list text-secondary me-2"></i>
                    系统日志
                </h5>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <span class="badge bg-success">INFO</span>
                        <span class="ms-2">系统启动完成</span>
                        <br><small class="text-muted">10:30:00</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info">INFO</span>
                        <span class="ms-2">双色球预测完成</span>
                        <br><small class="text-muted">10:31:00</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">WARN</span>
                        <span class="ms-2">内存使用率较高</span>
                        <br><small class="text-muted">10:32:00</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 预测准确率图表
    const ctx = document.getElementById('accuracyChart').getContext('2d');
    const accuracyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '预测准确率',
                data: [65, 68, 72, 70, 75, 78],
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
    
    // 系统优化
    async function optimizeSystem() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            const response = await fetch('/api/performance/optimize');
            const result = await response.json();
            
            if (result.success) {
                showAlert('系统优化完成！', 'success');
            } else {
                showAlert('优化失败：' + result.error, 'danger');
            }
        } catch (error) {
            showAlert('优化失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 刷新数据
    function refreshData() {
        showAlert('数据刷新中...', 'info');
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
    
    // 定期更新系统状态
    setInterval(async () => {
        try {
            const response = await fetch('/api/system/info');
            const info = await response.json();
            
            // 更新内存使用率
            const memoryPercent = info.memory_percent || 65.5;
            document.querySelector('.progress-bar').style.width = memoryPercent + '%';
            
        } catch (error) {
            console.log('更新系统状态失败:', error);
        }
    }, 30000); // 每30秒更新一次
</script>
{% endblock %}
