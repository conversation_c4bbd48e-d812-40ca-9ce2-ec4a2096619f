import { configureStore } from '@reduxjs/toolkit';
import systemReducer from './slices/systemSlice';
import predictionReducer from './slices/predictionSlice';
import dataReducer from './slices/dataSlice';
import modelReducer from './slices/modelSlice';

export const store = configureStore({
  reducer: {
    system: systemReducer,
    prediction: predictionReducer,
    data: dataReducer,
    model: modelReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
