#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的大乐透爬虫测试

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fixed_dlt_crawler():
    """测试修复后的大乐透爬虫"""
    print("🔵 测试修复后的大乐透爬虫...")
    print("-" * 50)
    
    try:
        from src.crawler_system.daletou_crawler import DaletouCrawler
        
        # 创建爬虫实例
        crawler = DaletouCrawler()
        print("✅ 爬虫创建成功")
        print(f"📍 数据源数量: {len(crawler.data_sources)}")
        
        # 显示数据源信息
        for i, source in enumerate(crawler.data_sources):
            print(f"  {i+1}. {source['name']} ({source['type']})")
        
        # 测试爬取数据
        print("\n📡 爬取最新3期数据...")
        result = crawler.crawl_latest_data(3)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            
            print(f"\n🎯 开奖数据详情:")
            for i, draw in enumerate(result['data']):
                issue = draw.get('issue', '未知期号')
                date = draw.get('date', '未知日期')
                front_balls = draw.get('front_balls', [])
                back_balls = draw.get('back_balls', [])
                source = draw.get('source', '未知来源')
                
                front_str = ' '.join([f"{ball:02d}" for ball in front_balls])
                back_str = ' '.join([f"{ball:02d}" for ball in back_balls])
                print(f"  {i+1}. 期号: {issue}")
                print(f"     日期: {date}")
                print(f"     号码: {front_str} + {back_str}")
                print(f"     来源: {source}")
                print()
            
            # 验证期号和日期不为None
            all_valid = True
            for draw in result['data']:
                if not draw.get('issue') or not draw.get('date'):
                    all_valid = False
                    print(f"❌ 发现无效数据: {draw}")
                
                # 验证号码有效性
                front_balls = draw.get('front_balls', [])
                back_balls = draw.get('back_balls', [])
                
                front_valid = (len(front_balls) == 5 and 
                             all(1 <= ball <= 35 for ball in front_balls) and
                             len(set(front_balls)) == 5)
                
                back_valid = (len(back_balls) == 2 and
                            all(1 <= ball <= 12 for ball in back_balls) and
                            len(set(back_balls)) == 2)
                
                if not (front_valid and back_valid):
                    all_valid = False
                    print(f"❌ 发现无效号码: {draw}")
            
            if all_valid:
                print("✅ 所有数据都包含有效的期号、日期和号码！")
                return True
            else:
                print("❌ 部分数据无效")
                return False
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            crawler.close()
        except:
            pass

def test_dlt_database_insert():
    """测试大乐透数据库插入"""
    print("\n💾 测试大乐透数据库插入...")
    print("-" * 50)
    
    try:
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import DaletouDataAccess
        
        # 创建数据库管理器
        db_manager = LocalDatabaseManager()
        dlt_data = DaletouDataAccess(db_manager)
        
        # 测试数据
        test_data = [
            {
                'issue': '25001',
                'date': '2025-01-01',
                'front_balls': [1, 7, 12, 23, 28],
                'back_balls': [3, 8],
                'sales_amount': 250000000,
                'pool_amount': 600000000,
                'source': '测试数据'
            }
        ]
        
        print("📝 插入测试数据...")
        inserted_count = dlt_data.batch_insert_draw_results(test_data)
        print(f"✅ 成功插入 {inserted_count} 条记录")
        
        # 查询验证
        print("🔍 查询验证...")
        latest = dlt_data.get_latest_results(1)
        if latest:
            print(f"✅ 查询成功，最新记录: {latest[0]}")
            return True
        else:
            print("❌ 查询失败，未找到记录")
            return False
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            db_manager.close_all_connections()
        except:
            pass

def test_crawler_manager_dlt():
    """测试爬虫管理器的大乐透功能"""
    print("\n🎛️ 测试爬虫管理器大乐透功能...")
    print("-" * 50)
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        
        # 创建管理器
        manager = CrawlerManager()
        print("✅ 爬虫管理器创建成功")
        
        # 测试手动爬取大乐透
        print("\n📡 手动爬取大乐透数据...")
        result = manager.manual_crawl('daletou', 2)
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功，保存 {result.get('saved_count', 0)} 条记录")
            
            # 显示数据摘要
            print("\n📊 数据摘要:")
            summary = manager.get_data_summary()
            if 'daletou' in summary:
                dlt_data = summary['daletou']
                print(f"  - 总记录数: {dlt_data.get('total_records', 0)}")
                if 'latest_issue' in dlt_data and dlt_data['latest_issue']:
                    latest = dlt_data['latest_issue']
                    print(f"  - 最新期号: {latest.get('issue', 'N/A')}")
                    print(f"  - 开奖日期: {latest.get('date', 'N/A')}")
                else:
                    print(f"  - 最新期号: 暂无数据")
            
            return True
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            manager.close()
        except:
            pass

def main():
    """主函数"""
    print("🎯 修复后的大乐透爬虫系统测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success_count = 0
    total_tests = 3
    
    # 测试大乐透爬虫
    if test_fixed_dlt_crawler():
        success_count += 1
    
    # 测试数据库
    if test_dlt_database_insert():
        success_count += 1
    
    # 测试爬虫管理器
    if test_crawler_manager_dlt():
        success_count += 1
    
    # 显示结果
    print("=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！大乐透爬虫修复成功！")
        print("\n✅ 修复内容:")
        print("  🔧 500彩票网大乐透数据源集成")
        print("  🔧 HTML解析和数据提取")
        print("  🔧 期号和日期解析优化")
        print("  🔧 大乐透号码验证（前区1-35，后区1-12）")
        print("  🔧 默认期号和日期生成")
        print("  🔧 数据库集成和存储")
        print("\n🎯 现在双色球和大乐透爬虫都可以正常工作了！")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
