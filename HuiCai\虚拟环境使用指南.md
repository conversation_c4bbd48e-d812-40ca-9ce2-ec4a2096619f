# HuiCai 虚拟环境使用指南

## ✅ 环境配置完成

恭喜！您已经成功在 `E:\lottery\HuiCai` 目录下创建并配置了HuiCai虚拟环境。

### 📋 环境信息
- **项目路径**: `E:\lottery\HuiCai`
- **虚拟环境**: `E:\lottery\HuiCai\venv`
- **Python版本**: 3.12.10
- **依赖状态**: ✅ 基础依赖已安装
- **项目结构**: ✅ 完整
- **数据库**: ✅ SQLite支持正常
- **Web框架**: ✅ FastAPI + Uvicorn就绪

## 🚀 快速启动

### 方法一: 使用激活脚本（推荐）
```cmd
# 双击运行或在命令行执行
E:\lottery\HuiCai\activate_env.bat
```

### 方法二: 手动激活
```cmd
# 进入项目目录
cd E:\lottery\HuiCai

# 激活虚拟环境
venv\Scripts\activate

# 现在可以运行HuiCai系统
python main.py
```

## 📱 运行模式

### 1. CLI模式（命令行界面）
```cmd
# 激活环境后运行
python main.py
```
- 交互式命令行界面
- 适合开发和调试
- 支持所有核心功能

### 2. Web模式（浏览器界面）
```cmd
# 激活环境后运行
python start_web.py
```
- 现代化Web界面
- 地址: http://localhost:3000
- API文档: http://localhost:8000/docs

### 3. 功能演示
```cmd
# 激活环境后运行
python demo_stage4.py
```
- 展示第四阶段功能
- Web界面 + 性能优化演示

### 4. 环境测试
```cmd
# 激活环境后运行
python test_environment.py
```
- 检查环境配置
- 验证依赖安装
- 系统健康检查

## 🔧 常用命令

### 虚拟环境管理
```cmd
# 激活虚拟环境
venv\Scripts\activate

# 退出虚拟环境
deactivate

# 查看已安装的包
pip list

# 安装新包
pip install package_name

# 更新包
pip install --upgrade package_name
```

### 项目管理
```cmd
# 安装完整依赖（如果需要深度学习）
pip install -r requirements-local.txt

# 安装最小依赖（快速启动）
pip install -r requirements-minimal.txt

# 运行测试
python test_environment.py

# 查看项目结构
tree /f
```

## 📦 依赖管理

### 当前已安装的核心包
- **numpy** - 数值计算
- **pandas** - 数据处理
- **PyYAML** - 配置文件
- **requests** - HTTP请求
- **beautifulsoup4** - 网页解析
- **scikit-learn** - 机器学习
- **fastapi** - Web框架
- **uvicorn** - ASGI服务器
- **aiosqlite** - 异步SQLite
- **APScheduler** - 任务调度
- **psutil** - 系统监控

### 可选依赖（按需安装）
```cmd
# 深度学习支持
pip install tensorflow>=2.12.0

# 更多数据处理
pip install lxml aiohttp

# 开发工具
pip install pytest pytest-asyncio
```

## 🗂️ 项目结构

```
E:\lottery\HuiCai\
├── venv/                     # 虚拟环境
├── src/                      # 源代码
│   ├── management_layer/     # 管理层
│   ├── data_layer/          # 数据层
│   ├── optimization/        # 性能优化
│   └── web_api/            # Web API
├── web/                     # 前端代码
├── config/                  # 配置文件
├── data/                    # 数据文件
├── scripts/                 # 脚本文件
├── main.py                  # 主程序
├── start_web.py            # Web启动
├── test_environment.py     # 环境测试
├── activate_env.bat        # 激活脚本
├── requirements-minimal.txt # 最小依赖
└── requirements-local.txt  # 完整依赖
```

## ⚠️ 注意事项

### 1. 虚拟环境激活
- 每次使用前必须激活虚拟环境
- 看到 `(venv)` 前缀表示已激活
- 使用 `deactivate` 退出环境

### 2. 路径问题
- 确保在正确的项目目录下运行命令
- 使用绝对路径: `E:\lottery\HuiCai`

### 3. 权限问题
- 如遇权限问题，以管理员身份运行命令行
- 某些杀毒软件可能阻止Python执行

### 4. 依赖冲突
- 如遇包冲突，重新创建虚拟环境
- 使用 `pip install --force-reinstall` 强制重装

## 🔄 环境重建

如果环境出现问题，可以重新创建：

```cmd
# 删除旧环境
rmdir /s venv

# 创建新环境
python -m venv venv

# 激活环境
venv\Scripts\activate

# 安装依赖
pip install -r requirements-minimal.txt

# 测试环境
python test_environment.py
```

## 🎯 下一步

1. **熟悉系统**: 运行 `python demo_stage4.py` 了解功能
2. **开始使用**: 运行 `python main.py` 进入CLI模式
3. **Web体验**: 运行 `python start_web.py` 体验Web界面
4. **深入学习**: 查看源代码和文档

## 📞 获取帮助

如果遇到问题：
1. 运行 `python test_environment.py` 检查环境
2. 查看错误日志和提示信息
3. 检查Python版本和依赖安装
4. 重新创建虚拟环境

---

**🎉 祝您使用愉快！**

*HuiCai慧彩团队*
