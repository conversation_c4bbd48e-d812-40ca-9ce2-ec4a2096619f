# HuiCai 慧彩系统 - 本地部署指南

## 🏠 本地部署概述

HuiCai慧彩系统支持完全本地化部署，无需云服务即可在个人电脑上运行完整的AI彩票分析系统。本地部署版本经过特别优化，适合个人用户使用。

## 📋 系统要求

### 最低配置
- **操作系统**: Windows 10/macOS 10.15/Ubuntu 18.04
- **内存**: 8GB RAM
- **存储**: 10GB 可用空间
- **处理器**: 4核心CPU
- **Python**: 3.8 或更高版本

### 推荐配置
- **操作系统**: Windows 11/macOS 12/Ubuntu 20.04
- **内存**: 16GB RAM
- **存储**: 20GB 可用空间 (SSD推荐)
- **处理器**: 8核心CPU
- **Python**: 3.10 或更高版本

## 🚀 快速安装

### 方法一: 自动安装脚本

#### Windows用户
```cmd
# 1. 下载项目
git clone https://github.com/huicai-team/HuiCai.git
cd HuiCai

# 2. 运行安装脚本
scripts\install_local.bat
```

#### Linux/macOS用户
```bash
# 1. 下载项目
git clone https://github.com/huicai-team/HuiCai.git
cd HuiCai

# 2. 运行安装脚本
chmod +x scripts/install_local.sh
./scripts/install_local.sh
```

### 方法二: 手动安装

#### 1. 环境准备
```bash
# 检查Python版本
python --version  # 需要3.8+

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

#### 2. 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

#### 3. 初始化数据库
```bash
# 运行数据库初始化脚本
python scripts/init_database.py
```

#### 4. 启动系统
```bash
# 启动HuiCai系统
python main.py
```

## 🎯 启动和使用

### 启动系统

#### 使用启动脚本 (推荐)
```bash
# Windows
start_huicai.bat

# Linux/macOS
./start_huicai.sh
```

#### 手动启动
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 启动系统
python main.py
```

### 基本使用

系统启动后，您将看到CLI界面：

```
  _   _       _  _____      _ 
 | | | |     (_)/  __ \    (_)
 | |_| |_   _ _| /  \/ __ _ _ 
 |  _  | | | | | |    / _` | |
 | | | | |_| | | \__/\ (_| | |
 \_| |_/\__,_|_|\____/\__,_|_|

    慧彩智能体系统 v4.0
    本地部署版本

HuiCai> 
```

### 常用命令

```bash
# 查看帮助
HuiCai> help

# 查看系统状态
HuiCai> status

# 传统机器学习预测
HuiCai> predict shuangseqiu

# 中国特色算法预测
HuiCai> predict chinese shuangseqiu

# 深度学习预测
HuiCai> predict deeplearning shuangseqiu

# 综合预测（所有算法集成）
HuiCai> predict comprehensive shuangseqiu

# 查看模型信息
HuiCai> model info shuangseqiu

# 手动爬取数据
HuiCai> crawl shuangseqiu

# 训练模型
HuiCai> train shuangseqiu

# 查看历史数据
HuiCai> data show shuangseqiu

# 退出系统
HuiCai> exit
```

## ⚙️ 配置说明

### 本地配置文件

系统会自动创建 `config/local_config.yaml` 配置文件：

```yaml
# 数据库配置 (使用SQLite)
database:
  type: sqlite
  path: data/huicai.db
  backup_enabled: true

# 深度学习配置 (本地优化)
deep_learning:
  enabled: true
  device: cpu
  batch_size: 16
  enabled_models: ["lstm"]  # 本地默认只启用LSTM

# 本地优化配置
local_optimization:
  memory_limit: 4GB
  cpu_cores: auto
  cache_enabled: true
```

### 性能调优

#### 内存优化
```yaml
# 低内存环境 (8GB以下)
deep_learning:
  batch_size: 8
  lstm:
    hidden_units: 32
cache:
  max_size: 256MB
```

#### CPU优化
```yaml
# 多核CPU优化
local_optimization:
  cpu_cores: 4  # 或 auto
crawler:
  concurrent_limit: 2
```

## 📊 数据管理

### 数据存储位置
- **数据库**: `data/huicai.db` (SQLite)
- **模型文件**: `data/models/`
- **日志文件**: `data/logs/`
- **缓存文件**: `data/cache/`

### 数据备份
```bash
# 手动备份数据库
cp data/huicai.db data/backup/huicai_backup_$(date +%Y%m%d).db

# 备份整个数据目录
tar -czf huicai_data_backup.tar.gz data/
```

### 数据导入导出
```bash
# 导出开奖数据
HuiCai> data export shuangseqiu data/export/shuangseqiu.csv

# 导入开奖数据
HuiCai> data import shuangseqiu data/import/shuangseqiu.csv
```

## 🔧 故障排除

### 常见问题

#### 1. Python版本问题
```bash
# 错误: Python版本过低
# 解决: 升级Python到3.8+
python --version
```

#### 2. 依赖安装失败
```bash
# 错误: 某些包安装失败
# 解决: 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 3. 内存不足
```bash
# 错误: 内存不足导致训练失败
# 解决: 调整配置文件中的batch_size
deep_learning:
  batch_size: 8  # 降低批次大小
```

#### 4. 数据库锁定
```bash
# 错误: database is locked
# 解决: 重启系统或删除锁文件
rm data/huicai.db-wal
rm data/huicai.db-shm
```

#### 5. 端口占用
```bash
# 错误: 端口8000被占用
# 解决: 修改配置文件中的端口
web:
  port: 8001  # 使用其他端口
```

### 日志查看
```bash
# 查看系统日志
tail -f data/logs/huicai.log

# 查看错误日志
grep ERROR data/logs/huicai.log
```

### 重置系统
```bash
# 完全重置 (删除所有数据)
rm -rf data/
python scripts/init_database.py
```

## 🔄 更新升级

### 更新代码
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重新初始化数据库 (如有必要)
python scripts/init_database.py
```

### 版本迁移
```bash
# 备份当前数据
cp data/huicai.db data/backup/

# 运行迁移脚本 (如有)
python scripts/migrate_database.py
```

## 📈 性能监控

### 系统资源监控
```bash
# 查看系统状态
HuiCai> system status

# 查看内存使用
HuiCai> system memory

# 查看模型性能
HuiCai> model performance
```

### 性能优化建议

1. **SSD存储**: 使用SSD可显著提升数据库性能
2. **内存充足**: 16GB内存可获得更好的训练体验
3. **定期清理**: 定期清理日志和缓存文件
4. **模型选择**: 根据硬件配置选择合适的模型

## 🛡️ 安全建议

1. **数据备份**: 定期备份重要数据
2. **访问控制**: 仅在本地网络使用
3. **防火墙**: 配置防火墙规则
4. **更新及时**: 及时更新系统和依赖

## 📞 技术支持

### 获取帮助
- **文档**: 查看项目文档和README
- **日志**: 检查系统日志文件
- **社区**: 在GitHub提交Issue

### 联系方式
- **GitHub**: https://github.com/huicai-team/HuiCai
- **邮箱**: <EMAIL> (虚拟)

## ⚠️ 免责声明

1. **学术用途**: 本系统仅用于学术研究和技术学习
2. **预测准确性**: 不保证预测结果的准确性
3. **投资风险**: 请理性对待彩票，不要过度投注
4. **法律责任**: 使用本系统的风险由用户自行承担

---

**祝您使用愉快！** 🎉

*HuiCai慧彩团队*
