#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成算法
整合多种算法的预测结果，提供综合预测

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import logging
from collections import Counter
import random


class EnsembleAlgorithms:
    """集成算法集合"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.algorithm_weights = {
            'traditional_ml': 0.3,
            'statistical': 0.25,
            'chinese': 0.2,
            'deep_learning': 0.25
        }
        self.prediction_history = []
    
    def weighted_ensemble_prediction(self, predictions: Dict[str, Dict], 
                                   lottery_type: str) -> Dict[str, Any]:
        """
        加权集成预测
        
        Args:
            predictions: 各算法的预测结果
            lottery_type: 彩票类型
            
        Returns:
            集成预测结果
        """
        try:
            if not predictions:
                return self._generate_fallback_prediction(lottery_type)
            
            # 收集所有预测号码
            all_numbers = []
            weighted_scores = {}
            
            for algorithm, prediction in predictions.items():
                if 'numbers' in prediction:
                    numbers = prediction['numbers']
                    confidence = prediction.get('confidence', 0.5)
                    weight = self.algorithm_weights.get(algorithm, 0.1)
                    
                    # 计算加权分数
                    for num in numbers:
                        if num not in weighted_scores:
                            weighted_scores[num] = 0
                        weighted_scores[num] += weight * confidence
                    
                    all_numbers.extend(numbers)
            
            # 根据彩票类型生成最终预测
            if lottery_type == 'shuangseqiu':
                final_numbers = self._ensemble_shuangseqiu(weighted_scores, all_numbers)
            elif lottery_type == 'daletou':
                final_numbers = self._ensemble_daletou(weighted_scores, all_numbers)
            elif lottery_type == 'fucai3d':
                final_numbers = self._ensemble_fucai3d(weighted_scores, all_numbers)
            else:
                final_numbers = self._generate_fallback_prediction(lottery_type)['numbers']
            
            # 计算综合置信度
            avg_confidence = np.mean([p.get('confidence', 0.5) for p in predictions.values()])
            ensemble_confidence = min(avg_confidence * 1.1, 0.95)  # 集成通常能提高置信度
            
            result = {
                'lottery_type': lottery_type,
                'method': 'weighted_ensemble',
                'numbers': final_numbers,
                'confidence': ensemble_confidence,
                'algorithm_count': len(predictions),
                'individual_predictions': predictions,
                'reasoning': f'集成{len(predictions)}种算法的预测结果',
                'timestamp': datetime.now().isoformat()
            }
            
            # 记录预测历史
            self.prediction_history.append(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            return self._generate_fallback_prediction(lottery_type)
    
    def _ensemble_shuangseqiu(self, weighted_scores: Dict[int, float], 
                             all_numbers: List[int]) -> List[int]:
        """双色球集成预测"""
        # 分离红球和蓝球
        red_scores = {k: v for k, v in weighted_scores.items() if 1 <= k <= 33}
        blue_scores = {k: v for k, v in weighted_scores.items() if 1 <= k <= 16}
        
        # 选择得分最高的6个红球
        sorted_reds = sorted(red_scores.items(), key=lambda x: x[1], reverse=True)
        selected_reds = [num for num, score in sorted_reds[:6]]
        
        # 如果不足6个，随机补充
        while len(selected_reds) < 6:
            num = random.randint(1, 33)
            if num not in selected_reds:
                selected_reds.append(num)
        
        selected_reds.sort()
        
        # 选择蓝球
        if blue_scores:
            selected_blue = max(blue_scores.items(), key=lambda x: x[1])[0]
        else:
            selected_blue = random.randint(1, 16)
        
        return selected_reds + [selected_blue]
    
    def _ensemble_daletou(self, weighted_scores: Dict[int, float], 
                         all_numbers: List[int]) -> List[int]:
        """大乐透集成预测"""
        # 分离前区和后区
        front_scores = {k: v for k, v in weighted_scores.items() if 1 <= k <= 35}
        back_scores = {k: v for k, v in weighted_scores.items() if 1 <= k <= 12}
        
        # 选择前区5个号码
        sorted_fronts = sorted(front_scores.items(), key=lambda x: x[1], reverse=True)
        selected_fronts = [num for num, score in sorted_fronts[:5]]
        
        while len(selected_fronts) < 5:
            num = random.randint(1, 35)
            if num not in selected_fronts:
                selected_fronts.append(num)
        
        # 选择后区2个号码
        sorted_backs = sorted(back_scores.items(), key=lambda x: x[1], reverse=True)
        selected_backs = [num for num, score in sorted_backs[:2]]
        
        while len(selected_backs) < 2:
            num = random.randint(1, 12)
            if num not in selected_backs:
                selected_backs.append(num)
        
        return sorted(selected_fronts) + sorted(selected_backs)
    
    def _ensemble_fucai3d(self, weighted_scores: Dict[int, float], 
                         all_numbers: List[int]) -> List[int]:
        """福彩3D集成预测"""
        # 选择得分最高的3个数字
        digit_scores = {k: v for k, v in weighted_scores.items() if 0 <= k <= 9}
        
        if len(digit_scores) >= 3:
            sorted_digits = sorted(digit_scores.items(), key=lambda x: x[1], reverse=True)
            selected_digits = [num for num, score in sorted_digits[:3]]
        else:
            selected_digits = [random.randint(0, 9) for _ in range(3)]
        
        return selected_digits
    
    def voting_ensemble_prediction(self, predictions: Dict[str, Dict], 
                                 lottery_type: str) -> Dict[str, Any]:
        """
        投票集成预测
        
        Args:
            predictions: 各算法的预测结果
            lottery_type: 彩票类型
            
        Returns:
            投票集成结果
        """
        try:
            if not predictions:
                return self._generate_fallback_prediction(lottery_type)
            
            # 统计每个号码的投票数
            vote_counter = Counter()
            
            for algorithm, prediction in predictions.items():
                if 'numbers' in prediction:
                    numbers = prediction['numbers']
                    confidence = prediction.get('confidence', 0.5)
                    
                    # 根据置信度给予不同的投票权重
                    vote_weight = int(confidence * 10)  # 置信度转换为投票权重
                    
                    for num in numbers:
                        vote_counter[num] += vote_weight
            
            # 根据投票结果选择号码
            if lottery_type == 'shuangseqiu':
                final_numbers = self._voting_shuangseqiu(vote_counter)
            elif lottery_type == 'daletou':
                final_numbers = self._voting_daletou(vote_counter)
            elif lottery_type == 'fucai3d':
                final_numbers = self._voting_fucai3d(vote_counter)
            else:
                final_numbers = self._generate_fallback_prediction(lottery_type)['numbers']
            
            # 计算投票一致性
            total_votes = sum(vote_counter.values())
            top_votes = sum(vote_counter[num] for num in final_numbers if num in vote_counter)
            consistency = top_votes / total_votes if total_votes > 0 else 0
            
            result = {
                'lottery_type': lottery_type,
                'method': 'voting_ensemble',
                'numbers': final_numbers,
                'confidence': min(consistency * 0.8, 0.9),
                'vote_distribution': dict(vote_counter.most_common(10)),
                'consistency_score': consistency,
                'reasoning': f'基于{len(predictions)}种算法的投票结果',
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"投票集成失败: {e}")
            return self._generate_fallback_prediction(lottery_type)
    
    def _voting_shuangseqiu(self, vote_counter: Counter) -> List[int]:
        """双色球投票选择"""
        # 分离红球和蓝球投票
        red_votes = {k: v for k, v in vote_counter.items() if 1 <= k <= 33}
        blue_votes = {k: v for k, v in vote_counter.items() if 1 <= k <= 16}
        
        # 选择得票最多的6个红球
        top_reds = [num for num, votes in Counter(red_votes).most_common(6)]
        
        # 补充到6个
        while len(top_reds) < 6:
            num = random.randint(1, 33)
            if num not in top_reds:
                top_reds.append(num)
        
        # 选择得票最多的蓝球
        if blue_votes:
            top_blue = Counter(blue_votes).most_common(1)[0][0]
        else:
            top_blue = random.randint(1, 16)
        
        return sorted(top_reds) + [top_blue]
    
    def _voting_daletou(self, vote_counter: Counter) -> List[int]:
        """大乐透投票选择"""
        front_votes = {k: v for k, v in vote_counter.items() if 1 <= k <= 35}
        back_votes = {k: v for k, v in vote_counter.items() if 1 <= k <= 12}
        
        top_fronts = [num for num, votes in Counter(front_votes).most_common(5)]
        top_backs = [num for num, votes in Counter(back_votes).most_common(2)]
        
        # 补充不足的号码
        while len(top_fronts) < 5:
            num = random.randint(1, 35)
            if num not in top_fronts:
                top_fronts.append(num)
        
        while len(top_backs) < 2:
            num = random.randint(1, 12)
            if num not in top_backs:
                top_backs.append(num)
        
        return sorted(top_fronts) + sorted(top_backs)
    
    def _voting_fucai3d(self, vote_counter: Counter) -> List[int]:
        """福彩3D投票选择"""
        digit_votes = {k: v for k, v in vote_counter.items() if 0 <= k <= 9}
        
        if len(digit_votes) >= 3:
            top_digits = [num for num, votes in Counter(digit_votes).most_common(3)]
        else:
            top_digits = [random.randint(0, 9) for _ in range(3)]
        
        return top_digits
    
    def _generate_fallback_prediction(self, lottery_type: str) -> Dict[str, Any]:
        """生成备用预测"""
        if lottery_type == 'shuangseqiu':
            numbers = sorted(random.sample(range(1, 34), 6)) + [random.randint(1, 16)]
        elif lottery_type == 'daletou':
            numbers = sorted(random.sample(range(1, 36), 5)) + sorted(random.sample(range(1, 13), 2))
        elif lottery_type == 'fucai3d':
            numbers = [random.randint(0, 9) for _ in range(3)]
        else:
            numbers = []
        
        return {
            'lottery_type': lottery_type,
            'method': 'fallback_random',
            'numbers': numbers,
            'confidence': 0.3,
            'reasoning': '备用随机预测',
            'timestamp': datetime.now().isoformat()
        }
    
    def adaptive_ensemble(self, predictions: Dict[str, Dict], 
                         historical_performance: Dict[str, float],
                         lottery_type: str) -> Dict[str, Any]:
        """
        自适应集成预测
        根据历史表现动态调整算法权重
        """
        try:
            # 根据历史表现调整权重
            adjusted_weights = {}
            total_performance = sum(historical_performance.values())
            
            for algorithm in predictions.keys():
                if algorithm in historical_performance and total_performance > 0:
                    adjusted_weights[algorithm] = historical_performance[algorithm] / total_performance
                else:
                    adjusted_weights[algorithm] = 1.0 / len(predictions)
            
            # 使用调整后的权重进行预测
            old_weights = self.algorithm_weights.copy()
            self.algorithm_weights.update(adjusted_weights)
            
            result = self.weighted_ensemble_prediction(predictions, lottery_type)
            result['method'] = 'adaptive_ensemble'
            result['adjusted_weights'] = adjusted_weights
            result['reasoning'] = '基于历史表现的自适应集成预测'
            
            # 恢复原权重
            self.algorithm_weights = old_weights
            
            return result
            
        except Exception as e:
            self.logger.error(f"自适应集成失败: {e}")
            return self._generate_fallback_prediction(lottery_type)
    
    def get_ensemble_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        if not self.prediction_history:
            return {'message': '暂无预测历史'}
        
        recent_predictions = self.prediction_history[-10:]  # 最近10次预测
        
        avg_confidence = np.mean([p['confidence'] for p in recent_predictions])
        method_distribution = Counter([p['method'] for p in recent_predictions])
        
        return {
            'total_predictions': len(self.prediction_history),
            'recent_avg_confidence': avg_confidence,
            'method_distribution': dict(method_distribution),
            'current_weights': self.algorithm_weights,
            'last_prediction_time': recent_predictions[-1]['timestamp'] if recent_predictions else None
        }
