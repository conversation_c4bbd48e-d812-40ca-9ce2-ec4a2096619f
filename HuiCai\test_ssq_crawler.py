#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球爬虫简化测试脚本

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ssq_crawler():
    """测试双色球爬虫"""
    print("🔴 测试双色球爬虫...")
    print("="*50)
    
    try:
        # 导入爬虫模块
        from src.crawler_system.shuangseqiu_crawler import ShuangseqiuCrawler
        print("✅ 双色球爬虫模块导入成功")
        
        # 创建爬虫实例
        crawler = ShuangseqiuCrawler()
        print("✅ 双色球爬虫实例创建成功")
        
        # 显示数据源信息
        print(f"📡 配置的数据源数量: {len(crawler.data_sources)}")
        for i, source in enumerate(crawler.data_sources):
            print(f"  {i+1}. {source['name']} ({source['url']})")
        
        # 测试爬取最新数据
        print("\n📡 开始爬取双色球最新3期数据...")
        result = crawler.crawl_latest_data(3)
        
        print(f"📊 爬取结果状态: {result['status']}")
        
        if result['status'] == 'success':
            print(f"✅ 爬取成功！")
            print(f"📍 数据源: {result['source']}")
            print(f"📈 获取期数: {result['count']}")
            print(f"⏰ 爬取时间: {result['timestamp']}")
            
            # 显示具体数据
            if result['data']:
                print("\n🎯 开奖数据详情:")
                for i, draw in enumerate(result['data'][:3]):  # 只显示前3期
                    issue = draw.get('issue', 'N/A')
                    date = draw.get('date', 'N/A')
                    red_balls = draw.get('red_balls', [])
                    blue_ball = draw.get('blue_ball', 0)
                    source = draw.get('source', 'N/A')
                    
                    if red_balls and blue_ball:
                        red_str = ','.join([f"{ball:02d}" for ball in red_balls])
                        print(f"  期号 {i+1}: {issue} ({date})")
                        print(f"    🔴 开奖号码: {red_str} + {blue_ball:02d}")
                        print(f"    📡 数据来源: {source}")
                    else:
                        print(f"  期号 {i+1}: {issue} - 数据格式异常")
                        print(f"    原始数据: {draw}")
        else:
            print(f"❌ 爬取失败: {result.get('error', '未知错误')}")
        
        # 显示爬虫统计
        print("\n📊 爬虫统计信息:")
        stats = crawler.get_crawler_stats()
        print(f"  - 爬虫类型: {stats['crawler_type']}")
        print(f"  - 当前数据源: {stats['current_source']}")
        print(f"  - 数据源总数: {stats['total_sources']}")
        
        # 显示反爬虫统计
        if 'anti_crawler_stats' in stats:
            ac_stats = stats['anti_crawler_stats']
            print(f"  - 反爬虫统计:")
            if 'throttler' in ac_stats:
                throttler = ac_stats['throttler']
                print(f"    • 总请求数: {throttler.get('total_requests', 0)}")
                print(f"    • 每分钟请求数: {throttler.get('requests_per_minute', 0):.2f}")
            if 'errors' in ac_stats:
                errors = ac_stats['errors']
                print(f"    • 总错误数: {errors.get('total_errors', 0)}")
        
        # 关闭爬虫
        crawler.close()
        print("\n✅ 双色球爬虫测试完成")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装必要的依赖包")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 HuiCai双色球爬虫测试")
    print("="*50)
    
    # 设置日志级别为WARNING，减少输出
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 测试双色球爬虫
    success = test_ssq_crawler()
    
    if success:
        print("\n🎉 测试成功完成！")
        print("="*50)
        print("📝 测试总结:")
        print("  ✅ 双色球爬虫模块正常工作")
        print("  ✅ API接口访问正常")
        print("  ✅ 数据解析功能正常")
        print("  ✅ 反爬虫机制正常")
    else:
        print("\n❌ 测试失败")
        print("请检查网络连接和依赖包安装")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
