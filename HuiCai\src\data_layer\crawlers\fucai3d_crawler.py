#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D爬虫
专门爬取福彩3D开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
import logging

from .base_crawler import BaseCrawler


class Fucai3dCrawler(BaseCrawler):
    """福彩3D爬虫"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """初始化福彩3D爬虫"""
        super().__init__(config_manager, logger, 'fucai3d')
        
        # 福彩3D特定配置
        self.data_sources = self.lottery_config.get('data_source', {})
        self.primary_url = self.data_sources.get('primary', 'https://www.cwl.gov.cn/kjxx/fc3d/')
        self.backup_urls = self.data_sources.get('backup', [])
        
        # 数字范围配置
        self.number_range = self.lottery_config.get('number_range', [0, 9])
        self.number_count = self.lottery_config.get('number_count', 3)
    
    async def crawl_latest_draw(self) -> Optional[Dict[str, Any]]:
        """爬取最新开奖数据"""
        try:
            self.logger.info("开始爬取福彩3D最新开奖数据")
            
            # 尝试主要数据源
            result = await self._crawl_from_primary_source()
            if result:
                return result
            
            # 尝试备用数据源
            for backup_url in self.backup_urls:
                self.logger.info(f"尝试备用数据源: {backup_url}")
                result = await self._crawl_from_backup_source(backup_url)
                if result:
                    return result
            
            self.logger.error("所有数据源都无法获取最新开奖数据")
            return None
            
        except Exception as e:
            self.logger.error(f"爬取最新开奖数据失败: {e}")
            return None
    
    async def _crawl_from_primary_source(self) -> Optional[Dict[str, Any]]:
        """从主要数据源爬取数据"""
        try:
            html = await self.get_html(self.primary_url)
            if not html:
                return None
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找开奖信息容器
            draw_container = soup.find('div', class_='kjjg')
            if not draw_container:
                self.logger.warning("未找到开奖结果容器")
                return None
            
            # 提取期号
            period_elem = draw_container.find('span', class_='qi')
            if not period_elem:
                self.logger.warning("未找到期号信息")
                return None
            
            period_text = period_elem.get_text(strip=True)
            period_match = re.search(r'(\d{7})', period_text)
            if not period_match:
                self.logger.warning("无法解析期号")
                return None
            
            draw_number = period_match.group(1)
            
            # 提取开奖日期
            date_elem = draw_container.find('span', class_='rq')
            if not date_elem:
                self.logger.warning("未找到开奖日期")
                return None
            
            date_text = date_elem.get_text(strip=True)
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', date_text)
            if not date_match:
                self.logger.warning("无法解析开奖日期")
                return None
            
            draw_date = datetime.strptime(date_match.group(1), '%Y-%m-%d').date()
            
            # 提取开奖号码
            numbers = []
            
            # 福彩3D的号码通常用特定的class标识
            number_elems = draw_container.find_all('span', class_='number')
            for elem in number_elems:
                try:
                    number = int(elem.get_text(strip=True))
                    if self.number_range[0] <= number <= self.number_range[1]:
                        numbers.append(number)
                except ValueError:
                    continue
            
            # 验证数据
            if len(numbers) != self.number_count:
                self.logger.warning(f"号码数量不正确: {len(numbers)}")
                return None
            
            # 计算3D特有的统计信息
            sum_value = sum(numbers)
            span_value = max(numbers) - min(numbers)
            
            # 判断形态
            form_type = self._analyze_form_type(numbers)
            
            return {
                'lottery_type': self.lottery_type,
                'draw_date': draw_date,
                'draw_number': draw_number,
                'numbers': numbers,
                'sum_value': sum_value,
                'span_value': span_value,
                'form_type': form_type,
                'source': 'primary'
            }
            
        except Exception as e:
            self.logger.error(f"从主要数据源爬取失败: {e}")
            return None
    
    def _analyze_form_type(self, numbers: List[int]) -> str:
        """分析3D号码形态"""
        if len(numbers) != 3:
            return "未知"
        
        # 豹子号（三个数字相同）
        if numbers[0] == numbers[1] == numbers[2]:
            return "豹子"
        
        # 组三（两个数字相同）
        if (numbers[0] == numbers[1] or 
            numbers[0] == numbers[2] or 
            numbers[1] == numbers[2]):
            return "组三"
        
        # 组六（三个数字都不同）
        return "组六"
    
    async def _crawl_from_backup_source(self, url: str) -> Optional[Dict[str, Any]]:
        """从备用数据源爬取数据"""
        try:
            self.logger.info(f"尝试从备用源爬取: {url}")
            
            # 示例：500彩票网的API接口
            if '500.com' in url:
                return await self._crawl_from_500_com()
            
            return None
            
        except Exception as e:
            self.logger.error(f"从备用数据源爬取失败: {e}")
            return None
    
    async def _crawl_from_500_com(self) -> Optional[Dict[str, Any]]:
        """从500彩票网爬取数据"""
        try:
            # 500彩票网的API接口
            api_url = "https://datachart.500.com/fc3d/history/newinc/history.php"
            params = {
                'limit': '1',
                'sort': 'desc'
            }
            
            json_data = await self.get_json(api_url, params=params)
            if not json_data or 'data' not in json_data:
                return None
            
            data = json_data['data']
            if not data:
                return None
            
            latest_draw = data[0]
            
            # 解析数据
            draw_number = latest_draw.get('expect', '')
            draw_date_str = latest_draw.get('opentime', '')
            numbers_str = latest_draw.get('opencode', '')
            
            if not all([draw_number, draw_date_str, numbers_str]):
                return None
            
            # 解析日期
            draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d').date()
            
            # 解析号码
            numbers = [int(x) for x in numbers_str.split(',')]
            if len(numbers) != 3:
                return None
            
            # 计算统计信息
            sum_value = sum(numbers)
            span_value = max(numbers) - min(numbers)
            form_type = self._analyze_form_type(numbers)
            
            return {
                'lottery_type': self.lottery_type,
                'draw_date': draw_date,
                'draw_number': draw_number,
                'numbers': numbers,
                'sum_value': sum_value,
                'span_value': span_value,
                'form_type': form_type,
                'source': 'backup_500com'
            }
            
        except Exception as e:
            self.logger.error(f"从500彩票网爬取失败: {e}")
            return None
    
    async def crawl_historical_draws(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """爬取历史开奖数据"""
        try:
            self.logger.info(f"开始爬取福彩3D历史数据: {start_date} 到 {end_date}")
            
            results = []
            current_date = end_date
            
            while current_date >= start_date:
                # 这里可以实现按日期爬取历史数据的逻辑
                # 由于篇幅限制，这里只是示例框架
                
                # 模拟爬取一天的数据
                daily_data = await self._crawl_daily_data(current_date)
                if daily_data:
                    results.append(daily_data)
                
                current_date -= timedelta(days=1)
                
                # 避免请求过于频繁
                await self._delay_request()
            
            self.logger.info(f"爬取历史数据完成，共获取 {len(results)} 条记录")
            return results
            
        except Exception as e:
            self.logger.error(f"爬取历史数据失败: {e}")
            return []
    
    async def _crawl_daily_data(self, target_date: date) -> Optional[Dict[str, Any]]:
        """爬取指定日期的开奖数据"""
        # 这里实现具体的日期数据爬取逻辑
        # 返回格式与crawl_latest_draw相同
        pass
    
    def parse_draw_data(self, raw_data: Any) -> Optional[Dict[str, Any]]:
        """解析开奖数据"""
        try:
            if isinstance(raw_data, dict):
                return raw_data
            
            # 如果是其他格式的原始数据，在这里进行解析
            # 返回标准格式的数据
            
            return None
            
        except Exception as e:
            self.logger.error(f"解析开奖数据失败: {e}")
            return None
    
    def validate_numbers(self, numbers: List[int]) -> bool:
        """验证号码有效性"""
        # 验证号码数量
        if len(numbers) != self.number_count:
            return False
        
        # 验证号码范围
        for number in numbers:
            if not (self.number_range[0] <= number <= self.number_range[1]):
                return False
        
        return True
    
    def analyze_3d_patterns(self, historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析3D号码模式"""
        try:
            if not historical_data:
                return {}
            
            # 统计各种形态的出现次数
            form_stats = {'豹子': 0, '组三': 0, '组六': 0}
            sum_stats = {}
            span_stats = {}
            position_stats = [{'0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0, '9': 0} for _ in range(3)]
            
            for draw in historical_data:
                # 形态统计
                form_type = draw.get('form_type', '未知')
                if form_type in form_stats:
                    form_stats[form_type] += 1
                
                # 和值统计
                sum_value = draw.get('sum_value', 0)
                sum_stats[sum_value] = sum_stats.get(sum_value, 0) + 1
                
                # 跨度统计
                span_value = draw.get('span_value', 0)
                span_stats[span_value] = span_stats.get(span_value, 0) + 1
                
                # 位置统计
                numbers = draw.get('numbers', [])
                for i, num in enumerate(numbers):
                    if i < 3:
                        position_stats[i][str(num)] += 1
            
            total_draws = len(historical_data)
            
            # 计算概率
            form_probabilities = {form: count/total_draws for form, count in form_stats.items()}
            
            # 找出热号和冷号
            hot_numbers = []
            cold_numbers = []
            
            for pos in range(3):
                pos_stats = position_stats[pos]
                sorted_stats = sorted(pos_stats.items(), key=lambda x: x[1], reverse=True)
                hot_numbers.append([int(num) for num, _ in sorted_stats[:3]])  # 前3个热号
                cold_numbers.append([int(num) for num, _ in sorted_stats[-3:]])  # 后3个冷号
            
            return {
                'form_statistics': form_stats,
                'form_probabilities': form_probabilities,
                'sum_statistics': sum_stats,
                'span_statistics': span_stats,
                'position_statistics': position_stats,
                'hot_numbers': hot_numbers,
                'cold_numbers': cold_numbers,
                'total_draws': total_draws
            }
            
        except Exception as e:
            self.logger.error(f"分析3D模式失败: {e}")
            return {}
