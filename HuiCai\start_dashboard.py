#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai仪表盘启动脚本
一键启动新的仪表盘界面

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path
import webbrowser
import time
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    try:
        # 检查核心模块
        from src.crawler_system.crawler_manager import CrawlerManager
        from src.database.local_database_manager import LocalDatabaseManager
        print("✅ 核心模块检查通过")
        
        # 检查数据库
        db_manager = LocalDatabaseManager()
        db_manager.close_all_connections()
        print("✅ 数据库连接正常")
        
        # 检查模板文件
        template_path = project_root / 'dashboard_template.html'
        if template_path.exists():
            print("✅ 仪表盘模板文件存在")
        else:
            print("❌ 仪表盘模板文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def open_browser_delayed(url, delay=3):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 已在浏览器中打开: {url}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def main():
    """主函数"""
    print("🎯 HuiCai慧彩智能体 - 仪表盘启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请检查系统配置")
        return 1
    
    print("\n✅ 系统检查通过，准备启动仪表盘...")
    
    # 设置端口
    port = 8000
    url = f"http://localhost:{port}/dashboard"
    
    print(f"\n🚀 启动信息:")
    print(f"  📍 端口: {port}")
    print(f"  🌐 访问地址: {url}")
    print(f"  📊 仪表盘功能:")
    print(f"    - 四个算法卡片显示")
    print(f"    - 双色球最新开奖和预测")
    print(f"    - 大乐透最新开奖和预测")
    print(f"    - 实时倒计时显示")
    print(f"    - 一键更新数据功能")
    
    # 延迟打开浏览器
    open_browser_delayed(url, delay=3)
    
    print(f"\n🔄 正在启动服务器...")
    print(f"💡 提示: 服务器启动后会自动打开浏览器")
    print(f"🛑 按 Ctrl+C 可停止服务器")
    print("=" * 50)
    
    try:
        # 启动仪表盘服务器
        from dashboard_server import run_dashboard_server
        run_dashboard_server(port)
        
    except KeyboardInterrupt:
        print(f"\n✅ 仪表盘已关闭")
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
