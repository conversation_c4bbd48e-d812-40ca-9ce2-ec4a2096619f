import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { systemAPI } from '../../services/api';

export interface SystemInfo {
  platform: string;
  python_version: string;
  cpu_count: number;
  memory_total: number;
  disk_free: number;
  local_mode: boolean;
}

export interface SystemStatus {
  status: 'running' | 'stopped' | 'error';
  uptime: number;
  cpu_usage: number;
  memory_usage: number;
  active_models: number;
  last_prediction: string;
}

interface SystemState {
  info: SystemInfo | null;
  status: SystemStatus | null;
  loading: boolean;
  error: string | null;
  logs: string[];
}

const initialState: SystemState = {
  info: null,
  status: null,
  loading: false,
  error: null,
  logs: [],
};

// 异步thunks
export const fetchSystemInfo = createAsyncThunk(
  'system/fetchInfo',
  async () => {
    const response = await systemAPI.getSystemInfo();
    return response.data;
  }
);

export const fetchSystemStatus = createAsyncThunk(
  'system/fetchStatus',
  async () => {
    const response = await systemAPI.getSystemStatus();
    return response.data;
  }
);

export const fetchSystemLogs = createAsyncThunk(
  'system/fetchLogs',
  async (params: { level?: string; limit?: number }) => {
    const response = await systemAPI.getSystemLogs(params);
    return response.data;
  }
);

const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addLog: (state, action: PayloadAction<string>) => {
      state.logs.unshift(action.payload);
      if (state.logs.length > 1000) {
        state.logs = state.logs.slice(0, 1000);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchSystemInfo
      .addCase(fetchSystemInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.info = action.payload;
      })
      .addCase(fetchSystemInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取系统信息失败';
      })
      // fetchSystemStatus
      .addCase(fetchSystemStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSystemStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.status = action.payload;
      })
      .addCase(fetchSystemStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取系统状态失败';
      })
      // fetchSystemLogs
      .addCase(fetchSystemLogs.fulfilled, (state, action) => {
        state.logs = action.payload;
      });
  },
});

export const { clearError, addLog } = systemSlice.actions;
export default systemSlice.reducer;
