#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据备份和恢复管理器
提供自动备份、手动备份、数据恢复等功能

Author: HuiCai Team
Date: 2025-01-15
"""

import os
import shutil
import sqlite3
import json
import gzip
import pickle
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import threading
import schedule
import time


class BackupManager:
    """备份管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        self.config = config_manager
        self.logger = logger
        self.backup_root = Path("backups")
        self.backup_root.mkdir(exist_ok=True)
        
        # 备份配置
        self.backup_config = {
            'auto_backup_enabled': True,
            'backup_interval_hours': 24,
            'max_backup_files': 30,
            'compress_backups': True,
            'backup_types': ['database', 'models', 'config', 'logs']
        }
        
        # 备份线程
        self.backup_thread = None
        self.stop_backup_thread = False
        
        self._initialize_backup_schedule()
    
    def _initialize_backup_schedule(self):
        """初始化备份调度"""
        if self.backup_config['auto_backup_enabled']:
            # 每天凌晨2点自动备份
            schedule.every().day.at("02:00").do(self.create_full_backup)
            
            # 启动调度线程
            self.backup_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.backup_thread.start()
            
            self.logger.info("自动备份调度已启动")
    
    def _run_scheduler(self):
        """运行备份调度器"""
        while not self.stop_backup_thread:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def create_full_backup(self, backup_name: str = None) -> Dict[str, Any]:
        """
        创建完整备份
        
        Args:
            backup_name: 备份名称，如果为None则自动生成
            
        Returns:
            备份结果
        """
        try:
            if backup_name is None:
                backup_name = f"full_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            backup_dir = self.backup_root / backup_name
            backup_dir.mkdir(exist_ok=True)
            
            backup_info = {
                'backup_name': backup_name,
                'backup_time': datetime.now().isoformat(),
                'backup_type': 'full',
                'components': {},
                'total_size': 0,
                'status': 'in_progress'
            }
            
            # 备份数据库
            if 'database' in self.backup_config['backup_types']:
                db_result = self._backup_database(backup_dir)
                backup_info['components']['database'] = db_result
            
            # 备份模型
            if 'models' in self.backup_config['backup_types']:
                model_result = self._backup_models(backup_dir)
                backup_info['components']['models'] = model_result
            
            # 备份配置
            if 'config' in self.backup_config['backup_types']:
                config_result = self._backup_config(backup_dir)
                backup_info['components']['config'] = config_result
            
            # 备份日志
            if 'logs' in self.backup_config['backup_types']:
                log_result = self._backup_logs(backup_dir)
                backup_info['components']['logs'] = log_result
            
            # 计算总大小
            backup_info['total_size'] = self._calculate_directory_size(backup_dir)
            
            # 压缩备份（如果启用）
            if self.backup_config['compress_backups']:
                compressed_file = self._compress_backup(backup_dir)
                backup_info['compressed_file'] = str(compressed_file)
                backup_info['compressed_size'] = compressed_file.stat().st_size
                
                # 删除原始目录
                shutil.rmtree(backup_dir)
            
            # 保存备份信息
            backup_info['status'] = 'completed'
            self._save_backup_info(backup_info)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            self.logger.info(f"完整备份创建成功: {backup_name}")
            return backup_info
            
        except Exception as e:
            self.logger.error(f"创建完整备份失败: {e}")
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
            return backup_info
    
    def _backup_database(self, backup_dir: Path) -> Dict[str, Any]:
        """备份数据库"""
        try:
            db_backup_dir = backup_dir / "database"
            db_backup_dir.mkdir(exist_ok=True)
            
            # 获取数据库文件路径
            db_path = Path("data/lottery.db")
            
            if db_path.exists():
                # 创建数据库备份
                backup_db_path = db_backup_dir / "lottery_backup.db"
                
                # 使用SQLite的备份API
                source_conn = sqlite3.connect(str(db_path))
                backup_conn = sqlite3.connect(str(backup_db_path))
                
                source_conn.backup(backup_conn)
                
                source_conn.close()
                backup_conn.close()
                
                # 导出为SQL文件
                sql_backup_path = db_backup_dir / "lottery_backup.sql"
                self._export_database_to_sql(db_path, sql_backup_path)
                
                return {
                    'status': 'success',
                    'files': [str(backup_db_path), str(sql_backup_path)],
                    'size': backup_db_path.stat().st_size + sql_backup_path.stat().st_size
                }
            else:
                return {
                    'status': 'skipped',
                    'reason': 'Database file not found'
                }
                
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _backup_models(self, backup_dir: Path) -> Dict[str, Any]:
        """备份模型文件"""
        try:
            model_backup_dir = backup_dir / "models"
            model_backup_dir.mkdir(exist_ok=True)
            
            models_dir = Path("models")
            backed_up_files = []
            total_size = 0
            
            if models_dir.exists():
                for model_file in models_dir.rglob("*.pkl"):
                    backup_file = model_backup_dir / model_file.name
                    shutil.copy2(model_file, backup_file)
                    backed_up_files.append(str(backup_file))
                    total_size += backup_file.stat().st_size
                
                for model_file in models_dir.rglob("*.h5"):
                    backup_file = model_backup_dir / model_file.name
                    shutil.copy2(model_file, backup_file)
                    backed_up_files.append(str(backup_file))
                    total_size += backup_file.stat().st_size
            
            return {
                'status': 'success',
                'files': backed_up_files,
                'count': len(backed_up_files),
                'size': total_size
            }
            
        except Exception as e:
            self.logger.error(f"模型备份失败: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _backup_config(self, backup_dir: Path) -> Dict[str, Any]:
        """备份配置文件"""
        try:
            config_backup_dir = backup_dir / "config"
            config_backup_dir.mkdir(exist_ok=True)
            
            config_dir = Path("config")
            backed_up_files = []
            total_size = 0
            
            if config_dir.exists():
                for config_file in config_dir.rglob("*.yaml"):
                    backup_file = config_backup_dir / config_file.name
                    shutil.copy2(config_file, backup_file)
                    backed_up_files.append(str(backup_file))
                    total_size += backup_file.stat().st_size
                
                for config_file in config_dir.rglob("*.json"):
                    backup_file = config_backup_dir / config_file.name
                    shutil.copy2(config_file, backup_file)
                    backed_up_files.append(str(backup_file))
                    total_size += backup_file.stat().st_size
            
            return {
                'status': 'success',
                'files': backed_up_files,
                'count': len(backed_up_files),
                'size': total_size
            }
            
        except Exception as e:
            self.logger.error(f"配置备份失败: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _backup_logs(self, backup_dir: Path) -> Dict[str, Any]:
        """备份日志文件"""
        try:
            log_backup_dir = backup_dir / "logs"
            log_backup_dir.mkdir(exist_ok=True)
            
            logs_dir = Path("logs")
            backed_up_files = []
            total_size = 0
            
            if logs_dir.exists():
                # 只备份最近7天的日志
                cutoff_date = datetime.now() - timedelta(days=7)
                
                for log_file in logs_dir.rglob("*.log"):
                    if log_file.stat().st_mtime > cutoff_date.timestamp():
                        backup_file = log_backup_dir / log_file.name
                        shutil.copy2(log_file, backup_file)
                        backed_up_files.append(str(backup_file))
                        total_size += backup_file.stat().st_size
            
            return {
                'status': 'success',
                'files': backed_up_files,
                'count': len(backed_up_files),
                'size': total_size
            }
            
        except Exception as e:
            self.logger.error(f"日志备份失败: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _export_database_to_sql(self, db_path: Path, sql_path: Path):
        """导出数据库为SQL文件"""
        try:
            conn = sqlite3.connect(str(db_path))
            
            with open(sql_path, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write(f"{line}\n")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"导出数据库为SQL失败: {e}")
            raise
    
    def _compress_backup(self, backup_dir: Path) -> Path:
        """压缩备份目录"""
        try:
            compressed_file = backup_dir.with_suffix('.tar.gz')
            
            import tarfile
            with tarfile.open(compressed_file, 'w:gz') as tar:
                tar.add(backup_dir, arcname=backup_dir.name)
            
            return compressed_file
            
        except Exception as e:
            self.logger.error(f"压缩备份失败: {e}")
            raise
    
    def _calculate_directory_size(self, directory: Path) -> int:
        """计算目录大小"""
        total_size = 0
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size
    
    def _save_backup_info(self, backup_info: Dict[str, Any]):
        """保存备份信息"""
        try:
            info_file = self.backup_root / f"{backup_info['backup_name']}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存备份信息失败: {e}")
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            # 获取所有备份文件
            backup_files = []
            
            for file_path in self.backup_root.iterdir():
                if file_path.suffix in ['.tar.gz', '.zip']:
                    backup_files.append((file_path, file_path.stat().st_mtime))
            
            # 按时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出限制的备份
            max_backups = self.backup_config['max_backup_files']
            if len(backup_files) > max_backups:
                for file_path, _ in backup_files[max_backups:]:
                    file_path.unlink()
                    
                    # 删除对应的信息文件
                    info_file = file_path.with_name(f"{file_path.stem}_info.json")
                    if info_file.exists():
                        info_file.unlink()
                    
                    self.logger.info(f"删除旧备份: {file_path.name}")
                    
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
    
    def restore_from_backup(self, backup_name: str, components: List[str] = None) -> Dict[str, Any]:
        """
        从备份恢复数据
        
        Args:
            backup_name: 备份名称
            components: 要恢复的组件列表，None表示恢复所有
            
        Returns:
            恢复结果
        """
        try:
            # 查找备份文件
            backup_file = None
            for file_path in self.backup_root.iterdir():
                if file_path.stem == backup_name and file_path.suffix in ['.tar.gz', '.zip']:
                    backup_file = file_path
                    break
            
            if not backup_file:
                return {
                    'status': 'failed',
                    'error': f'备份文件不存在: {backup_name}'
                }
            
            # 解压备份
            temp_dir = self.backup_root / f"temp_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            temp_dir.mkdir(exist_ok=True)
            
            import tarfile
            with tarfile.open(backup_file, 'r:gz') as tar:
                tar.extractall(temp_dir)
            
            # 获取解压后的目录
            extracted_dir = temp_dir / backup_name
            
            restore_results = {}
            
            # 恢复各组件
            if components is None:
                components = ['database', 'models', 'config']
            
            for component in components:
                if component == 'database':
                    restore_results['database'] = self._restore_database(extracted_dir)
                elif component == 'models':
                    restore_results['models'] = self._restore_models(extracted_dir)
                elif component == 'config':
                    restore_results['config'] = self._restore_config(extracted_dir)
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            
            self.logger.info(f"从备份恢复完成: {backup_name}")
            
            return {
                'status': 'success',
                'backup_name': backup_name,
                'restored_components': restore_results,
                'restore_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"从备份恢复失败: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def _restore_database(self, backup_dir: Path) -> Dict[str, Any]:
        """恢复数据库"""
        try:
            db_backup_dir = backup_dir / "database"
            
            if not db_backup_dir.exists():
                return {'status': 'skipped', 'reason': 'No database backup found'}
            
            # 恢复数据库文件
            backup_db_file = db_backup_dir / "lottery_backup.db"
            target_db_file = Path("data/lottery.db")
            
            if backup_db_file.exists():
                # 备份当前数据库
                if target_db_file.exists():
                    backup_current = target_db_file.with_suffix('.db.backup')
                    shutil.copy2(target_db_file, backup_current)
                
                # 恢复数据库
                target_db_file.parent.mkdir(exist_ok=True)
                shutil.copy2(backup_db_file, target_db_file)
                
                return {
                    'status': 'success',
                    'restored_file': str(target_db_file)
                }
            else:
                return {'status': 'failed', 'error': 'Backup database file not found'}
                
        except Exception as e:
            return {'status': 'failed', 'error': str(e)}
    
    def _restore_models(self, backup_dir: Path) -> Dict[str, Any]:
        """恢复模型文件"""
        try:
            model_backup_dir = backup_dir / "models"
            
            if not model_backup_dir.exists():
                return {'status': 'skipped', 'reason': 'No model backup found'}
            
            models_dir = Path("models")
            models_dir.mkdir(exist_ok=True)
            
            restored_files = []
            
            for backup_file in model_backup_dir.iterdir():
                if backup_file.is_file():
                    target_file = models_dir / backup_file.name
                    shutil.copy2(backup_file, target_file)
                    restored_files.append(str(target_file))
            
            return {
                'status': 'success',
                'restored_files': restored_files,
                'count': len(restored_files)
            }
            
        except Exception as e:
            return {'status': 'failed', 'error': str(e)}
    
    def _restore_config(self, backup_dir: Path) -> Dict[str, Any]:
        """恢复配置文件"""
        try:
            config_backup_dir = backup_dir / "config"
            
            if not config_backup_dir.exists():
                return {'status': 'skipped', 'reason': 'No config backup found'}
            
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            
            restored_files = []
            
            for backup_file in config_backup_dir.iterdir():
                if backup_file.is_file():
                    target_file = config_dir / backup_file.name
                    shutil.copy2(backup_file, target_file)
                    restored_files.append(str(target_file))
            
            return {
                'status': 'success',
                'restored_files': restored_files,
                'count': len(restored_files)
            }
            
        except Exception as e:
            return {'status': 'failed', 'error': str(e)}
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        backups = []
        
        for info_file in self.backup_root.glob("*_info.json"):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                    backups.append(backup_info)
            except Exception as e:
                self.logger.error(f"读取备份信息失败: {info_file}, {e}")
        
        # 按时间排序
        backups.sort(key=lambda x: x.get('backup_time', ''), reverse=True)
        
        return backups
    
    def get_backup_status(self) -> Dict[str, Any]:
        """获取备份状态"""
        backups = self.list_backups()
        
        total_size = 0
        for file_path in self.backup_root.iterdir():
            if file_path.suffix in ['.tar.gz', '.zip']:
                total_size += file_path.stat().st_size
        
        return {
            'auto_backup_enabled': self.backup_config['auto_backup_enabled'],
            'total_backups': len(backups),
            'total_size_mb': total_size / (1024 * 1024),
            'latest_backup': backups[0] if backups else None,
            'backup_directory': str(self.backup_root),
            'max_backup_files': self.backup_config['max_backup_files']
        }
    
    def stop_auto_backup(self):
        """停止自动备份"""
        self.stop_backup_thread = True
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
        self.logger.info("自动备份已停止")
