#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫管理器
负责管理和调度所有彩票爬虫

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
from typing import Dict, List, Any, Optional, Type
import logging
from datetime import datetime, date, timedelta
import importlib
import inspect

from .crawlers.base_crawler import BaseCrawler
from .crawlers.shuangseqiu_crawler import ShuangseqiuCrawler
from .crawlers.daletou_crawler import DaletouCrawler
from .crawlers.fucai3d_crawler import Fucai3dCrawler
from .data_storage import DataStorage


class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化爬虫管理器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 爬虫实例字典
        self.crawlers: Dict[str, BaseCrawler] = {}
        
        # 数据存储
        self.data_storage = DataStorage(config_manager, logger)
        
        # 爬虫配置
        self.crawler_config = config_manager.get('crawler', {})
        self.concurrent_limit = self.crawler_config.get('concurrent_limit', 5)
        
        # 任务队列
        self.task_queue = asyncio.Queue()
        self.running_tasks = set()
        
        # 状态标志
        self.is_running = False
        
        # 爬虫类映射
        self.crawler_classes = {
            'shuangseqiu': ShuangseqiuCrawler,
            'daletou': DaletouCrawler,
            'fucai3d': Fucai3dCrawler,
            # 'kuai3': Kuai3Crawler,     # 待实现
            # 'shishicai': ShishicaiCrawler,  # 待实现
        }
    
    async def initialize(self):
        """初始化爬虫管理器"""
        try:
            # 初始化数据存储
            await self.data_storage.initialize()
            
            # 初始化所有爬虫
            await self._initialize_crawlers()
            
            self.logger.info("爬虫管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"爬虫管理器初始化失败: {e}")
            raise
    
    async def _initialize_crawlers(self):
        """初始化所有爬虫"""
        # 获取所有彩票配置
        lottery_configs = self.config_manager.lottery_configs
        
        for lottery_type in lottery_configs.keys():
            if lottery_type in self.crawler_classes:
                try:
                    # 创建爬虫实例
                    crawler_class = self.crawler_classes[lottery_type]
                    crawler = crawler_class(self.config_manager, self.logger)
                    
                    # 初始化爬虫
                    await crawler.initialize()
                    
                    # 添加到管理器
                    self.crawlers[lottery_type] = crawler
                    
                    self.logger.info(f"初始化 {lottery_type} 爬虫成功")
                    
                except Exception as e:
                    self.logger.error(f"初始化 {lottery_type} 爬虫失败: {e}")
    
    async def start(self):
        """启动爬虫管理器"""
        if self.is_running:
            self.logger.warning("爬虫管理器已在运行")
            return
        
        self.is_running = True
        self.logger.info("启动爬虫管理器")
        
        # 启动任务处理器
        asyncio.create_task(self._task_processor())
    
    async def stop(self):
        """停止爬虫管理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.logger.info("停止爬虫管理器")
        
        # 等待所有任务完成
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks, return_exceptions=True)
        
        # 关闭所有爬虫
        for crawler in self.crawlers.values():
            await crawler.close()
        
        # 关闭数据存储
        await self.data_storage.close()
    
    async def _task_processor(self):
        """任务处理器"""
        while self.is_running:
            try:
                # 控制并发数量
                if len(self.running_tasks) >= self.concurrent_limit:
                    await asyncio.sleep(1)
                    continue
                
                # 获取任务
                try:
                    task_data = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # 创建并启动任务
                task = asyncio.create_task(self._execute_task(task_data))
                self.running_tasks.add(task)
                
                # 清理完成的任务
                self.running_tasks = {t for t in self.running_tasks if not t.done()}
                
            except Exception as e:
                self.logger.error(f"任务处理器错误: {e}")
    
    async def _execute_task(self, task_data: Dict[str, Any]):
        """执行爬虫任务"""
        try:
            task_type = task_data.get('type')
            lottery_type = task_data.get('lottery_type')
            
            if lottery_type not in self.crawlers:
                self.logger.error(f"未找到 {lottery_type} 爬虫")
                return
            
            crawler = self.crawlers[lottery_type]
            
            if task_type == 'latest':
                await self._crawl_latest_data(crawler, lottery_type)
            elif task_type == 'historical':
                start_date = task_data.get('start_date')
                end_date = task_data.get('end_date')
                await self._crawl_historical_data(crawler, lottery_type, start_date, end_date)
            else:
                self.logger.error(f"未知任务类型: {task_type}")
                
        except Exception as e:
            self.logger.error(f"执行爬虫任务失败: {e}")
    
    async def _crawl_latest_data(self, crawler: BaseCrawler, lottery_type: str):
        """爬取最新数据"""
        try:
            self.logger.info(f"开始爬取 {lottery_type} 最新数据")
            
            # 爬取数据
            draw_data = await crawler.crawl_latest_draw()
            if not draw_data:
                self.logger.warning(f"{lottery_type} 最新数据爬取失败")
                return
            
            # 保存数据
            success = await self.data_storage.save_lottery_draw(
                lottery_type=draw_data['lottery_type'],
                draw_date=draw_data['draw_date'],
                draw_number=draw_data['draw_number'],
                numbers=draw_data['numbers'],
                extra_info={
                    'red_balls': draw_data.get('red_balls'),
                    'blue_balls': draw_data.get('blue_balls'),
                    'source': draw_data.get('source')
                }
            )
            
            if success:
                self.logger.info(f"{lottery_type} 最新数据保存成功: {draw_data['draw_number']}")
            else:
                self.logger.error(f"{lottery_type} 最新数据保存失败")
                
        except Exception as e:
            self.logger.error(f"爬取 {lottery_type} 最新数据失败: {e}")
    
    async def _crawl_historical_data(self, crawler: BaseCrawler, lottery_type: str, 
                                   start_date: date, end_date: date):
        """爬取历史数据"""
        try:
            self.logger.info(f"开始爬取 {lottery_type} 历史数据: {start_date} 到 {end_date}")
            
            # 爬取数据
            historical_data = await crawler.crawl_historical_draws(start_date, end_date)
            
            if not historical_data:
                self.logger.warning(f"{lottery_type} 历史数据爬取失败")
                return
            
            # 批量保存数据
            success_count = 0
            for draw_data in historical_data:
                success = await self.data_storage.save_lottery_draw(
                    lottery_type=draw_data['lottery_type'],
                    draw_date=draw_data['draw_date'],
                    draw_number=draw_data['draw_number'],
                    numbers=draw_data['numbers'],
                    extra_info={
                        'red_balls': draw_data.get('red_balls'),
                        'blue_balls': draw_data.get('blue_balls'),
                        'source': draw_data.get('source')
                    }
                )
                
                if success:
                    success_count += 1
            
            self.logger.info(f"{lottery_type} 历史数据保存完成: {success_count}/{len(historical_data)}")
            
        except Exception as e:
            self.logger.error(f"爬取 {lottery_type} 历史数据失败: {e}")
    
    async def crawl_latest(self, lottery_type: str) -> bool:
        """
        爬取指定彩票的最新数据
        
        Args:
            lottery_type: 彩票类型
            
        Returns:
            bool: 是否成功添加到任务队列
        """
        if lottery_type not in self.crawlers:
            self.logger.error(f"不支持的彩票类型: {lottery_type}")
            return False
        
        task_data = {
            'type': 'latest',
            'lottery_type': lottery_type
        }
        
        await self.task_queue.put(task_data)
        return True
    
    async def crawl_historical(self, lottery_type: str, start_date: date, end_date: date) -> bool:
        """
        爬取指定彩票的历史数据
        
        Args:
            lottery_type: 彩票类型
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            bool: 是否成功添加到任务队列
        """
        if lottery_type not in self.crawlers:
            self.logger.error(f"不支持的彩票类型: {lottery_type}")
            return False
        
        task_data = {
            'type': 'historical',
            'lottery_type': lottery_type,
            'start_date': start_date,
            'end_date': end_date
        }
        
        await self.task_queue.put(task_data)
        return True
    
    async def crawl_all_latest(self) -> int:
        """
        爬取所有彩票的最新数据
        
        Returns:
            int: 添加的任务数量
        """
        count = 0
        for lottery_type in self.crawlers.keys():
            if await self.crawl_latest(lottery_type):
                count += 1
        
        return count
    
    def get_crawler_statistics(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        stats = {}
        for lottery_type, crawler in self.crawlers.items():
            stats[lottery_type] = crawler.get_statistics()
        
        return {
            'crawlers': stats,
            'task_queue_size': self.task_queue.qsize(),
            'running_tasks': len(self.running_tasks),
            'is_running': self.is_running
        }
    
    def get_supported_lotteries(self) -> List[str]:
        """获取支持的彩票类型列表"""
        return list(self.crawlers.keys())
