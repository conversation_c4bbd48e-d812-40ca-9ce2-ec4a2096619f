#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库去重功能
验证同一期号的数据是否会被正确去重

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_shuangseqiu_dedup():
    """测试双色球数据去重"""
    print("🔴 测试双色球数据去重...")
    
    try:
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import ShuangseqiuDataAccess
        
        db_manager = LocalDatabaseManager()
        ssq_data = ShuangseqiuDataAccess(db_manager)
        
        # 测试数据 - 同一期号的不同数据
        test_data_1 = [
            {
                'issue': 'TEST001',
                'date': '2025-01-15',
                'red_balls': [1, 2, 3, 4, 5, 6],
                'blue_ball': 7,
                'sales_amount': 100000000,
                'pool_amount': 500000000,
                'source': '测试数据1'
            }
        ]
        
        test_data_2 = [
            {
                'issue': 'TEST001',  # 相同期号
                'date': '2025-01-15',
                'red_balls': [7, 8, 9, 10, 11, 12],  # 不同号码
                'blue_ball': 13,
                'sales_amount': 200000000,
                'pool_amount': 600000000,
                'source': '测试数据2'
            }
        ]
        
        # 插入第一组数据
        print("📝 插入第一组测试数据...")
        count1 = ssq_data.batch_insert_draw_results(test_data_1)
        print(f"   插入结果: {count1}条")
        
        # 查询数据
        results = ssq_data.get_results_by_issue('TEST001')
        if results:
            result = results[0]
            print(f"   第一次插入后的数据: 红球={result['red_balls']}, 蓝球={result['blue_ball']}")
        
        # 插入第二组数据（相同期号）
        print("📝 插入第二组测试数据（相同期号）...")
        count2 = ssq_data.batch_insert_draw_results(test_data_2)
        print(f"   插入结果: {count2}条")
        
        # 再次查询数据
        results = ssq_data.get_results_by_issue('TEST001')
        if results:
            result = results[0]
            print(f"   第二次插入后的数据: 红球={result['red_balls']}, 蓝球={result['blue_ball']}")
            
            # 验证是否被替换
            if result['red_balls'] == [7, 8, 9, 10, 11, 12]:
                print("✅ 去重功能正常：旧数据被新数据替换")
            else:
                print("❌ 去重功能异常：数据未被替换")
        
        # 查询总数
        all_test_results = ssq_data.get_results_by_date_range('2025-01-15', '2025-01-15')
        test_count = len([r for r in all_test_results if r['issue'] == 'TEST001'])
        print(f"   TEST001期号的记录总数: {test_count}")
        
        if test_count == 1:
            print("✅ 数据库去重正常：同一期号只有一条记录")
        else:
            print(f"❌ 数据库去重异常：同一期号有{test_count}条记录")
        
        # 清理测试数据
        print("🧹 清理测试数据...")
        with db_manager.get_connection('shuangseqiu_data') as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM draw_results WHERE issue = 'TEST001'")
            conn.commit()
            print("   测试数据已清理")
        
        db_manager.close_all_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_daletou_dedup():
    """测试大乐透数据去重"""
    print("\n🔵 测试大乐透数据去重...")
    
    try:
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import DaletouDataAccess
        
        db_manager = LocalDatabaseManager()
        dlt_data = DaletouDataAccess(db_manager)
        
        # 测试数据 - 同一期号的不同数据
        test_data_1 = [
            {
                'issue': 'TEST001',
                'date': '2025-01-15',
                'front_balls': [1, 2, 3, 4, 5],
                'back_balls': [6, 7],
                'sales_amount': 100000000,
                'pool_amount': 500000000,
                'source': '测试数据1'
            }
        ]
        
        test_data_2 = [
            {
                'issue': 'TEST001',  # 相同期号
                'date': '2025-01-15',
                'front_balls': [8, 9, 10, 11, 12],  # 不同号码
                'back_balls': [1, 2],
                'sales_amount': 200000000,
                'pool_amount': 600000000,
                'source': '测试数据2'
            }
        ]
        
        # 插入第一组数据
        print("📝 插入第一组测试数据...")
        count1 = dlt_data.batch_insert_draw_results(test_data_1)
        print(f"   插入结果: {count1}条")
        
        # 查询数据
        results = dlt_data.get_results_by_issue('TEST001')
        if results:
            result = results[0]
            print(f"   第一次插入后的数据: 前区={result['front_balls']}, 后区={result['back_balls']}")
        
        # 插入第二组数据（相同期号）
        print("📝 插入第二组测试数据（相同期号）...")
        count2 = dlt_data.batch_insert_draw_results(test_data_2)
        print(f"   插入结果: {count2}条")
        
        # 再次查询数据
        results = dlt_data.get_results_by_issue('TEST001')
        if results:
            result = results[0]
            print(f"   第二次插入后的数据: 前区={result['front_balls']}, 后区={result['back_balls']}")
            
            # 验证是否被替换
            if result['front_balls'] == [8, 9, 10, 11, 12]:
                print("✅ 去重功能正常：旧数据被新数据替换")
            else:
                print("❌ 去重功能异常：数据未被替换")
        
        # 查询总数
        all_test_results = dlt_data.get_results_by_date_range('2025-01-15', '2025-01-15')
        test_count = len([r for r in all_test_results if r['issue'] == 'TEST001'])
        print(f"   TEST001期号的记录总数: {test_count}")
        
        if test_count == 1:
            print("✅ 数据库去重正常：同一期号只有一条记录")
        else:
            print(f"❌ 数据库去重异常：同一期号有{test_count}条记录")
        
        # 清理测试数据
        print("🧹 清理测试数据...")
        with db_manager.get_connection('daletou_data') as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM draw_results WHERE issue = 'TEST001'")
            conn.commit()
            print("   测试数据已清理")
        
        db_manager.close_all_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_dedup():
    """测试爬虫的去重效果"""
    print("\n🕷️ 测试爬虫去重效果...")
    
    try:
        from src.crawler_system.crawler_manager import CrawlerManager
        from src.database.local_database_manager import LocalDatabaseManager
        from src.database.data_access_layer import ShuangseqiuDataAccess, DaletouDataAccess
        
        # 获取爬取前的数据数量
        db_manager = LocalDatabaseManager()
        ssq_data = ShuangseqiuDataAccess(db_manager)
        dlt_data = DaletouDataAccess(db_manager)
        
        ssq_before = len(ssq_data.get_latest_results(100))
        dlt_before = len(dlt_data.get_latest_results(100))
        
        print(f"爬取前数据量: 双色球={ssq_before}条, 大乐透={dlt_before}条")
        
        # 执行爬取
        manager = CrawlerManager()
        
        print("📡 第一次爬取...")
        ssq_result1 = manager.manual_crawl('shuangseqiu', 3)
        dlt_result1 = manager.manual_crawl('daletou', 3)
        
        ssq_after1 = len(ssq_data.get_latest_results(100))
        dlt_after1 = len(dlt_data.get_latest_results(100))
        
        print(f"第一次爬取后: 双色球={ssq_after1}条(+{ssq_after1-ssq_before}), 大乐透={dlt_after1}条(+{dlt_after1-dlt_before})")
        
        print("📡 第二次爬取（相同数据）...")
        ssq_result2 = manager.manual_crawl('shuangseqiu', 3)
        dlt_result2 = manager.manual_crawl('daletou', 3)
        
        ssq_after2 = len(ssq_data.get_latest_results(100))
        dlt_after2 = len(dlt_data.get_latest_results(100))
        
        print(f"第二次爬取后: 双色球={ssq_after2}条(+{ssq_after2-ssq_after1}), 大乐透={dlt_after2}条(+{dlt_after2-dlt_after1})")
        
        # 验证去重效果
        if ssq_after2 == ssq_after1 and dlt_after2 == dlt_after1:
            print("✅ 爬虫去重正常：重复爬取不会增加数据")
        else:
            print("❌ 爬虫去重异常：重复爬取增加了数据")
        
        manager.close()
        db_manager.close_all_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🗃️ 数据库去重功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试双色球去重
    if test_shuangseqiu_dedup():
        success_count += 1
    
    # 测试大乐透去重
    if test_daletou_dedup():
        success_count += 1
    
    # 测试爬虫去重
    if test_crawler_dedup():
        success_count += 1
    
    # 显示结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有去重测试通过！")
        print("\n✅ 数据库去重机制正常:")
        print("  🔧 使用 INSERT OR REPLACE 语句")
        print("  🔧 期号字段设置为 UNIQUE")
        print("  🔧 同一期号的数据会被自动替换")
        print("  🔧 爬虫重复运行不会产生重复数据")
    else:
        print("❌ 部分测试失败，需要检查去重机制")
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
