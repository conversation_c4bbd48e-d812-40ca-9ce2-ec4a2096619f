#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai环境测试脚本
测试虚拟环境和基础依赖是否正常工作

Author: HuiCai Team
Date: 2025-01-15
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("🐍 Python版本测试:")
    print(f"   Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    
    if sys.version_info >= (3, 8):
        print("   ✅ Python版本符合要求 (3.8+)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.8+")
        return False

def test_basic_imports():
    """测试基础依赖导入"""
    print("\n📦 基础依赖测试:")
    
    packages = [
        ('numpy', 'NumPy数值计算'),
        ('pandas', 'Pandas数据处理'),
        ('yaml', 'PyYAML配置文件'),
        ('requests', 'HTTP请求库'),
        ('bs4', 'BeautifulSoup网页解析'),
        ('sklearn', 'Scikit-learn机器学习'),
        ('fastapi', 'FastAPI Web框架'),
        ('uvicorn', 'Uvicorn ASGI服务器'),
        ('aiosqlite', 'AsyncIO SQLite'),
        ('apscheduler', 'APScheduler任务调度'),
        ('psutil', 'PSUtil系统监控'),
    ]
    
    success_count = 0
    for package, description in packages:
        try:
            __import__(package)
            print(f"   ✅ {package:<12} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {package:<12} - 导入失败: {e}")
    
    print(f"\n   📊 导入成功率: {success_count}/{len(packages)} ({success_count/len(packages)*100:.1f}%)")
    return success_count == len(packages)

def test_project_structure():
    """测试项目结构"""
    print("\n📁 项目结构测试:")
    
    required_dirs = [
        'src',
        'src/management_layer',
        'src/data_layer',
        'src/optimization',
        'src/web_api',
        'config',
        'data',
        'scripts',
        'web',
    ]
    
    required_files = [
        'main.py',
        'requirements-minimal.txt',
        'activate_env.bat',
        'src/management_layer/config_manager.py',
        'src/optimization/performance_optimizer.py',
        'src/web_api/main.py',
    ]
    
    project_root = Path('.')
    
    # 检查目录
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"   ✅ {dir_path}/")
        else:
            print(f"   ❌ {dir_path}/ - 目录不存在")
            missing_dirs.append(dir_path)
    
    # 检查文件
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if missing_dirs or missing_files:
        print(f"\n   ⚠️ 缺少 {len(missing_dirs)} 个目录和 {len(missing_files)} 个文件")
        return False
    else:
        print(f"\n   ✅ 项目结构完整")
        return True

def test_database_creation():
    """测试数据库创建"""
    print("\n💾 数据库测试:")
    
    try:
        import aiosqlite
        import asyncio
        
        async def create_test_db():
            db_path = "data/test.db"
            os.makedirs("data", exist_ok=True)
            
            async with aiosqlite.connect(db_path) as db:
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS test_table (
                        id INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                await db.execute('''
                    INSERT INTO test_table (name) VALUES (?)
                ''', ('HuiCai测试',))
                
                await db.commit()
                
                cursor = await db.execute('SELECT * FROM test_table')
                rows = await cursor.fetchall()
                return len(rows) > 0
        
        # 运行异步测试
        if sys.platform.startswith('win'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        result = asyncio.run(create_test_db())
        
        if result:
            print("   ✅ SQLite数据库创建和操作成功")
            # 清理测试文件
            test_db_path = Path("data/test.db")
            if test_db_path.exists():
                test_db_path.unlink()
            return True
        else:
            print("   ❌ 数据库操作失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return False

def test_web_framework():
    """测试Web框架"""
    print("\n🌐 Web框架测试:")
    
    try:
        from fastapi import FastAPI
        from uvicorn import Config
        
        # 创建简单的FastAPI应用
        app = FastAPI(title="HuiCai测试")
        
        @app.get("/")
        def read_root():
            return {"message": "HuiCai系统运行正常"}
        
        @app.get("/health")
        def health_check():
            return {"status": "healthy", "system": "HuiCai"}
        
        # 测试配置创建
        config = Config(app, host="127.0.0.1", port=8000, log_level="info")
        
        print("   ✅ FastAPI应用创建成功")
        print("   ✅ Uvicorn配置创建成功")
        print("   ℹ️ Web服务器可以启动 (未实际启动)")
        return True
        
    except Exception as e:
        print(f"   ❌ Web框架测试失败: {e}")
        return False

def test_system_resources():
    """测试系统资源"""
    print("\n💻 系统资源测试:")
    
    try:
        import psutil
        
        # 获取系统信息
        cpu_count = psutil.cpu_count()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        print(f"   💾 CPU核心数: {cpu_count}")
        print(f"   🧠 总内存: {memory.total / (1024**3):.1f} GB")
        print(f"   💽 可用磁盘: {disk.free / (1024**3):.1f} GB")
        print(f"   📊 内存使用率: {memory.percent:.1f}%")
        
        # 检查资源是否足够
        if memory.total >= 4 * (1024**3):  # 4GB
            print("   ✅ 内存充足")
        else:
            print("   ⚠️ 内存可能不足，建议8GB+")
        
        if disk.free >= 5 * (1024**3):  # 5GB
            print("   ✅ 磁盘空间充足")
        else:
            print("   ⚠️ 磁盘空间不足，建议10GB+")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 系统资源检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("🧪 HuiCai 慧彩系统环境测试")
    print("="*60)
    
    tests = [
        ("Python版本", test_python_version),
        ("基础依赖", test_basic_imports),
        ("项目结构", test_project_structure),
        ("数据库功能", test_database_creation),
        ("Web框架", test_web_framework),
        ("系统资源", test_system_resources),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📋 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<12}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！HuiCai环境配置成功！")
        print("\n🚀 下一步:")
        print("   1. 运行: python main.py (CLI模式)")
        print("   2. 运行: python start_web.py (Web模式)")
        print("   3. 运行: python demo_stage4.py (功能演示)")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("\n🔧 建议:")
        print("   1. 检查Python版本是否为3.8+")
        print("   2. 重新安装依赖: pip install -r requirements-minimal.txt")
        print("   3. 检查项目文件是否完整")
    
    print("\n" + "="*60)
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
