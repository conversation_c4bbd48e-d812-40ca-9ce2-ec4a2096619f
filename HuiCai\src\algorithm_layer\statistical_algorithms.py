#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计学算法
基于概率论和统计学的彩票分析算法

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from scipy import stats
from collections import Counter, defaultdict
import logging
from datetime import datetime


class StatisticalAlgorithms:
    """统计学算法集合"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.frequency_stats = {}
        self.pattern_stats = {}
        self.trend_stats = {}
    
    def analyze_frequency(self, data: pd.DataFrame, lottery_type: str) -> Dict[str, Any]:
        """频率分析"""
        try:
            if lottery_type == 'shuangseqiu':
                return self._analyze_shuangseqiu_frequency(data)
            elif lottery_type == 'daletou':
                return self._analyze_daletou_frequency(data)
            elif lottery_type == 'fucai3d':
                return self._analyze_fucai3d_frequency(data)
            else:
                raise ValueError(f"不支持的彩票类型: {lottery_type}")
        except Exception as e:
            self.logger.error(f"频率分析失败: {e}")
            raise
    
    def _analyze_shuangseqiu_frequency(self, data: pd.DataFrame) -> Dict[str, Any]:
        """双色球频率分析"""
        # 红球频率统计
        red_counter = Counter()
        for red_balls in data['red_balls']:
            red_counter.update(red_balls)
        
        # 蓝球频率统计
        blue_counter = Counter(data['blue_ball'])
        
        # 计算期望频率
        total_periods = len(data)
        red_expected = total_periods * 6 / 33  # 每期6个球，共33个号码
        blue_expected = total_periods / 16     # 每期1个球，共16个号码
        
        # 计算偏差
        red_deviations = {}
        for num in range(1, 34):
            actual = red_counter.get(num, 0)
            deviation = (actual - red_expected) / red_expected
            red_deviations[num] = {
                'actual': actual,
                'expected': red_expected,
                'deviation': deviation,
                'hot_cold': 'hot' if deviation > 0.1 else 'cold' if deviation < -0.1 else 'normal'
            }
        
        blue_deviations = {}
        for num in range(1, 17):
            actual = blue_counter.get(num, 0)
            deviation = (actual - blue_expected) / blue_expected
            blue_deviations[num] = {
                'actual': actual,
                'expected': blue_expected,
                'deviation': deviation,
                'hot_cold': 'hot' if deviation > 0.1 else 'cold' if deviation < -0.1 else 'normal'
            }
        
        return {
            'red_frequency': red_deviations,
            'blue_frequency': blue_deviations,
            'total_periods': total_periods,
            'analysis_date': datetime.now().isoformat()
        }
    
    def _analyze_daletou_frequency(self, data: pd.DataFrame) -> Dict[str, Any]:
        """大乐透频率分析"""
        front_counter = Counter()
        back_counter = Counter()
        
        for front_balls in data['front_balls']:
            front_counter.update(front_balls)
        
        for back_balls in data['back_balls']:
            back_counter.update(back_balls)
        
        total_periods = len(data)
        front_expected = total_periods * 5 / 35
        back_expected = total_periods * 2 / 12
        
        return {
            'front_frequency': self._calculate_deviations(front_counter, front_expected, range(1, 36)),
            'back_frequency': self._calculate_deviations(back_counter, back_expected, range(1, 13)),
            'total_periods': total_periods
        }
    
    def _analyze_fucai3d_frequency(self, data: pd.DataFrame) -> Dict[str, Any]:
        """福彩3D频率分析"""
        position_counters = [Counter(), Counter(), Counter()]
        
        for numbers in data['numbers']:
            for i, num in enumerate(numbers):
                position_counters[i][num] += 1
        
        total_periods = len(data)
        expected = total_periods / 10  # 每位0-9共10个数字
        
        position_stats = {}
        for i in range(3):
            position_stats[f'position_{i+1}'] = self._calculate_deviations(
                position_counters[i], expected, range(0, 10)
            )
        
        return {
            'position_frequency': position_stats,
            'total_periods': total_periods
        }

    def _calculate_deviations(self, counter: Counter, expected: float, number_range: range) -> Dict[int, Dict]:
        """计算频率偏差"""
        deviations = {}
        for num in number_range:
            actual = counter.get(num, 0)
            deviation = (actual - expected) / expected if expected > 0 else 0
            deviations[num] = {
                'actual': actual,
                'expected': expected,
                'deviation': deviation,
                'hot_cold': 'hot' if deviation > 0.1 else 'cold' if deviation < -0.1 else 'normal'
            }
        return deviations

    def analyze_patterns(self, data: pd.DataFrame, lottery_type: str) -> Dict[str, Any]:
        """模式分析"""
        try:
            if lottery_type == 'shuangseqiu':
                return self._analyze_shuangseqiu_patterns(data)
            elif lottery_type == 'daletou':
                return self._analyze_daletou_patterns(data)
            elif lottery_type == 'fucai3d':
                return self._analyze_fucai3d_patterns(data)
        except Exception as e:
            self.logger.error(f"模式分析失败: {e}")
            raise

    def _analyze_shuangseqiu_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """双色球模式分析"""
        patterns = {
            'odd_even_patterns': [],
            'big_small_patterns': [],
            'zone_patterns': [],
            'sum_patterns': []
        }

        for red_balls in data['red_balls']:
            # 奇偶模式
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            patterns['odd_even_patterns'].append(f"{odd_count}:{6-odd_count}")

            # 大小模式
            big_count = sum(1 for x in red_balls if x > 17)
            patterns['big_small_patterns'].append(f"{big_count}:{6-big_count}")

            # 区间模式
            zone1 = sum(1 for x in red_balls if 1 <= x <= 11)
            zone2 = sum(1 for x in red_balls if 12 <= x <= 22)
            zone3 = sum(1 for x in red_balls if 23 <= x <= 33)
            patterns['zone_patterns'].append(f"{zone1}:{zone2}:{zone3}")

            # 和值模式
            patterns['sum_patterns'].append(sum(red_balls))

        # 统计模式频率
        pattern_stats = {}
        for pattern_type, pattern_list in patterns.items():
            counter = Counter(pattern_list)
            pattern_stats[pattern_type] = dict(counter.most_common(10))

        return pattern_stats

    def _analyze_daletou_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """大乐透模式分析"""
        patterns = {
            'front_odd_even': [],
            'back_odd_even': [],
            'front_sum': [],
            'back_sum': []
        }

        for front_balls, back_balls in zip(data['front_balls'], data['back_balls']):
            # 前区奇偶
            front_odd = sum(1 for x in front_balls if x % 2 == 1)
            patterns['front_odd_even'].append(f"{front_odd}:{5-front_odd}")

            # 后区奇偶
            back_odd = sum(1 for x in back_balls if x % 2 == 1)
            patterns['back_odd_even'].append(f"{back_odd}:{2-back_odd}")

            # 和值
            patterns['front_sum'].append(sum(front_balls))
            patterns['back_sum'].append(sum(back_balls))

        pattern_stats = {}
        for pattern_type, pattern_list in patterns.items():
            counter = Counter(pattern_list)
            pattern_stats[pattern_type] = dict(counter.most_common(10))

        return pattern_stats

    def _analyze_fucai3d_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """福彩3D模式分析"""
        patterns = {
            'form_patterns': [],  # 组三、组六
            'sum_patterns': [],
            'span_patterns': []
        }

        for numbers in data['numbers']:
            # 形态分析
            unique_count = len(set(numbers))
            if unique_count == 2:
                patterns['form_patterns'].append('组三')
            elif unique_count == 3:
                patterns['form_patterns'].append('组六')
            else:
                patterns['form_patterns'].append('豹子')

            # 和值
            patterns['sum_patterns'].append(sum(numbers))

            # 跨度
            patterns['span_patterns'].append(max(numbers) - min(numbers))

        pattern_stats = {}
        for pattern_type, pattern_list in patterns.items():
            counter = Counter(pattern_list)
            pattern_stats[pattern_type] = dict(counter.most_common(10))

        return pattern_stats

    def analyze_trends(self, data: pd.DataFrame, lottery_type: str, window_size: int = 10) -> Dict[str, Any]:
        """趋势分析"""
        try:
            recent_data = data.tail(window_size)

            if lottery_type == 'shuangseqiu':
                return self._analyze_shuangseqiu_trends(recent_data)
            elif lottery_type == 'daletou':
                return self._analyze_daletou_trends(recent_data)
            elif lottery_type == 'fucai3d':
                return self._analyze_fucai3d_trends(recent_data)
        except Exception as e:
            self.logger.error(f"趋势分析失败: {e}")
            raise

    def _analyze_shuangseqiu_trends(self, data: pd.DataFrame) -> Dict[str, Any]:
        """双色球趋势分析"""
        trends = {
            'hot_numbers': [],
            'cold_numbers': [],
            'trending_up': [],
            'trending_down': []
        }

        # 统计最近出现频率
        red_counter = Counter()
        for red_balls in data['red_balls']:
            red_counter.update(red_balls)

        # 计算趋势
        avg_frequency = len(data) * 6 / 33

        for num in range(1, 34):
            frequency = red_counter.get(num, 0)
            if frequency > avg_frequency * 1.5:
                trends['hot_numbers'].append(num)
            elif frequency < avg_frequency * 0.5:
                trends['cold_numbers'].append(num)

        return trends

    def predict_by_frequency(self, data: pd.DataFrame, lottery_type: str) -> Dict[str, Any]:
        """基于频率的预测"""
        try:
            frequency_analysis = self.analyze_frequency(data, lottery_type)

            if lottery_type == 'shuangseqiu':
                return self._predict_shuangseqiu_by_frequency(frequency_analysis)
            elif lottery_type == 'daletou':
                return self._predict_daletou_by_frequency(frequency_analysis)
            elif lottery_type == 'fucai3d':
                return self._predict_fucai3d_by_frequency(frequency_analysis)
        except Exception as e:
            self.logger.error(f"频率预测失败: {e}")
            raise

    def _predict_shuangseqiu_by_frequency(self, frequency_analysis: Dict) -> Dict[str, Any]:
        """双色球频率预测"""
        red_freq = frequency_analysis['red_frequency']
        blue_freq = frequency_analysis['blue_frequency']

        # 选择冷号（理论上应该回补）
        cold_reds = [num for num, stats in red_freq.items()
                    if stats['hot_cold'] == 'cold']
        hot_reds = [num for num, stats in red_freq.items()
                   if stats['hot_cold'] == 'hot']

        # 平衡选择：3个冷号 + 3个热号
        import random
        selected_reds = []
        if len(cold_reds) >= 3:
            selected_reds.extend(random.sample(cold_reds, 3))
        else:
            selected_reds.extend(cold_reds)

        remaining_count = 6 - len(selected_reds)
        if remaining_count > 0 and hot_reds:
            selected_reds.extend(random.sample(hot_reds, min(remaining_count, len(hot_reds))))

        # 如果还不够6个，随机补充
        while len(selected_reds) < 6:
            num = random.randint(1, 33)
            if num not in selected_reds:
                selected_reds.append(num)

        selected_reds.sort()

        # 选择蓝球（选择冷号）
        cold_blues = [num for num, stats in blue_freq.items()
                     if stats['hot_cold'] == 'cold']
        selected_blue = random.choice(cold_blues) if cold_blues else random.randint(1, 16)

        return {
            'lottery_type': 'shuangseqiu',
            'method': 'frequency_analysis',
            'numbers': selected_reds + [selected_blue],
            'confidence': 0.65,
            'reasoning': '基于冷热号平衡选择',
            'timestamp': datetime.now().isoformat()
        }

    def predict_by_pattern(self, data: pd.DataFrame, lottery_type: str) -> Dict[str, Any]:
        """基于模式的预测"""
        try:
            pattern_analysis = self.analyze_patterns(data, lottery_type)
            trend_analysis = self.analyze_trends(data, lottery_type)

            if lottery_type == 'shuangseqiu':
                return self._predict_shuangseqiu_by_pattern(pattern_analysis, trend_analysis)
            elif lottery_type == 'fucai3d':
                return self._predict_fucai3d_by_pattern(pattern_analysis)
        except Exception as e:
            self.logger.error(f"模式预测失败: {e}")
            raise

    def _predict_shuangseqiu_by_pattern(self, pattern_analysis: Dict, trend_analysis: Dict) -> Dict[str, Any]:
        """双色球模式预测"""
        # 分析最常见的奇偶模式
        odd_even_patterns = pattern_analysis.get('odd_even_patterns', {})
        most_common_pattern = max(odd_even_patterns.items(), key=lambda x: x[1])[0] if odd_even_patterns else "3:3"

        odd_count = int(most_common_pattern.split(':')[0])

        # 结合趋势选择号码
        hot_numbers = trend_analysis.get('hot_numbers', [])
        cold_numbers = trend_analysis.get('cold_numbers', [])

        import random
        selected_numbers = []

        # 按奇偶比例选择
        odds = [n for n in range(1, 34) if n % 2 == 1]
        evens = [n for n in range(1, 34) if n % 2 == 0]

        # 优先选择热号中的奇数
        hot_odds = [n for n in hot_numbers if n % 2 == 1]
        hot_evens = [n for n in hot_numbers if n % 2 == 0]

        # 选择奇数
        selected_odds = random.sample(hot_odds, min(odd_count, len(hot_odds)))
        if len(selected_odds) < odd_count:
            remaining_odds = [n for n in odds if n not in selected_odds]
            selected_odds.extend(random.sample(remaining_odds, odd_count - len(selected_odds)))

        # 选择偶数
        even_count = 6 - odd_count
        selected_evens = random.sample(hot_evens, min(even_count, len(hot_evens)))
        if len(selected_evens) < even_count:
            remaining_evens = [n for n in evens if n not in selected_evens]
            selected_evens.extend(random.sample(remaining_evens, even_count - len(selected_evens)))

        selected_numbers = sorted(selected_odds + selected_evens)
        selected_blue = random.randint(1, 16)

        return {
            'lottery_type': 'shuangseqiu',
            'method': 'pattern_analysis',
            'numbers': selected_numbers + [selected_blue],
            'confidence': 0.70,
            'reasoning': f'基于{most_common_pattern}奇偶模式和热号趋势',
            'timestamp': datetime.now().isoformat()
        }
