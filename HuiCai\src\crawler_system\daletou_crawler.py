#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透专用爬虫
专门爬取大乐透开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from bs4 import BeautifulSoup
import requests

from .anti_crawler import create_anti_crawler_manager


class DaletouCrawler:
    """大乐透爬虫"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化反爬虫管理器
        self.anti_crawler = create_anti_crawler_manager(
            self.config.get('anti_crawler_mode', 'moderate')
        )
        
        # 数据源配置
        self.data_sources = [
            {
                'name': '模拟数据源',
                'url': 'mock://daletou/data',
                'type': 'mock',
                'method': 'GET',
                'headers': {},
                'params': {'count': 30}
            },
            {
                'name': '中国体育彩票官网API',
                'url': 'https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry',
                'type': 'api',
                'method': 'POST',
                'headers': {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Accept': 'application/json, text/plain, */*',
                    'Referer': 'https://www.sporttery.cn/lottery/dlt/',
                    'Origin': 'https://www.sporttery.cn',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                'params': {'gameNo': '85', 'provinceId': '0', 'pageSize': 30, 'isVerify': 1}
            },
            {
                'name': '彩票开奖API',
                'url': 'https://api.apiopen.top/api/lottery',
                'type': 'api',
                'method': 'GET',
                'headers': {
                    'Accept': 'application/json'
                },
                'params': {'type': 'dlt', 'count': 30}
            }
        ]
        
        # 当前使用的数据源索引
        self.current_source_index = 0
        
        # 数据验证规则
        self.validation_rules = {
            'front_balls': {
                'count': 5,
                'min_value': 1,
                'max_value': 35,
                'unique': True
            },
            'back_balls': {
                'count': 2,
                'min_value': 1,
                'max_value': 12,
                'unique': True
            }
        }
    
    def crawl_latest_data(self, count: int = 10) -> Dict[str, Any]:
        """爬取最新开奖数据"""
        self.logger.info(f"开始爬取大乐透最新 {count} 期数据")
        
        for attempt in range(len(self.data_sources)):
            source = self.data_sources[self.current_source_index]
            
            try:
                self.logger.info(f"尝试从 {source['name']} 爬取数据")

                # 根据数据源类型选择爬取方法
                if source['type'] == 'mock':
                    data = self._generate_mock_data(count)
                else:
                    data = self._crawl_from_api(source, count)
                
                if data and len(data) > 0:
                    self.logger.info(f"成功从 {source['name']} 爬取到 {len(data)} 期数据")
                    return {
                        'status': 'success',
                        'source': source['name'],
                        'data': data,
                        'count': len(data),
                        'timestamp': datetime.now().isoformat()
                    }
                
            except Exception as e:
                self.logger.error(f"从 {source['name']} 爬取数据失败: {e}")
            
            # 切换到下一个数据源
            self.current_source_index = (self.current_source_index + 1) % len(self.data_sources)
        
        return {
            'status': 'failed',
            'error': '所有数据源都无法获取数据',
            'timestamp': datetime.now().isoformat()
        }
    
    def _crawl_from_api(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从API爬取数据"""
        url = source['url']
        method = source.get('method', 'POST')
        headers = source.get('headers', {})
        params = source['params'].copy()

        # 根据不同API调整参数
        if 'sporttery.cn' in url and method == 'POST':
            params['pageSize'] = count
            # 使用JSON格式
            response = self.anti_crawler.make_request(
                url, method=method, json=params, headers=headers
            )
        elif 'sporttery.cn' in url and method == 'GET':
            params['pageSize'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        elif 'apiopen.top' in url:
            params['count'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        else:
            params['issueCount'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )

        if not response:
            return []

        try:
            data = response.json()

            # 根据不同API解析数据
            if 'sporttery.cn' in url:
                if data.get('success') and 'value' in data:
                    return self._parse_official_api_data(data['value']['list'])
            elif 'apiopen.top' in url:
                if data.get('code') == 200 and 'result' in data:
                    return self._parse_apiopen_data(data['result'])
            else:
                # 通用解析
                if 'result' in data or 'data' in data:
                    result_data = data.get('result', data.get('data', []))
                    return self._parse_generic_api_data(result_data)

        except Exception as e:
            self.logger.error(f"解析API数据失败: {e}")

        return []
    
    def _crawl_from_html(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从HTML页面爬取数据"""
        url = source['url']
        
        response = self.anti_crawler.make_request(url)
        if not response:
            return []
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            if '500.com' in url:
                return self._parse_500_data(soup, count)
            elif 'sina.com' in url:
                return self._parse_sina_data(soup, count)
            
        except Exception as e:
            self.logger.error(f"解析HTML数据失败: {e}")
        
        return []
    
    def _parse_official_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析官方API数据"""
        parsed_data = []
        
        for item in result_data:
            try:
                # 解析开奖号码
                lotteryDrawResult = item.get('lotteryDrawResult', '')
                if not lotteryDrawResult:
                    continue
                
                # 分割前区和后区
                parts = lotteryDrawResult.split(' ')
                if len(parts) >= 2:
                    front_part = parts[0]  # 前区
                    back_part = parts[1]   # 后区
                    
                    front_balls = [int(x) for x in front_part.split(' ') if x.isdigit()]
                    back_balls = [int(x) for x in back_part.split(' ') if x.isdigit()]
                    
                    # 如果解析失败，尝试其他方式
                    if len(front_balls) != 5 or len(back_balls) != 2:
                        # 尝试按空格分割所有号码
                        all_numbers = [int(x) for x in lotteryDrawResult.split() if x.isdigit()]
                        if len(all_numbers) == 7:
                            front_balls = all_numbers[:5]
                            back_balls = all_numbers[5:7]
                    
                    # 验证数据
                    if self._validate_numbers(front_balls, back_balls):
                        parsed_data.append({
                            'issue': item.get('lotteryDrawNum', ''),
                            'date': item.get('lotteryDrawTime', ''),
                            'front_balls': front_balls,
                            'back_balls': back_balls,
                            'sales_amount': item.get('totalSaleAmount', 0),
                            'pool_amount': item.get('poolBalanceAmt', 0),
                            'source': '官方API'
                        })
                
            except Exception as e:
                self.logger.warning(f"解析单条官方数据失败: {e}")
                continue
        
        return parsed_data
    
    def _parse_500_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析500彩票网数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'class': 'kj_tablelist02'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 9:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析前区球
                        front_balls = []
                        for i in range(2, 7):  # 前区在第2-6列
                            front_balls.append(int(cells[i].text.strip()))
                        
                        # 解析后区球
                        back_balls = []
                        for i in range(7, 9):  # 后区在第7-8列
                            back_balls.append(int(cells[i].text.strip()))
                        
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '500彩票网'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析500彩票网单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析500彩票网数据失败: {e}")
        
        return parsed_data
    
    def _parse_sina_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析新浪彩票数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'id': 'dlt_data'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 9:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析前区球
                        front_balls = []
                        for i in range(2, 7):  # 前区在第2-6列
                            front_balls.append(int(cells[i].text.strip()))
                        
                        # 解析后区球
                        back_balls = []
                        for i in range(7, 9):  # 后区在第7-8列
                            back_balls.append(int(cells[i].text.strip()))
                        
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '新浪彩票'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析新浪彩票单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析新浪彩票数据失败: {e}")
        
        return parsed_data

    def _parse_apiopen_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析apiopen.top API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 解析开奖号码
                lottery_no = item.get('lottery_no', '')
                lottery_date = item.get('lottery_date', '')
                lottery_res = item.get('lottery_res', '')

                if lottery_res:
                    # 分割号码 "01,07,12,23,28,03,08"
                    numbers = lottery_res.split(',')
                    if len(numbers) >= 7:
                        front_balls = [int(num) for num in numbers[:5]]
                        back_balls = [int(num) for num in numbers[5:7]]

                        # 验证数据
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': lottery_no,
                                'date': lottery_date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': 'apiopen.top'
                            })

            except Exception as e:
                self.logger.warning(f"解析apiopen数据失败: {e}")
                continue

        return parsed_data

    def _parse_generic_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析通用API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 尝试多种可能的字段名
                issue = item.get('issue', item.get('code', item.get('period', '')))
                date = item.get('date', item.get('opentime', item.get('time', '')))

                # 尝试解析号码
                front_balls = []
                back_balls = []

                # 方式1: 直接字段
                if 'front_balls' in item and 'back_balls' in item:
                    front_balls = item['front_balls']
                    back_balls = item['back_balls']
                # 方式2: 号码字符串
                elif 'numbers' in item:
                    numbers_str = item['numbers']
                    if '+' in numbers_str:
                        parts = numbers_str.split('+')
                        front_part = parts[0].strip()
                        back_part = parts[1].strip()
                        front_balls = [int(x) for x in front_part.split(',')]
                        back_balls = [int(x) for x in back_part.split(',')]
                    else:
                        numbers = [int(x) for x in numbers_str.split(',')]
                        if len(numbers) >= 7:
                            front_balls = numbers[:5]
                            back_balls = numbers[5:7]

                # 验证数据
                if len(front_balls) == 5 and len(back_balls) == 2:
                    if self._validate_numbers(front_balls, back_balls):
                        parsed_data.append({
                            'issue': issue,
                            'date': date,
                            'front_balls': front_balls,
                            'back_balls': back_balls,
                            'sales_amount': item.get('sales', 0),
                            'pool_amount': item.get('pool', 0),
                            'source': '通用API'
                        })

            except Exception as e:
                self.logger.warning(f"解析通用API数据失败: {e}")
                continue

        return parsed_data

    def _generate_mock_data(self, count: int) -> List[Dict[str, Any]]:
        """生成模拟大乐透数据"""
        import random
        from datetime import datetime, timedelta

        mock_data = []
        base_date = datetime.now() - timedelta(days=count * 3)  # 每3天一期

        for i in range(count):
            issue_date = base_date + timedelta(days=i * 3)
            issue = f"24{str(120 + i).zfill(3)}"  # 从24120开始

            # 生成前区球（1-35，5个不重复）
            front_balls = sorted(random.sample(range(1, 36), 5))

            # 生成后区球（1-12，2个不重复）
            back_balls = sorted(random.sample(range(1, 13), 2))

            # 生成销售额和奖池（模拟真实数据）
            sales_amount = random.randint(150000000, 400000000)  # 1.5-4亿
            pool_amount = random.randint(300000000, 1000000000)  # 3-10亿

            mock_data.append({
                'issue': issue,
                'date': issue_date.strftime('%Y-%m-%d'),
                'front_balls': front_balls,
                'back_balls': back_balls,
                'sales_amount': sales_amount,
                'pool_amount': pool_amount,
                'source': '模拟数据源'
            })

        # 按日期倒序排列（最新的在前面）
        mock_data.reverse()

        self.logger.info(f"生成了 {len(mock_data)} 期模拟大乐透数据")
        return mock_data

    def _validate_numbers(self, front_balls: List[int], back_balls: List[int]) -> bool:
        """验证开奖号码"""
        try:
            # 验证前区球
            front_rules = self.validation_rules['front_balls']
            if len(front_balls) != front_rules['count']:
                return False
            
            for ball in front_balls:
                if ball < front_rules['min_value'] or ball > front_rules['max_value']:
                    return False
            
            if front_rules['unique'] and len(set(front_balls)) != len(front_balls):
                return False
            
            # 验证后区球
            back_rules = self.validation_rules['back_balls']
            if len(back_balls) != back_rules['count']:
                return False
            
            for ball in back_balls:
                if ball < back_rules['min_value'] or ball > back_rules['max_value']:
                    return False
            
            if back_rules['unique'] and len(set(back_balls)) != len(back_balls):
                return False
            
            return True
            
        except Exception:
            return False
    
    def crawl_historical_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """爬取历史数据"""
        self.logger.info(f"爬取大乐透历史数据: {start_date} 到 {end_date}")
        
        # 这里可以实现更复杂的历史数据爬取逻辑
        # 目前返回最新数据作为示例
        return self.crawl_latest_data(100)
    
    def get_crawler_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        anti_crawler_stats = self.anti_crawler.get_stats()
        
        return {
            'crawler_type': '大乐透爬虫',
            'current_source': self.data_sources[self.current_source_index]['name'],
            'total_sources': len(self.data_sources),
            'anti_crawler_stats': anti_crawler_stats,
            'validation_rules': self.validation_rules
        }
    
    def close(self):
        """关闭爬虫"""
        self.anti_crawler.close()
        self.logger.info("大乐透爬虫已关闭")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    crawler = DaletouCrawler()
    
    # 测试爬取最新数据
    result = crawler.crawl_latest_data(5)
    print(f"爬取结果: {result}")
    
    # 显示统计信息
    stats = crawler.get_crawler_stats()
    print(f"爬虫统计: {stats}")
    
    crawler.close()
