#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透专用爬虫
专门爬取大乐透开奖数据

Author: HuiCai Team
Date: 2025-01-15
"""

import re
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from bs4 import BeautifulSoup
import requests

from .anti_crawler import create_anti_crawler_manager


class DaletouCrawler:
    """大乐透爬虫"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化反爬虫管理器
        self.anti_crawler = create_anti_crawler_manager(
            self.config.get('anti_crawler_mode', 'moderate')
        )
        
        # 数据源配置
        self.data_sources = [
            {
                'name': '500彩票网大乐透',
                'url': 'https://datachart.500.com/dlt/',
                'type': 'html',
                'method': 'GET',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                    'Referer': 'https://www.500.com/'
                },
                'encoding': 'gb2312',
                'params': {}
            },
            {
                'name': '500彩票网大乐透历史',
                'url': 'https://datachart.500.com/dlt/history/newinc/history.php',
                'type': 'html',
                'method': 'GET',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Referer': 'https://datachart.500.com/dlt/'
                },
                'encoding': 'gb2312',
                'params': {'start': '24001', 'end': '24010'}
            },
            {
                'name': '模拟数据源',
                'url': 'mock://daletou/data',
                'type': 'mock',
                'method': 'GET',
                'headers': {},
                'params': {'count': 30}
            }
        ]
        
        # 当前使用的数据源索引
        self.current_source_index = 0
        
        # 数据验证规则
        self.validation_rules = {
            'front_balls': {
                'count': 5,
                'min_value': 1,
                'max_value': 35,
                'unique': True
            },
            'back_balls': {
                'count': 2,
                'min_value': 1,
                'max_value': 12,
                'unique': True
            }
        }
    
    def crawl_latest_data(self, count: int = 10) -> Dict[str, Any]:
        """爬取最新开奖数据"""
        self.logger.info(f"开始爬取大乐透最新 {count} 期数据")
        
        for attempt in range(len(self.data_sources)):
            source = self.data_sources[self.current_source_index]
            
            try:
                self.logger.info(f"尝试从 {source['name']} 爬取数据")

                # 根据数据源类型选择爬取方法
                if source['type'] == 'mock':
                    data = self._generate_mock_data(count)
                elif source['type'] == 'html':
                    data = self._crawl_from_html(source, count)
                else:
                    data = self._crawl_from_api(source, count)
                
                if data and len(data) > 0:
                    self.logger.info(f"成功从 {source['name']} 爬取到 {len(data)} 期数据")
                    return {
                        'status': 'success',
                        'source': source['name'],
                        'data': data,
                        'count': len(data),
                        'timestamp': datetime.now().isoformat()
                    }
                
            except Exception as e:
                self.logger.error(f"从 {source['name']} 爬取数据失败: {e}")
            
            # 切换到下一个数据源
            self.current_source_index = (self.current_source_index + 1) % len(self.data_sources)
        
        return {
            'status': 'failed',
            'error': '所有数据源都无法获取数据',
            'timestamp': datetime.now().isoformat()
        }
    
    def _crawl_from_api(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从API爬取数据"""
        url = source['url']
        method = source.get('method', 'POST')
        headers = source.get('headers', {})
        params = source['params'].copy()

        # 根据不同API调整参数
        if 'sporttery.cn' in url and method == 'POST':
            params['pageSize'] = count
            # 使用JSON格式
            response = self.anti_crawler.make_request(
                url, method=method, json=params, headers=headers
            )
        elif 'sporttery.cn' in url and method == 'GET':
            params['pageSize'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        elif 'apiopen.top' in url:
            params['count'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )
        else:
            params['issueCount'] = count
            response = self.anti_crawler.make_request(
                url, method=method, params=params, headers=headers
            )

        if not response:
            return []

        try:
            data = response.json()

            # 根据不同API解析数据
            if 'sporttery.cn' in url:
                if data.get('success') and 'value' in data:
                    return self._parse_official_api_data(data['value']['list'])
            elif 'apiopen.top' in url:
                if data.get('code') == 200 and 'result' in data:
                    return self._parse_apiopen_data(data['result'])
            else:
                # 通用解析
                if 'result' in data or 'data' in data:
                    result_data = data.get('result', data.get('data', []))
                    return self._parse_generic_api_data(result_data)

        except Exception as e:
            self.logger.error(f"解析API数据失败: {e}")

        return []
    
    def _crawl_from_html(self, source: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """从HTML页面爬取数据"""
        url = source['url']
        params = source.get('params', {})
        headers = source.get('headers', {})
        encoding = source.get('encoding', 'utf-8')

        response = self.anti_crawler.make_request(url, params=params, headers=headers)
        if not response:
            return []

        try:
            # 设置正确的编码
            response.encoding = encoding
            soup = BeautifulSoup(response.text, 'html.parser')

            if '500.com' in url:
                return self._parse_500_dlt_data_enhanced(soup, count)
            else:
                # 通用HTML解析
                return self._parse_generic_html(soup, count)

        except Exception as e:
            self.logger.error(f"解析HTML数据失败: {e}")

        return []
    
    def _parse_official_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析官方API数据"""
        parsed_data = []
        
        for item in result_data:
            try:
                # 解析开奖号码
                lotteryDrawResult = item.get('lotteryDrawResult', '')
                if not lotteryDrawResult:
                    continue
                
                # 分割前区和后区
                parts = lotteryDrawResult.split(' ')
                if len(parts) >= 2:
                    front_part = parts[0]  # 前区
                    back_part = parts[1]   # 后区
                    
                    front_balls = [int(x) for x in front_part.split(' ') if x.isdigit()]
                    back_balls = [int(x) for x in back_part.split(' ') if x.isdigit()]
                    
                    # 如果解析失败，尝试其他方式
                    if len(front_balls) != 5 or len(back_balls) != 2:
                        # 尝试按空格分割所有号码
                        all_numbers = [int(x) for x in lotteryDrawResult.split() if x.isdigit()]
                        if len(all_numbers) == 7:
                            front_balls = all_numbers[:5]
                            back_balls = all_numbers[5:7]
                    
                    # 验证数据
                    if self._validate_numbers(front_balls, back_balls):
                        parsed_data.append({
                            'issue': item.get('lotteryDrawNum', ''),
                            'date': item.get('lotteryDrawTime', ''),
                            'front_balls': front_balls,
                            'back_balls': back_balls,
                            'sales_amount': item.get('totalSaleAmount', 0),
                            'pool_amount': item.get('poolBalanceAmt', 0),
                            'source': '官方API'
                        })
                
            except Exception as e:
                self.logger.warning(f"解析单条官方数据失败: {e}")
                continue
        
        return parsed_data
    
    def _parse_500_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析500彩票网数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'class': 'kj_tablelist02'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 9:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析前区球
                        front_balls = []
                        for i in range(2, 7):  # 前区在第2-6列
                            front_balls.append(int(cells[i].text.strip()))
                        
                        # 解析后区球
                        back_balls = []
                        for i in range(7, 9):  # 后区在第7-8列
                            back_balls.append(int(cells[i].text.strip()))
                        
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '500彩票网'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析500彩票网单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析500彩票网数据失败: {e}")
        
        return parsed_data
    
    def _parse_sina_data(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """解析新浪彩票数据"""
        parsed_data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', {'id': 'dlt_data'})
            if not table:
                return []
            
            rows = table.find_all('tr')[1:count+1]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 9:
                    try:
                        issue = cells[0].text.strip()
                        date = cells[1].text.strip()
                        
                        # 解析前区球
                        front_balls = []
                        for i in range(2, 7):  # 前区在第2-6列
                            front_balls.append(int(cells[i].text.strip()))
                        
                        # 解析后区球
                        back_balls = []
                        for i in range(7, 9):  # 后区在第7-8列
                            back_balls.append(int(cells[i].text.strip()))
                        
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': issue,
                                'date': date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': '新浪彩票'
                            })
                    
                    except Exception as e:
                        self.logger.warning(f"解析新浪彩票单行数据失败: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"解析新浪彩票数据失败: {e}")
        
        return parsed_data

    def _parse_apiopen_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析apiopen.top API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 解析开奖号码
                lottery_no = item.get('lottery_no', '')
                lottery_date = item.get('lottery_date', '')
                lottery_res = item.get('lottery_res', '')

                if lottery_res:
                    # 分割号码 "01,07,12,23,28,03,08"
                    numbers = lottery_res.split(',')
                    if len(numbers) >= 7:
                        front_balls = [int(num) for num in numbers[:5]]
                        back_balls = [int(num) for num in numbers[5:7]]

                        # 验证数据
                        if self._validate_numbers(front_balls, back_balls):
                            parsed_data.append({
                                'issue': lottery_no,
                                'date': lottery_date,
                                'front_balls': front_balls,
                                'back_balls': back_balls,
                                'sales_amount': 0,
                                'pool_amount': 0,
                                'source': 'apiopen.top'
                            })

            except Exception as e:
                self.logger.warning(f"解析apiopen数据失败: {e}")
                continue

        return parsed_data

    def _parse_generic_api_data(self, result_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析通用API数据"""
        parsed_data = []

        for item in result_data:
            try:
                # 尝试多种可能的字段名
                issue = item.get('issue', item.get('code', item.get('period', '')))
                date = item.get('date', item.get('opentime', item.get('time', '')))

                # 尝试解析号码
                front_balls = []
                back_balls = []

                # 方式1: 直接字段
                if 'front_balls' in item and 'back_balls' in item:
                    front_balls = item['front_balls']
                    back_balls = item['back_balls']
                # 方式2: 号码字符串
                elif 'numbers' in item:
                    numbers_str = item['numbers']
                    if '+' in numbers_str:
                        parts = numbers_str.split('+')
                        front_part = parts[0].strip()
                        back_part = parts[1].strip()
                        front_balls = [int(x) for x in front_part.split(',')]
                        back_balls = [int(x) for x in back_part.split(',')]
                    else:
                        numbers = [int(x) for x in numbers_str.split(',')]
                        if len(numbers) >= 7:
                            front_balls = numbers[:5]
                            back_balls = numbers[5:7]

                # 验证数据
                if len(front_balls) == 5 and len(back_balls) == 2:
                    if self._validate_numbers(front_balls, back_balls):
                        parsed_data.append({
                            'issue': issue,
                            'date': date,
                            'front_balls': front_balls,
                            'back_balls': back_balls,
                            'sales_amount': item.get('sales', 0),
                            'pool_amount': item.get('pool', 0),
                            'source': '通用API'
                        })

            except Exception as e:
                self.logger.warning(f"解析通用API数据失败: {e}")
                continue

        return parsed_data

    def _parse_500_dlt_data_enhanced(self, soup: BeautifulSoup, count: int) -> List[Dict[str, Any]]:
        """增强的500彩票网大乐透数据解析"""
        parsed_data = []

        try:
            # 方法1: 查找历史数据表格
            tables = soup.find_all('table')
            self.logger.info(f"找到 {len(tables)} 个表格")

            for table in tables:
                table_text = table.get_text()
                if any(keyword in table_text for keyword in ['期号', '开奖号码', '前区', '后区', '开奖日期']):
                    self.logger.info("找到包含大乐透开奖数据的表格")

                    # 解析表格数据
                    data = self._parse_dlt_lottery_table(table)
                    if data:
                        parsed_data.extend(data)
                        self.logger.info(f"从表格解析到 {len(data)} 条数据")

            # 方法2: 正则表达式搜索
            if not parsed_data:
                html_content = str(soup)

                # 查找期号
                issue_pattern = r'(2\d{4})'  # 大乐透期号格式
                issues = re.findall(issue_pattern, html_content)

                # 查找日期
                date_pattern = r'(20\d{2}-\d{2}-\d{2})'
                dates = re.findall(date_pattern, html_content)

                # 查找号码组合模式 (5个前区球 + 2个后区球)
                number_patterns = [
                    r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})',
                    r'(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2})',
                ]

                for pattern in number_patterns:
                    matches = re.findall(pattern, html_content)
                    for i, match in enumerate(matches[:count]):
                        numbers = [int(x) for x in match]
                        if len(numbers) == 7:
                            front_balls = numbers[:5]
                            back_balls = numbers[5:7]

                            if self._validate_numbers(front_balls, back_balls):
                                parsed_data.append({
                                    'issue': issues[i] if i < len(issues) else self._generate_default_dlt_issue(i),
                                    'date': dates[i] if i < len(dates) else self._generate_default_date(i),
                                    'front_balls': front_balls,
                                    'back_balls': back_balls,
                                    'sales_amount': 0,
                                    'pool_amount': 0,
                                    'source': '500彩票网大乐透增强解析'
                                })

            # 去重
            unique_data = []
            seen = set()
            for item in parsed_data:
                key = tuple(item['front_balls'] + item['back_balls'])
                if key not in seen:
                    seen.add(key)
                    unique_data.append(item)

            self.logger.info(f"500彩票网大乐透增强解析完成，去重后 {len(unique_data)} 条数据")
            return unique_data[:count]  # 限制返回数量

        except Exception as e:
            self.logger.error(f"500彩票网大乐透增强解析失败: {e}")

        return []

    def _parse_dlt_lottery_table(self, table):
        """解析大乐透开奖数据表格"""
        data = []
        rows = table.find_all('tr')

        # 查找表头
        header_row = None
        for row in rows:
            cells = row.find_all(['th', 'td'])
            header_text = ' '.join([cell.get_text().strip() for cell in cells])
            if '期号' in header_text or '开奖号码' in header_text:
                header_row = row
                break

        if header_row:
            header_cells = header_row.find_all(['th', 'td'])
            headers = [cell.get_text().strip() for cell in header_cells]

            # 找到关键列的索引
            issue_col = self._find_column_index(headers, ['期号', '期数'])
            date_col = self._find_column_index(headers, ['开奖日期', '日期', '时间'])

            # 处理数据行
            for row in rows[1:]:  # 跳过表头
                cells = row.find_all('td')
                if len(cells) >= 7:  # 大乐透至少需要7列（5前区+2后区）
                    try:
                        # 提取期号和日期 - 增强版
                        issue = None
                        date = None

                        # 方法1: 从指定列提取
                        if issue_col is not None and issue_col < len(cells):
                            issue_text = cells[issue_col].get_text().strip()
                            if re.match(r'2\d{4}', issue_text):
                                issue = issue_text

                        if date_col is not None and date_col < len(cells):
                            date_text = cells[date_col].get_text().strip()
                            if re.match(r'20\d{2}-\d{2}-\d{2}', date_text):
                                date = date_text

                        # 方法2: 遍历所有单元格查找期号和日期
                        if not issue or not date:
                            for cell in cells:
                                cell_text = cell.get_text().strip()

                                # 查找期号模式
                                if not issue and re.match(r'2\d{4}', cell_text):
                                    issue = cell_text

                                # 查找日期模式
                                if not date:
                                    date_patterns = [
                                        r'20\d{2}-\d{2}-\d{2}',
                                        r'20\d{2}/\d{2}/\d{2}',
                                        r'20\d{2}\.\d{2}\.\d{2}',
                                        r'20\d{2}年\d{1,2}月\d{1,2}日'
                                    ]
                                    for pattern in date_patterns:
                                        if re.match(pattern, cell_text):
                                            date = cell_text
                                            break

                        # 提取号码
                        numbers = []
                        for cell in cells:
                            cell_text = cell.get_text().strip()
                            if re.match(r'^\d{1,2}$', cell_text):
                                num = int(cell_text)
                                if 1 <= num <= 35:  # 大乐透号码范围更大
                                    numbers.append(num)

                        # 验证号码
                        if len(numbers) >= 7:
                            front_balls = numbers[:5]
                            back_balls = numbers[5:7]

                            if self._validate_numbers(front_balls, back_balls):
                                # 如果没有期号和日期，生成默认值
                                if not issue:
                                    issue = self._generate_default_dlt_issue(len(data))
                                if not date:
                                    date = self._generate_default_date(len(data))

                                data.append({
                                    'issue': issue,
                                    'date': date,
                                    'front_balls': front_balls,
                                    'back_balls': back_balls,
                                    'sales_amount': 0,
                                    'pool_amount': 0,
                                    'source': '500彩票网大乐透表格'
                                })

                    except Exception as e:
                        continue

        return data

    def _find_column_index(self, headers, keywords):
        """查找包含关键词的列索引"""
        for i, header in enumerate(headers):
            for keyword in keywords:
                if keyword in header:
                    return i
        return None

    def _generate_default_dlt_issue(self, index):
        """生成默认大乐透期号"""
        from datetime import datetime
        current_year = datetime.now().year
        # 大乐透期号格式: 年份后两位 + 三位数期号
        year_suffix = str(current_year)[-2:]
        issue_num = 120 - index  # 从120开始递减
        return f"{year_suffix}{issue_num:03d}"

    def _generate_default_date(self, index):
        """生成默认日期"""
        from datetime import datetime, timedelta
        # 从当前日期开始，每3天一期，往前推
        base_date = datetime.now() - timedelta(days=index * 3)
        return base_date.strftime('%Y-%m-%d')

    def _generate_mock_data(self, count: int) -> List[Dict[str, Any]]:
        """生成模拟大乐透数据"""
        import random
        from datetime import datetime, timedelta

        mock_data = []
        base_date = datetime.now() - timedelta(days=count * 3)  # 每3天一期

        for i in range(count):
            issue_date = base_date + timedelta(days=i * 3)
            issue = f"24{str(120 + i).zfill(3)}"  # 从24120开始

            # 生成前区球（1-35，5个不重复）
            front_balls = sorted(random.sample(range(1, 36), 5))

            # 生成后区球（1-12，2个不重复）
            back_balls = sorted(random.sample(range(1, 13), 2))

            # 生成销售额和奖池（模拟真实数据）
            sales_amount = random.randint(150000000, 400000000)  # 1.5-4亿
            pool_amount = random.randint(300000000, 1000000000)  # 3-10亿

            mock_data.append({
                'issue': issue,
                'date': issue_date.strftime('%Y-%m-%d'),
                'front_balls': front_balls,
                'back_balls': back_balls,
                'sales_amount': sales_amount,
                'pool_amount': pool_amount,
                'source': '模拟数据源'
            })

        # 按日期倒序排列（最新的在前面）
        mock_data.reverse()

        self.logger.info(f"生成了 {len(mock_data)} 期模拟大乐透数据")
        return mock_data

    def _validate_numbers(self, front_balls: List[int], back_balls: List[int]) -> bool:
        """验证开奖号码"""
        try:
            # 验证前区球
            front_rules = self.validation_rules['front_balls']
            if len(front_balls) != front_rules['count']:
                return False
            
            for ball in front_balls:
                if ball < front_rules['min_value'] or ball > front_rules['max_value']:
                    return False
            
            if front_rules['unique'] and len(set(front_balls)) != len(front_balls):
                return False
            
            # 验证后区球
            back_rules = self.validation_rules['back_balls']
            if len(back_balls) != back_rules['count']:
                return False
            
            for ball in back_balls:
                if ball < back_rules['min_value'] or ball > back_rules['max_value']:
                    return False
            
            if back_rules['unique'] and len(set(back_balls)) != len(back_balls):
                return False
            
            return True
            
        except Exception:
            return False
    
    def crawl_historical_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """爬取历史数据"""
        self.logger.info(f"爬取大乐透历史数据: {start_date} 到 {end_date}")
        
        # 这里可以实现更复杂的历史数据爬取逻辑
        # 目前返回最新数据作为示例
        return self.crawl_latest_data(100)
    
    def get_crawler_stats(self) -> Dict[str, Any]:
        """获取爬虫统计信息"""
        anti_crawler_stats = self.anti_crawler.get_stats()
        
        return {
            'crawler_type': '大乐透爬虫',
            'current_source': self.data_sources[self.current_source_index]['name'],
            'total_sources': len(self.data_sources),
            'anti_crawler_stats': anti_crawler_stats,
            'validation_rules': self.validation_rules
        }
    
    def close(self):
        """关闭爬虫"""
        self.anti_crawler.close()
        self.logger.info("大乐透爬虫已关闭")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    crawler = DaletouCrawler()
    
    # 测试爬取最新数据
    result = crawler.crawl_latest_data(5)
    print(f"爬取结果: {result}")
    
    # 显示统计信息
    stats = crawler.get_crawler_stats()
    print(f"爬虫统计: {stats}")
    
    crawler.close()
