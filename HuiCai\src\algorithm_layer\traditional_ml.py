#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传统机器学习算法
包含随机森林、SVM、逻辑回归等经典算法

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
import joblib
import logging
from datetime import datetime, timedelta


class TraditionalMLAlgorithms:
    """传统机器学习算法集合"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        
        # 初始化模型
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化所有模型"""
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            'svm': SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                probability=True,
                random_state=42
            ),
            'logistic_regression': LogisticRegression(
                max_iter=1000,
                random_state=42,
                n_jobs=-1
            ),
            'naive_bayes': GaussianNB()
        }
        
        self.logger.info("传统机器学习模型初始化完成")
    
    def extract_features(self, data: pd.DataFrame, lottery_type: str) -> pd.DataFrame:
        """
        特征工程
        
        Args:
            data: 历史开奖数据
            lottery_type: 彩票类型
            
        Returns:
            特征矩阵
        """
        try:
            features = pd.DataFrame()
            
            if lottery_type == 'shuangseqiu':
                features = self._extract_shuangseqiu_features(data)
            elif lottery_type == 'daletou':
                features = self._extract_daletou_features(data)
            elif lottery_type == 'fucai3d':
                features = self._extract_fucai3d_features(data)
            
            self.logger.info(f"特征提取完成: {features.shape}")
            return features
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            raise
    
    def _extract_shuangseqiu_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """双色球特征提取"""
        features = pd.DataFrame()
        
        # 基础统计特征
        features['sum_red'] = data['red_balls'].apply(lambda x: sum(x))
        features['mean_red'] = data['red_balls'].apply(lambda x: np.mean(x))
        features['std_red'] = data['red_balls'].apply(lambda x: np.std(x))
        features['range_red'] = data['red_balls'].apply(lambda x: max(x) - min(x))
        
        # 奇偶比例
        features['odd_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if n % 2 == 1))
        features['even_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if n % 2 == 0))
        
        # 大小比例（以17为界）
        features['big_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if n > 17))
        features['small_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if n <= 17))
        
        # 区间分布
        features['zone1_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if 1 <= n <= 11))
        features['zone2_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if 12 <= n <= 22))
        features['zone3_count'] = data['red_balls'].apply(lambda x: sum(1 for n in x if 23 <= n <= 33))
        
        # 连号特征
        features['consecutive_count'] = data['red_balls'].apply(self._count_consecutive)
        
        # 重复号码特征（与前期对比）
        features['repeat_count'] = self._calculate_repeat_numbers(data['red_balls'])
        
        # 蓝球特征
        features['blue_ball'] = data['blue_ball']
        features['blue_odd'] = data['blue_ball'].apply(lambda x: x % 2)
        features['blue_big'] = data['blue_ball'].apply(lambda x: 1 if x > 8 else 0)
        
        # 时间特征
        if 'draw_date' in data.columns:
            features['weekday'] = pd.to_datetime(data['draw_date']).dt.weekday
            features['month'] = pd.to_datetime(data['draw_date']).dt.month
            features['day'] = pd.to_datetime(data['draw_date']).dt.day
        
        return features
    
    def _extract_daletou_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """大乐透特征提取"""
        features = pd.DataFrame()
        
        # 前区特征
        features['sum_front'] = data['front_balls'].apply(lambda x: sum(x))
        features['mean_front'] = data['front_balls'].apply(lambda x: np.mean(x))
        features['std_front'] = data['front_balls'].apply(lambda x: np.std(x))
        
        # 后区特征
        features['sum_back'] = data['back_balls'].apply(lambda x: sum(x))
        features['mean_back'] = data['back_balls'].apply(lambda x: np.mean(x))
        
        # 奇偶分布
        features['front_odd'] = data['front_balls'].apply(lambda x: sum(1 for n in x if n % 2 == 1))
        features['back_odd'] = data['back_balls'].apply(lambda x: sum(1 for n in x if n % 2 == 1))
        
        return features
    
    def _extract_fucai3d_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """福彩3D特征提取"""
        features = pd.DataFrame()
        
        # 基础特征
        features['sum_all'] = data['numbers'].apply(lambda x: sum(x))
        features['span'] = data['numbers'].apply(lambda x: max(x) - min(x))
        
        # 形态特征
        features['is_group3'] = data['numbers'].apply(self._is_group3)
        features['is_group6'] = data['numbers'].apply(self._is_group6)
        
        # 奇偶特征
        features['odd_count'] = data['numbers'].apply(lambda x: sum(1 for n in x if n % 2 == 1))
        
        return features
    
    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连号个数"""
        if len(numbers) < 2:
            return 0
        
        sorted_nums = sorted(numbers)
        consecutive_count = 0
        
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i + 1] - sorted_nums[i] == 1:
                consecutive_count += 1
        
        return consecutive_count
    
    def _calculate_repeat_numbers(self, red_balls_series: pd.Series) -> pd.Series:
        """计算重复号码"""
        repeat_counts = []
        
        for i in range(len(red_balls_series)):
            if i == 0:
                repeat_counts.append(0)
            else:
                current = set(red_balls_series.iloc[i])
                previous = set(red_balls_series.iloc[i-1])
                repeat_count = len(current.intersection(previous))
                repeat_counts.append(repeat_count)
        
        return pd.Series(repeat_counts)
    
    def _is_group3(self, numbers: List[int]) -> int:
        """判断是否为组三（有两个相同数字）"""
        return 1 if len(set(numbers)) == 2 else 0
    
    def _is_group6(self, numbers: List[int]) -> int:
        """判断是否为组六（三个不同数字）"""
        return 1 if len(set(numbers)) == 3 else 0
    
    def train_models(self, features: pd.DataFrame, targets: pd.DataFrame, 
                    lottery_type: str) -> Dict[str, Any]:
        """
        训练所有模型
        
        Args:
            features: 特征矩阵
            targets: 目标变量
            lottery_type: 彩票类型
            
        Returns:
            训练结果
        """
        try:
            results = {}
            
            # 数据预处理
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=0.2, random_state=42
            )
            
            # 特征缩放
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            self.scalers[lottery_type] = scaler
            
            # 训练每个模型
            for model_name, model in self.models.items():
                self.logger.info(f"训练模型: {model_name}")
                
                # 使用缩放后的数据训练
                if model_name in ['svm', 'logistic_regression']:
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                
                # 计算准确率
                accuracy = accuracy_score(y_test, y_pred)
                
                # 保存结果
                results[model_name] = {
                    'accuracy': accuracy,
                    'model': model,
                    'predictions': y_pred.tolist()
                }
                
                # 特征重要性（如果支持）
                if hasattr(model, 'feature_importances_'):
                    self.feature_importance[f"{lottery_type}_{model_name}"] = {
                        'features': features.columns.tolist(),
                        'importance': model.feature_importances_.tolist()
                    }
                
                self.logger.info(f"{model_name} 训练完成，准确率: {accuracy:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, features: pd.DataFrame, lottery_type: str, 
               model_name: str = 'random_forest') -> Dict[str, Any]:
        """
        使用指定模型进行预测
        
        Args:
            features: 特征数据
            lottery_type: 彩票类型
            model_name: 模型名称
            
        Returns:
            预测结果
        """
        try:
            if model_name not in self.models:
                raise ValueError(f"未知模型: {model_name}")
            
            model = self.models[model_name]
            
            # 特征缩放
            if lottery_type in self.scalers and model_name in ['svm', 'logistic_regression']:
                features_scaled = self.scalers[lottery_type].transform(features)
                predictions = model.predict(features_scaled)
                probabilities = model.predict_proba(features_scaled) if hasattr(model, 'predict_proba') else None
            else:
                predictions = model.predict(features)
                probabilities = model.predict_proba(features) if hasattr(model, 'predict_proba') else None
            
            # 生成推荐号码
            recommended_numbers = self._generate_recommendations(
                predictions, probabilities, lottery_type
            )
            
            result = {
                'model_name': model_name,
                'lottery_type': lottery_type,
                'predictions': predictions.tolist(),
                'probabilities': probabilities.tolist() if probabilities is not None else None,
                'recommended_numbers': recommended_numbers,
                'confidence': self._calculate_confidence(probabilities),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def _generate_recommendations(self, predictions: np.ndarray, 
                                probabilities: Optional[np.ndarray], 
                                lottery_type: str) -> List[int]:
        """根据预测结果生成推荐号码"""
        try:
            if lottery_type == 'shuangseqiu':
                # 生成6个红球 + 1个蓝球
                red_balls = self._select_top_numbers(predictions, probabilities, 6, range(1, 34))
                blue_ball = self._select_top_numbers(predictions, probabilities, 1, range(1, 17))
                return red_balls + blue_ball
            
            elif lottery_type == 'daletou':
                # 生成5个前区 + 2个后区
                front_balls = self._select_top_numbers(predictions, probabilities, 5, range(1, 36))
                back_balls = self._select_top_numbers(predictions, probabilities, 2, range(1, 13))
                return front_balls + back_balls
            
            elif lottery_type == 'fucai3d':
                # 生成3个数字
                return self._select_top_numbers(predictions, probabilities, 3, range(0, 10))
            
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"生成推荐号码失败: {e}")
            # 返回随机号码作为备选
            return self._generate_random_numbers(lottery_type)
    
    def _select_top_numbers(self, predictions: np.ndarray, 
                           probabilities: Optional[np.ndarray],
                           count: int, number_range: range) -> List[int]:
        """选择概率最高的号码"""
        if probabilities is not None:
            # 基于概率选择
            avg_probs = np.mean(probabilities, axis=0)
            top_indices = np.argsort(avg_probs)[-count:]
            return [number_range[i] for i in top_indices if i < len(number_range)]
        else:
            # 随机选择
            import random
            return sorted(random.sample(list(number_range), count))
    
    def _generate_random_numbers(self, lottery_type: str) -> List[int]:
        """生成随机号码作为备选"""
        import random
        
        if lottery_type == 'shuangseqiu':
            red_balls = sorted(random.sample(range(1, 34), 6))
            blue_ball = [random.randint(1, 16)]
            return red_balls + blue_ball
        elif lottery_type == 'daletou':
            front_balls = sorted(random.sample(range(1, 36), 5))
            back_balls = sorted(random.sample(range(1, 13), 2))
            return front_balls + back_balls
        elif lottery_type == 'fucai3d':
            return [random.randint(0, 9) for _ in range(3)]
        else:
            return []
    
    def _calculate_confidence(self, probabilities: Optional[np.ndarray]) -> float:
        """计算预测置信度"""
        if probabilities is not None:
            # 计算平均最大概率
            max_probs = np.max(probabilities, axis=1)
            return float(np.mean(max_probs))
        else:
            return 0.5  # 默认置信度
    
    def save_models(self, save_path: str, lottery_type: str):
        """保存训练好的模型"""
        try:
            import os
            os.makedirs(save_path, exist_ok=True)
            
            for model_name, model in self.models.items():
                model_file = os.path.join(save_path, f"{lottery_type}_{model_name}.pkl")
                joblib.dump(model, model_file)
            
            # 保存缩放器
            if lottery_type in self.scalers:
                scaler_file = os.path.join(save_path, f"{lottery_type}_scaler.pkl")
                joblib.dump(self.scalers[lottery_type], scaler_file)
            
            self.logger.info(f"模型保存完成: {save_path}")
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            raise
    
    def load_models(self, load_path: str, lottery_type: str):
        """加载训练好的模型"""
        try:
            import os
            
            for model_name in self.models.keys():
                model_file = os.path.join(load_path, f"{lottery_type}_{model_name}.pkl")
                if os.path.exists(model_file):
                    self.models[model_name] = joblib.load(model_file)
            
            # 加载缩放器
            scaler_file = os.path.join(load_path, f"{lottery_type}_scaler.pkl")
            if os.path.exists(scaler_file):
                self.scalers[lottery_type] = joblib.load(scaler_file)
            
            self.logger.info(f"模型加载完成: {load_path}")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def get_model_info(self, lottery_type: str) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'available_models': list(self.models.keys()),
            'feature_importance': self.feature_importance.get(f"{lottery_type}_random_forest", {}),
            'scalers_available': lottery_type in self.scalers,
            'model_count': len(self.models)
        }
