#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器
负责系统日志的配置和管理

Author: HuiCai Team
Date: 2025-01-15
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Optional
import sys


class LogManager:
    """日志管理器"""
    
    def __init__(self, config_manager):
        """
        初始化日志管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.loggers: Dict[str, logging.Logger] = {}
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志配置"""
        # 获取日志配置
        log_config = self.config_manager.get('logging', {})
        
        # 日志级别
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())
        
        # 日志格式
        log_format = log_config.get('format', 
                                   '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 日志文件路径
        log_file_path = log_config.get('file_path', 'data/logs/huicai.log')
        
        # 确保日志目录存在
        log_dir = Path(log_file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建格式化器
        formatter = logging.Formatter(log_format)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器（带轮转）
        max_file_size = self._parse_file_size(log_config.get('max_file_size', '10MB'))
        backup_count = log_config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_path = str(Path(log_file_path).with_suffix('.error.log'))
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    def _parse_file_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def set_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        log_level = getattr(logging, level.upper())
        
        # 更新所有处理器的级别
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        for handler in root_logger.handlers:
            if not isinstance(handler, logging.handlers.RotatingFileHandler) or \
               not handler.baseFilename.endswith('.error.log'):
                handler.setLevel(log_level)
    
    def add_file_handler(self, logger_name: str, file_path: str, level: str = 'INFO'):
        """
        为指定日志器添加文件处理器
        
        Args:
            logger_name: 日志器名称
            file_path: 日志文件路径
            level: 日志级别
        """
        logger = self.get_logger(logger_name)
        
        # 确保目录存在
        log_dir = Path(file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建文件处理器
        log_config = self.config_manager.get('logging', {})
        max_file_size = self._parse_file_size(log_config.get('max_file_size', '10MB'))
        backup_count = log_config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # 设置级别和格式
        file_handler.setLevel(getattr(logging, level.upper()))
        
        log_format = log_config.get('format', 
                                   '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
    
    def create_module_logger(self, module_name: str, log_file: Optional[str] = None) -> logging.Logger:
        """
        为模块创建专用日志器
        
        Args:
            module_name: 模块名称
            log_file: 可选的专用日志文件
            
        Returns:
            logging.Logger: 模块日志器
        """
        logger = self.get_logger(module_name)
        
        if log_file:
            self.add_file_handler(module_name, log_file)
        
        return logger
    
    def log_system_info(self):
        """记录系统信息"""
        logger = self.get_logger("SystemInfo")
        
        logger.info("=" * 50)
        logger.info("HuiCai 慧彩系统启动")
        logger.info("=" * 50)
        
        # 记录Python版本
        logger.info(f"Python版本: {sys.version}")
        
        # 记录系统配置
        system_config = self.config_manager.get('system', {})
        logger.info(f"系统名称: {system_config.get('name', 'HuiCai')}")
        logger.info(f"系统版本: {system_config.get('version', '1.0.0')}")
        logger.info(f"调试模式: {system_config.get('debug', False)}")
        
        # 记录数据库配置
        db_config = self.config_manager.get('database', {})
        logger.info(f"数据库类型: {db_config.get('type', 'postgresql')}")
        logger.info(f"数据库主机: {db_config.get('host', 'localhost')}")
        logger.info(f"数据库端口: {db_config.get('port', 5432)}")
        
        logger.info("=" * 50)
    
    def close(self):
        """关闭日志管理器"""
        # 关闭所有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)
        
        # 清空日志器缓存
        self.loggers.clear()
