#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HuiCai Web服务启动脚本
同时启动后端API和前端Web界面

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import sys
import os
import subprocess
import signal
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class WebServerManager:
    """Web服务管理器"""
    
    def __init__(self):
        self.api_process: Optional[subprocess.Popen] = None
        self.web_process: Optional[subprocess.Popen] = None
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止服务...")
        self.stop_all()
        sys.exit(0)
    
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查Python依赖
        try:
            import uvicorn
            import fastapi
            print("✅ Python依赖检查通过")
        except ImportError as e:
            print(f"❌ Python依赖缺失: {e}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        # 检查Web构建文件
        web_build_path = project_root / "web" / "build"
        if not web_build_path.exists():
            print("⚠️ Web界面未构建")
            print("正在构建Web界面...")
            
            if not self.build_web():
                print("❌ Web界面构建失败")
                return False
        else:
            print("✅ Web界面构建文件存在")
        
        return True
    
    def build_web(self) -> bool:
        """构建Web界面"""
        try:
            web_dir = project_root / "web"
            if not web_dir.exists():
                print("❌ web目录不存在")
                return False
            
            # 检查Node.js
            try:
                subprocess.run(["node", "--version"], check=True, capture_output=True)
                subprocess.run(["npm", "--version"], check=True, capture_output=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("❌ Node.js或npm未安装")
                return False
            
            print("📦 安装Web依赖...")
            result = subprocess.run(
                ["npm", "install"], 
                cwd=web_dir, 
                capture_output=True, 
                text=True
            )
            
            if result.returncode != 0:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
            
            print("🔨 构建Web界面...")
            env = os.environ.copy()
            env["REACT_APP_API_URL"] = "http://localhost:8000/api"
            env["GENERATE_SOURCEMAP"] = "false"
            
            result = subprocess.run(
                ["npm", "run", "build"], 
                cwd=web_dir, 
                capture_output=True, 
                text=True,
                env=env
            )
            
            if result.returncode != 0:
                print(f"❌ Web构建失败: {result.stderr}")
                return False
            
            print("✅ Web界面构建完成")
            return True
            
        except Exception as e:
            print(f"❌ Web构建异常: {e}")
            return False
    
    def start_api_server(self) -> bool:
        """启动API服务器"""
        try:
            print("🚀 启动API服务器...")
            
            api_script = project_root / "src" / "web_api" / "main.py"
            if not api_script.exists():
                print(f"❌ API脚本不存在: {api_script}")
                return False
            
            # 启动API服务器
            self.api_process = subprocess.Popen(
                [sys.executable, str(api_script)],
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务器启动
            time.sleep(3)
            
            if self.api_process.poll() is None:
                print("✅ API服务器启动成功 (端口: 8000)")
                return True
            else:
                stdout, stderr = self.api_process.communicate()
                print(f"❌ API服务器启动失败")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ API服务器启动异常: {e}")
            return False
    
    def start_web_server(self) -> bool:
        """启动Web服务器（开发模式）"""
        try:
            web_dir = project_root / "web"
            
            # 检查是否有构建文件
            build_dir = web_dir / "build"
            if build_dir.exists():
                print("ℹ️ 使用构建版本，Web界面已集成到API服务器")
                return True
            
            # 开发模式：启动React开发服务器
            print("🌐 启动Web开发服务器...")
            
            env = os.environ.copy()
            env["REACT_APP_API_URL"] = "http://localhost:8000/api"
            env["BROWSER"] = "none"  # 不自动打开浏览器
            
            self.web_process = subprocess.Popen(
                ["npm", "start"],
                cwd=web_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # 等待Web服务器启动
            time.sleep(5)
            
            if self.web_process.poll() is None:
                print("✅ Web开发服务器启动成功 (端口: 3000)")
                return True
            else:
                stdout, stderr = self.web_process.communicate()
                print(f"❌ Web服务器启动失败")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Web服务器启动异常: {e}")
            return False
    
    def start_all(self) -> bool:
        """启动所有服务"""
        print("="*60)
        print("🚀 启动HuiCai Web服务")
        print("="*60)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 启动API服务器
        if not self.start_api_server():
            return False
        
        # 启动Web服务器
        if not self.start_web_server():
            self.stop_api_server()
            return False
        
        self.running = True
        
        print("\n" + "="*60)
        print("✅ HuiCai Web服务启动完成！")
        print("="*60)
        print("📱 Web界面: http://localhost:3000")
        print("🔗 API接口: http://localhost:8000/api")
        print("📚 API文档: http://localhost:8000/docs")
        print("="*60)
        print("按 Ctrl+C 停止服务")
        
        return True
    
    def stop_api_server(self):
        """停止API服务器"""
        if self.api_process:
            print("🛑 停止API服务器...")
            self.api_process.terminate()
            try:
                self.api_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.api_process.kill()
            self.api_process = None
    
    def stop_web_server(self):
        """停止Web服务器"""
        if self.web_process:
            print("🛑 停止Web服务器...")
            self.web_process.terminate()
            try:
                self.web_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.web_process.kill()
            self.web_process = None
    
    def stop_all(self):
        """停止所有服务"""
        if not self.running:
            return
        
        print("\n🛑 停止所有服务...")
        
        self.stop_web_server()
        self.stop_api_server()
        
        self.running = False
        print("✅ 所有服务已停止")
    
    def wait_for_shutdown(self):
        """等待关闭信号"""
        try:
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.api_process and self.api_process.poll() is not None:
                    print("❌ API服务器意外停止")
                    self.running = False
                    break
                
                if self.web_process and self.web_process.poll() is not None:
                    print("❌ Web服务器意外停止")
                    # Web服务器停止不影响整体运行（可能使用构建版本）
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all()


def main():
    """主函数"""
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    manager = WebServerManager()
    
    try:
        if manager.start_all():
            manager.wait_for_shutdown()
        else:
            print("❌ 服务启动失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 服务运行异常: {e}")
        manager.stop_all()
        sys.exit(1)


if __name__ == "__main__":
    main()
