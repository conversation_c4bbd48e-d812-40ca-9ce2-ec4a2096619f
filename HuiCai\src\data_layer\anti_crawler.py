#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫模块
提供高级反爬虫对抗机制

Author: HuiCai Team
Date: 2025-01-15
"""

import asyncio
import aiohttp
import random
import time
import json
import base64
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
from urllib.parse import urlparse, urljoin
import hashlib
import io
from PIL import Image
import cv2
import numpy as np
import requests
from fake_useragent import UserAgent


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化代理管理器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 代理池
        self.proxy_pool = []
        self.current_proxy_index = 0
        
        # 代理配置
        self.proxy_config = config_manager.get('anti_crawler.proxy', {})
        self.enable_proxy = self.proxy_config.get('enable', False)
        self.proxy_sources = self.proxy_config.get('sources', [])
        self.proxy_test_url = self.proxy_config.get('test_url', 'http://httpbin.org/ip')
        
        # 代理质量统计
        self.proxy_stats = {}
        
        self.logger.info("代理管理器初始化完成")
    
    async def initialize(self):
        """初始化代理池"""
        if not self.enable_proxy:
            self.logger.info("代理功能未启用")
            return
        
        await self._load_proxy_pool()
        await self._test_proxy_pool()
    
    async def _load_proxy_pool(self):
        """加载代理池"""
        try:
            # 从配置文件加载静态代理
            static_proxies = self.proxy_config.get('static_proxies', [])
            self.proxy_pool.extend(static_proxies)
            
            # 从在线源获取代理
            for source in self.proxy_sources:
                try:
                    proxies = await self._fetch_proxies_from_source(source)
                    self.proxy_pool.extend(proxies)
                    self.logger.info(f"从 {source} 获取到 {len(proxies)} 个代理")
                except Exception as e:
                    self.logger.warning(f"从 {source} 获取代理失败: {e}")
            
            self.logger.info(f"代理池加载完成，共 {len(self.proxy_pool)} 个代理")
            
        except Exception as e:
            self.logger.error(f"加载代理池失败: {e}")
    
    async def _fetch_proxies_from_source(self, source: str) -> List[str]:
        """从在线源获取代理"""
        # 这里可以实现从各种免费代理网站获取代理的逻辑
        # 由于篇幅限制，这里只是示例框架
        return []
    
    async def _test_proxy_pool(self):
        """测试代理池中的代理"""
        if not self.proxy_pool:
            return
        
        self.logger.info("开始测试代理池...")
        
        valid_proxies = []
        
        for proxy in self.proxy_pool:
            try:
                if await self._test_single_proxy(proxy):
                    valid_proxies.append(proxy)
                    self.proxy_stats[proxy] = {
                        'success_count': 1,
                        'fail_count': 0,
                        'last_success': datetime.now(),
                        'response_time': 0
                    }
                else:
                    self.proxy_stats[proxy] = {
                        'success_count': 0,
                        'fail_count': 1,
                        'last_fail': datetime.now(),
                        'response_time': float('inf')
                    }
            except Exception as e:
                self.logger.debug(f"测试代理 {proxy} 失败: {e}")
        
        self.proxy_pool = valid_proxies
        self.logger.info(f"代理测试完成，有效代理 {len(valid_proxies)} 个")
    
    async def _test_single_proxy(self, proxy: str, timeout: int = 10) -> bool:
        """测试单个代理"""
        try:
            start_time = time.time()
            
            connector = aiohttp.TCPConnector()
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_config
            ) as session:
                async with session.get(
                    self.proxy_test_url,
                    proxy=proxy
                ) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        if proxy in self.proxy_stats:
                            self.proxy_stats[proxy]['response_time'] = response_time
                        return True
            
            return False
            
        except Exception:
            return False
    
    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        if not self.proxy_pool:
            return None
        
        # 基于成功率选择代理
        weighted_proxies = []
        for proxy in self.proxy_pool:
            stats = self.proxy_stats.get(proxy, {})
            success_rate = stats.get('success_count', 0) / max(1, 
                stats.get('success_count', 0) + stats.get('fail_count', 0))
            
            # 权重 = 成功率 * 响应时间倒数
            response_time = stats.get('response_time', 1)
            weight = success_rate / max(0.1, response_time)
            
            weighted_proxies.append((proxy, weight))
        
        # 加权随机选择
        if weighted_proxies:
            total_weight = sum(weight for _, weight in weighted_proxies)
            if total_weight > 0:
                rand_val = random.uniform(0, total_weight)
                current_weight = 0
                
                for proxy, weight in weighted_proxies:
                    current_weight += weight
                    if rand_val <= current_weight:
                        return proxy
        
        return random.choice(self.proxy_pool) if self.proxy_pool else None
    
    def update_proxy_stats(self, proxy: str, success: bool, response_time: float = 0):
        """更新代理统计"""
        if proxy not in self.proxy_stats:
            self.proxy_stats[proxy] = {
                'success_count': 0,
                'fail_count': 0,
                'response_time': 0
            }
        
        stats = self.proxy_stats[proxy]
        
        if success:
            stats['success_count'] += 1
            stats['last_success'] = datetime.now()
            stats['response_time'] = response_time
        else:
            stats['fail_count'] += 1
            stats['last_fail'] = datetime.now()


class UserAgentManager:
    """User-Agent管理器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化User-Agent管理器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # User-Agent配置
        self.ua_config = config_manager.get('anti_crawler.user_agent', {})
        self.enable_fake_ua = self.ua_config.get('enable_fake', True)
        
        # 初始化fake_useragent
        if self.enable_fake_ua:
            try:
                self.ua_generator = UserAgent()
            except Exception as e:
                self.logger.warning(f"初始化fake_useragent失败: {e}")
                self.ua_generator = None
        else:
            self.ua_generator = None
        
        # 静态User-Agent池
        self.static_user_agents = self.ua_config.get('static_list', [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0'
        ])
        
        self.logger.info("User-Agent管理器初始化完成")
    
    def get_random_user_agent(self, browser: str = None) -> str:
        """
        获取随机User-Agent
        
        Args:
            browser: 指定浏览器类型
            
        Returns:
            str: User-Agent字符串
        """
        try:
            if self.ua_generator:
                if browser == 'chrome':
                    return self.ua_generator.chrome
                elif browser == 'firefox':
                    return self.ua_generator.firefox
                elif browser == 'safari':
                    return self.ua_generator.safari
                else:
                    return self.ua_generator.random
            else:
                return random.choice(self.static_user_agents)
                
        except Exception as e:
            self.logger.warning(f"获取随机User-Agent失败: {e}")
            return random.choice(self.static_user_agents)


class CaptchaSolver:
    """验证码识别器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化验证码识别器
        
        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 验证码配置
        self.captcha_config = config_manager.get('anti_crawler.captcha', {})
        self.enable_ocr = self.captcha_config.get('enable_ocr', True)
        
        # OCR引擎（这里可以集成tesseract或其他OCR服务）
        self.ocr_engine = None
        
        self.logger.info("验证码识别器初始化完成")
    
    async def solve_captcha(self, captcha_image: bytes) -> Optional[str]:
        """
        识别验证码
        
        Args:
            captcha_image: 验证码图片字节
            
        Returns:
            str: 识别结果
        """
        try:
            # 图片预处理
            processed_image = self._preprocess_image(captcha_image)
            
            # OCR识别
            if self.enable_ocr:
                result = await self._ocr_recognize(processed_image)
                if result:
                    self.logger.info(f"验证码识别成功: {result}")
                    return result
            
            # 如果OCR失败，可以尝试其他方法
            # 例如调用第三方验证码识别服务
            
            return None
            
        except Exception as e:
            self.logger.error(f"验证码识别失败: {e}")
            return None
    
    def _preprocess_image(self, image_bytes: bytes) -> np.ndarray:
        """图片预处理"""
        try:
            # 将字节转换为PIL图像
            image = Image.open(io.BytesIO(image_bytes))
            
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 转换为灰度图
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # 二值化
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            
            # 降噪
            denoised = cv2.medianBlur(binary, 3)
            
            return denoised
            
        except Exception as e:
            self.logger.error(f"图片预处理失败: {e}")
            return None
    
    async def _ocr_recognize(self, image: np.ndarray) -> Optional[str]:
        """OCR识别"""
        try:
            # 这里可以集成tesseract或其他OCR引擎
            # 由于依赖复杂，这里只是示例框架
            
            # 示例：使用简单的模板匹配
            # 实际应用中应该使用更强大的OCR引擎
            
            return None

        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return None


class AntiCrawlerSystem:
    """反爬虫系统"""

    def __init__(self, config_manager, logger: logging.Logger):
        """
        初始化反爬虫系统

        Args:
            config_manager: 配置管理器
            logger: 日志器
        """
        self.config_manager = config_manager
        self.logger = logger

        # 子系统
        self.proxy_manager = ProxyManager(config_manager, logger)
        self.ua_manager = UserAgentManager(config_manager, logger)
        self.captcha_solver = CaptchaSolver(config_manager, logger)

        # 反爬虫配置
        self.anti_crawler_config = config_manager.get('anti_crawler', {})

        # 请求频率控制
        self.rate_limit = self.anti_crawler_config.get('rate_limit', {})
        self.min_delay = self.rate_limit.get('min_delay', 1)
        self.max_delay = self.rate_limit.get('max_delay', 5)
        self.burst_delay = self.rate_limit.get('burst_delay', 10)

        # 请求历史
        self.request_history = []
        self.last_request_time = 0

        # 会话管理
        self.session_config = self.anti_crawler_config.get('session', {})
        self.session_timeout = self.session_config.get('timeout', 300)
        self.max_retries = self.session_config.get('max_retries', 3)

        self.logger.info("反爬虫系统初始化完成")

    async def initialize(self):
        """初始化反爬虫系统"""
        await self.proxy_manager.initialize()

    async def create_session(self) -> aiohttp.ClientSession:
        """创建反爬虫会话"""
        try:
            # 获取代理
            proxy = self.proxy_manager.get_random_proxy()

            # 获取User-Agent
            user_agent = self.ua_manager.get_random_user_agent()

            # 构建请求头
            headers = self._build_headers(user_agent)

            # 创建连接器
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=False  # 如果需要忽略SSL验证
            )

            # 创建会话
            timeout = aiohttp.ClientTimeout(total=self.session_timeout)

            session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=headers
            )

            # 设置代理
            if proxy:
                session._default_proxy = proxy

            return session

        except Exception as e:
            self.logger.error(f"创建反爬虫会话失败: {e}")
            raise

    def _build_headers(self, user_agent: str) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

        # 随机添加一些可选头
        optional_headers = {
            'Sec-CH-UA': '"Google Chrome";v="91", "Chromium";v="91", ";Not A Brand";v="99"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"'
        }

        for key, value in optional_headers.items():
            if random.random() > 0.3:  # 70%概率添加
                headers[key] = value

        return headers

    async def smart_delay(self):
        """智能延迟"""
        current_time = time.time()

        # 记录请求历史
        self.request_history.append(current_time)

        # 只保留最近1分钟的记录
        cutoff_time = current_time - 60
        self.request_history = [t for t in self.request_history if t > cutoff_time]

        # 计算请求频率
        request_count = len(self.request_history)

        # 动态调整延迟
        if request_count > 20:  # 高频请求
            delay = random.uniform(self.burst_delay, self.burst_delay * 2)
        elif request_count > 10:  # 中频请求
            delay = random.uniform(self.max_delay, self.burst_delay)
        else:  # 低频请求
            delay = random.uniform(self.min_delay, self.max_delay)

        # 确保与上次请求的最小间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay:
            additional_delay = self.min_delay - time_since_last
            delay += additional_delay

        self.logger.debug(f"智能延迟: {delay:.2f}秒")
        await asyncio.sleep(delay)

        self.last_request_time = time.time()

    async def handle_captcha_challenge(self, session: aiohttp.ClientSession,
                                     captcha_url: str) -> Optional[str]:
        """处理验证码挑战"""
        try:
            self.logger.info("检测到验证码，开始处理...")

            # 下载验证码图片
            async with session.get(captcha_url) as response:
                if response.status == 200:
                    captcha_image = await response.read()
                else:
                    self.logger.error(f"下载验证码失败: {response.status}")
                    return None

            # 识别验证码
            captcha_result = await self.captcha_solver.solve_captcha(captcha_image)

            return captcha_result

        except Exception as e:
            self.logger.error(f"处理验证码失败: {e}")
            return None

    def detect_anti_crawler_response(self, response: aiohttp.ClientResponse,
                                   content: str) -> Dict[str, Any]:
        """检测反爬虫响应"""
        detection_result = {
            'is_blocked': False,
            'block_type': None,
            'captcha_url': None,
            'retry_after': None
        }

        try:
            # 检查状态码
            if response.status in [403, 429, 503]:
                detection_result['is_blocked'] = True
                detection_result['block_type'] = f'http_{response.status}'

                # 检查Retry-After头
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    detection_result['retry_after'] = int(retry_after)

            # 检查内容特征
            if content:
                content_lower = content.lower()

                # 常见的反爬虫关键词
                block_keywords = [
                    'access denied', 'forbidden', 'blocked', 'captcha',
                    '访问被拒绝', '禁止访问', '验证码', '人机验证',
                    'cloudflare', 'ddos protection', 'rate limit'
                ]

                for keyword in block_keywords:
                    if keyword in content_lower:
                        detection_result['is_blocked'] = True
                        detection_result['block_type'] = 'content_filter'
                        break

                # 检查验证码
                if 'captcha' in content_lower or '验证码' in content_lower:
                    detection_result['block_type'] = 'captcha_required'
                    # 尝试提取验证码URL
                    # 这里可以添加更复杂的URL提取逻辑

            return detection_result

        except Exception as e:
            self.logger.error(f"检测反爬虫响应失败: {e}")
            return detection_result

    async def adaptive_retry(self, session: aiohttp.ClientSession,
                           url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """自适应重试"""
        for attempt in range(self.max_retries):
            try:
                # 智能延迟
                if attempt > 0:
                    await self.smart_delay()

                # 发起请求
                async with session.get(url, **kwargs) as response:
                    content = await response.text()

                    # 检测反爬虫
                    detection = self.detect_anti_crawler_response(response, content)

                    if not detection['is_blocked']:
                        return response

                    # 处理不同类型的阻断
                    if detection['block_type'] == 'captcha_required':
                        captcha_url = detection.get('captcha_url')
                        if captcha_url:
                            captcha_result = await self.handle_captcha_challenge(
                                session, captcha_url)
                            if captcha_result:
                                # 重新请求（带验证码）
                                continue

                    # 如果有Retry-After，等待指定时间
                    if detection['retry_after']:
                        await asyncio.sleep(detection['retry_after'])

                    self.logger.warning(f"请求被阻断 (尝试 {attempt + 1}): {detection}")

            except Exception as e:
                self.logger.warning(f"请求异常 (尝试 {attempt + 1}): {e}")

        self.logger.error(f"请求最终失败: {url}")
        return None

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        return {
            'proxy_pool_size': len(self.proxy_manager.proxy_pool),
            'request_history_size': len(self.request_history),
            'last_request_time': self.last_request_time,
            'proxy_stats': self.proxy_manager.proxy_stats
        }
