{% extends "base.html" %}

{% block title %}数据管理 - HuiCai 慧彩智能体系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-database text-primary me-2"></i>
            数据管理
        </h2>
    </div>
</div>

<div class="row">
    <!-- 数据统计 -->
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">1,250</div>
            <div class="stat-label">
                <i class="fas fa-chart-bar me-1"></i>总开奖期数
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">98.5%</div>
            <div class="stat-label">
                <i class="fas fa-check-circle me-1"></i>数据完整性
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">2025-01-15</div>
            <div class="stat-label">
                <i class="fas fa-calendar me-1"></i>最新数据
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">3</div>
            <div class="stat-label">
                <i class="fas fa-dice me-1"></i>彩票类型
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 数据操作 -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools text-info me-2"></i>
                    数据操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="crawlData()">
                        <i class="fas fa-download me-2"></i>爬取最新数据
                    </button>
                    <button class="btn btn-outline-success" onclick="exportData()">
                        <i class="fas fa-file-export me-2"></i>导出数据
                    </button>
                    <button class="btn btn-outline-warning" onclick="importData()">
                        <i class="fas fa-file-import me-2"></i>导入数据
                    </button>
                    <button class="btn btn-outline-info" onclick="validateData()">
                        <i class="fas fa-check-double me-2"></i>数据验证
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 数据源状态 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-globe text-success me-2"></i>
                    数据源状态
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>中国福彩网</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>中国体彩网</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>备用数据源</span>
                        <span class="badge bg-warning">待机</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近开奖数据 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list text-primary me-2"></i>
                    最近开奖数据
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>开奖日期</th>
                                <th>彩票类型</th>
                                <th>开奖号码</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for draw in recent_draws %}
                            <tr>
                                <td>{{ draw.date }}</td>
                                <td>{{ draw.lottery }}</td>
                                <td>
                                    <code class="bg-light p-1 rounded">{{ draw.numbers }}</code>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ draw.status }}</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('{{ draw.date }}', '{{ draw.lottery }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <nav>
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">上一页</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 数据详情模态框 -->
<div class="modal fade" id="dataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">开奖详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- 详情内容将在这里显示 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 爬取数据
    async function crawlData() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            // 模拟爬取过程
            await new Promise(resolve => setTimeout(resolve, 2000));
            showAlert('数据爬取完成！获取到15条新数据', 'success');
        } catch (error) {
            showAlert('数据爬取失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 导出数据
    function exportData() {
        showAlert('数据导出中...', 'info');
        
        // 模拟导出
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = 'data:text/csv;charset=utf-8,日期,彩票类型,开奖号码\n2025-01-15,双色球,03-08-16-21-28-33+12';
            link.download = 'lottery_data_' + new Date().toISOString().split('T')[0] + '.csv';
            link.click();
            showAlert('数据导出成功！', 'success');
        }, 1000);
    }
    
    // 导入数据
    function importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv,.xlsx,.json';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                showAlert(`正在导入文件: ${file.name}`, 'info');
                // 这里可以添加实际的文件上传逻辑
                setTimeout(() => {
                    showAlert('数据导入成功！', 'success');
                }, 2000);
            }
        };
        input.click();
    }
    
    // 数据验证
    async function validateData() {
        const btn = event.target;
        const originalText = btn.innerHTML;
        showLoading(btn);
        
        try {
            // 模拟验证过程
            await new Promise(resolve => setTimeout(resolve, 3000));
            showAlert('数据验证完成！发现2个异常数据已修复', 'success');
        } catch (error) {
            showAlert('数据验证失败：' + error.message, 'danger');
        } finally {
            hideLoading(btn, originalText);
        }
    }
    
    // 查看详情
    function viewDetails(date, lottery) {
        const modalContent = document.getElementById('modalContent');
        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-borderless">
                        <tr><td>开奖日期:</td><td>${date}</td></tr>
                        <tr><td>彩票类型:</td><td>${lottery}</td></tr>
                        <tr><td>期号:</td><td>2025015</td></tr>
                        <tr><td>销售额:</td><td>3.2亿元</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>中奖统计</h6>
                    <table class="table table-borderless">
                        <tr><td>一等奖:</td><td>5注</td></tr>
                        <tr><td>二等奖:</td><td>128注</td></tr>
                        <tr><td>三等奖:</td><td>1,256注</td></tr>
                        <tr><td>奖池金额:</td><td>8.5亿元</td></tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>号码分析</h6>
                    <div class="alert alert-light">
                        <p><strong>奇偶比:</strong> 4:3</p>
                        <p><strong>大小比:</strong> 3:4</p>
                        <p><strong>和值:</strong> 139</p>
                        <p class="mb-0"><strong>连号:</strong> 无</p>
                    </div>
                </div>
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('dataModal'));
        modal.show();
    }
</script>
{% endblock %}
