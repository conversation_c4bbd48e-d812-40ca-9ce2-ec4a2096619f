#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的500彩票网解析器
获取完整的双色球开奖信息

Author: HuiCai Team
Date: 2025-01-15
"""

import requests
from bs4 import BeautifulSoup
import re
import json
from datetime import datetime

def get_500_ssq_data():
    """获取500彩票网双色球数据"""
    print("🔍 获取500彩票网双色球数据...")
    
    # 尝试不同的URL
    urls = [
        "https://datachart.500.com/ssq/",
        "https://datachart.500.com/ssq/history/",
        "https://datachart.500.com/ssq/history/newinc/history.php",
        "https://datachart.500.com/ssq/history/newinc/history.php?start=24001&end=24010"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.500.com/'
    }
    
    for url in urls:
        print(f"\n📡 尝试URL: {url}")
        try:
            response = requests.get(url, headers=headers, timeout=15)
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                response.encoding = 'gb2312'
                html_content = response.text
                
                # 解析数据
                lottery_data = parse_500_html(html_content, url)
                
                if lottery_data:
                    print(f"✅ 从 {url} 解析到 {len(lottery_data)} 条数据")
                    return lottery_data
                else:
                    print(f"❌ 从 {url} 未解析到数据")
            
        except Exception as e:
            print(f"❌ 请求 {url} 失败: {e}")
    
    return []

def parse_500_html(html_content, url):
    """解析500彩票网HTML内容"""
    soup = BeautifulSoup(html_content, 'html.parser')
    lottery_data = []
    
    print(f"📄 HTML大小: {len(html_content)} 字符")
    
    # 方法1: 查找历史数据表格
    print("\n🔍 方法1: 查找历史数据表格...")
    
    # 查找包含开奖数据的表格
    tables = soup.find_all('table')
    print(f"找到 {len(tables)} 个表格")
    
    for i, table in enumerate(tables):
        # 检查表格是否包含开奖相关内容
        table_text = table.get_text()
        if any(keyword in table_text for keyword in ['期号', '开奖号码', '红球', '蓝球', '开奖日期']):
            print(f"  表格 {i+1} 包含开奖相关内容")
            
            # 解析表格数据
            data = parse_lottery_table(table)
            if data:
                lottery_data.extend(data)
                print(f"    ✅ 解析到 {len(data)} 条数据")
    
    # 方法2: 查找特定的开奖数据结构
    print("\n🔍 方法2: 查找开奖数据结构...")
    
    # 查找可能包含开奖数据的div或其他元素
    data_containers = soup.find_all(['div', 'section', 'article'], 
                                   class_=re.compile(r'(history|result|award|lottery|ssq)', re.I))
    
    for container in data_containers:
        print(f"  找到数据容器: {container.get('class', 'no-class')}")
        data = parse_data_container(container)
        if data:
            lottery_data.extend(data)
            print(f"    ✅ 解析到 {len(data)} 条数据")
    
    # 方法3: 正则表达式全文搜索
    print("\n🔍 方法3: 正则表达式全文搜索...")
    
    # 查找期号模式
    issue_pattern = r'(20\d{5})'
    issues = re.findall(issue_pattern, html_content)
    print(f"  找到 {len(issues)} 个期号")
    
    # 查找日期模式
    date_pattern = r'(20\d{2}-\d{2}-\d{2})'
    dates = re.findall(date_pattern, html_content)
    print(f"  找到 {len(dates)} 个日期")
    
    # 查找号码组合模式
    number_patterns = [
        r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})',
        r'(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2}),(\d{2})',
        r'<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>\s*<[^>]*>(\d{1,2})<[^>]*>'
    ]
    
    for pattern in number_patterns:
        matches = re.findall(pattern, html_content)
        print(f"  模式匹配到 {len(matches)} 组号码")
        
        for match in matches:
            numbers = [int(x) for x in match]
            if len(numbers) == 7:
                red_balls = numbers[:6]
                blue_ball = numbers[6]
                
                # 验证号码有效性
                if (all(1 <= ball <= 33 for ball in red_balls) and 
                    1 <= blue_ball <= 16 and 
                    len(set(red_balls)) == 6):
                    
                    lottery_data.append({
                        'issue': issues[0] if issues else None,
                        'date': dates[0] if dates else None,
                        'red_balls': red_balls,
                        'blue_ball': blue_ball,
                        'source': '正则匹配'
                    })
                    print(f"    ✅ 有效号码: {red_balls} + {blue_ball}")
    
    # 去重
    unique_data = []
    seen = set()
    for item in lottery_data:
        key = tuple(item['red_balls'] + [item['blue_ball']])
        if key not in seen:
            seen.add(key)
            unique_data.append(item)
    
    print(f"\n📊 去重后共 {len(unique_data)} 条数据")
    return unique_data

def parse_lottery_table(table):
    """解析开奖数据表格"""
    data = []
    rows = table.find_all('tr')
    
    # 查找表头，确定列的含义
    header_row = None
    for row in rows:
        cells = row.find_all(['th', 'td'])
        header_text = ' '.join([cell.get_text().strip() for cell in cells])
        if '期号' in header_text or '开奖号码' in header_text:
            header_row = row
            break
    
    if header_row:
        # 分析表头结构
        header_cells = header_row.find_all(['th', 'td'])
        headers = [cell.get_text().strip() for cell in header_cells]
        print(f"    表头: {headers}")
        
        # 找到关键列的索引
        issue_col = find_column_index(headers, ['期号', '期数'])
        date_col = find_column_index(headers, ['开奖日期', '日期', '时间'])
        
        # 处理数据行
        for row in rows[1:]:  # 跳过表头
            cells = row.find_all('td')
            if len(cells) >= 6:  # 至少要有足够的列
                try:
                    # 提取期号
                    issue = None
                    if issue_col is not None and issue_col < len(cells):
                        issue_text = cells[issue_col].get_text().strip()
                        if re.match(r'20\d{5}', issue_text):
                            issue = issue_text
                    
                    # 提取日期
                    date = None
                    if date_col is not None and date_col < len(cells):
                        date_text = cells[date_col].get_text().strip()
                        if re.match(r'20\d{2}-\d{2}-\d{2}', date_text):
                            date = date_text
                    
                    # 提取号码
                    numbers = []
                    for cell in cells:
                        cell_text = cell.get_text().strip()
                        if re.match(r'^\d{1,2}$', cell_text):
                            num = int(cell_text)
                            if 1 <= num <= 33:
                                numbers.append(num)
                    
                    # 如果找到了完整的号码组合
                    if len(numbers) >= 7:
                        red_balls = numbers[:6]
                        blue_ball = numbers[6]
                        
                        if (all(1 <= ball <= 33 for ball in red_balls) and 
                            1 <= blue_ball <= 16 and 
                            len(set(red_balls)) == 6):
                            
                            data.append({
                                'issue': issue,
                                'date': date,
                                'red_balls': red_balls,
                                'blue_ball': blue_ball,
                                'source': '表格解析'
                            })
                
                except Exception as e:
                    continue
    
    return data

def parse_data_container(container):
    """解析数据容器"""
    data = []
    
    # 查找容器内的号码元素
    ball_elements = container.find_all(['span', 'div', 'em'], 
                                      class_=re.compile(r'(ball|num)', re.I))
    
    if ball_elements:
        numbers = []
        for elem in ball_elements:
            text = elem.get_text().strip()
            if re.match(r'^\d{1,2}$', text):
                numbers.append(int(text))
        
        if len(numbers) >= 7:
            red_balls = numbers[:6]
            blue_ball = numbers[6]
            
            if (all(1 <= ball <= 33 for ball in red_balls) and 
                1 <= blue_ball <= 16 and 
                len(set(red_balls)) == 6):
                
                data.append({
                    'issue': None,
                    'date': None,
                    'red_balls': red_balls,
                    'blue_ball': blue_ball,
                    'source': '容器解析'
                })
    
    return data

def find_column_index(headers, keywords):
    """查找包含关键词的列索引"""
    for i, header in enumerate(headers):
        for keyword in keywords:
            if keyword in header:
                return i
    return None

def main():
    """主函数"""
    print("🎯 增强的500彩票网解析器")
    print("=" * 50)
    
    lottery_data = get_500_ssq_data()
    
    if lottery_data:
        print(f"\n🎉 成功解析到 {len(lottery_data)} 条开奖数据")
        
        print("\n📊 解析结果:")
        for i, data in enumerate(lottery_data[:10]):  # 显示前10条
            issue = data['issue'] or '未知期号'
            date = data['date'] or '未知日期'
            red_str = ' '.join([f"{ball:02d}" for ball in data['red_balls']])
            blue_str = f"{data['blue_ball']:02d}"
            
            print(f"  {i+1}. {issue} ({date}): {red_str} + {blue_str} [{data['source']}]")
        
        # 保存数据到JSON文件
        with open('500_lottery_data.json', 'w', encoding='utf-8') as f:
            json.dump(lottery_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 数据已保存到 500_lottery_data.json")
        
        return lottery_data
    else:
        print("\n😞 未能解析到有效数据")
        return []

if __name__ == "__main__":
    main()
