#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型训练器
提供LSTM、Transformer等模型的训练功能

Author: HuiCai Team
Date: 2025-01-15
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from datetime import datetime
import json
import pickle
from pathlib import Path

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Embedding, MultiHeadAttention, LayerNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    tf = None


class ModelTrainer:
    """深度学习模型训练器"""
    
    def __init__(self, config_manager, logger: logging.Logger):
        self.config = config_manager
        self.logger = logger
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.training_history = {}
        
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，深度学习功能将受限")
        
        self.model_save_path = Path("models/deep_learning")
        self.model_save_path.mkdir(parents=True, exist_ok=True)
    
    def prepare_sequence_data(self, data: pd.DataFrame, lottery_type: str, 
                            sequence_length: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备序列数据用于训练
        
        Args:
            data: 历史数据
            lottery_type: 彩票类型
            sequence_length: 序列长度
            
        Returns:
            (X, y) 训练数据
        """
        try:
            if lottery_type == 'shuangseqiu':
                return self._prepare_shuangseqiu_sequences(data, sequence_length)
            elif lottery_type == 'daletou':
                return self._prepare_daletou_sequences(data, sequence_length)
            elif lottery_type == 'fucai3d':
                return self._prepare_fucai3d_sequences(data, sequence_length)
            else:
                raise ValueError(f"不支持的彩票类型: {lottery_type}")
                
        except Exception as e:
            self.logger.error(f"准备序列数据失败: {e}")
            raise
    
    def _prepare_shuangseqiu_sequences(self, data: pd.DataFrame, 
                                     sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """准备双色球序列数据"""
        # 将红球和蓝球组合成特征向量
        features = []
        targets = []
        
        for _, row in data.iterrows():
            red_balls = row['red_balls']
            blue_ball = row['blue_ball']
            
            # 创建特征向量：红球用one-hot编码，蓝球单独编码
            feature_vector = np.zeros(33 + 16)  # 33个红球位置 + 16个蓝球位置
            
            for ball in red_balls:
                feature_vector[ball - 1] = 1  # 红球one-hot编码
            
            feature_vector[33 + blue_ball - 1] = 1  # 蓝球one-hot编码
            
            features.append(feature_vector)
            targets.append(red_balls + [blue_ball])
        
        # 创建序列
        X, y = [], []
        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            y.append(features[i])
        
        return np.array(X), np.array(y)
    
    def _prepare_daletou_sequences(self, data: pd.DataFrame, 
                                 sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """准备大乐透序列数据"""
        features = []
        
        for _, row in data.iterrows():
            front_balls = row['front_balls']
            back_balls = row['back_balls']
            
            # 创建特征向量
            feature_vector = np.zeros(35 + 12)  # 35个前区 + 12个后区
            
            for ball in front_balls:
                feature_vector[ball - 1] = 1
            
            for ball in back_balls:
                feature_vector[35 + ball - 1] = 1
            
            features.append(feature_vector)
        
        # 创建序列
        X, y = [], []
        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            y.append(features[i])
        
        return np.array(X), np.array(y)
    
    def _prepare_fucai3d_sequences(self, data: pd.DataFrame, 
                                 sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """准备福彩3D序列数据"""
        features = []
        
        for _, row in data.iterrows():
            numbers = row['numbers']
            # 直接使用数字作为特征
            features.append(numbers)
        
        # 创建序列
        X, y = [], []
        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            y.append(features[i])
        
        return np.array(X), np.array(y)
    
    def build_lstm_model(self, input_shape: Tuple, lottery_type: str) -> Optional[Model]:
        """
        构建LSTM模型
        
        Args:
            input_shape: 输入形状
            lottery_type: 彩票类型
            
        Returns:
            LSTM模型
        """
        if not TENSORFLOW_AVAILABLE:
            self.logger.error("TensorFlow未安装，无法构建LSTM模型")
            return None
        
        try:
            model = Sequential([
                LSTM(128, return_sequences=True, input_shape=input_shape),
                Dropout(0.2),
                LSTM(64, return_sequences=True),
                Dropout(0.2),
                LSTM(32),
                Dropout(0.2),
                Dense(64, activation='relu'),
                Dropout(0.2),
                Dense(input_shape[1], activation='sigmoid')  # 输出层
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info(f"LSTM模型构建完成: {lottery_type}")
            return model
            
        except Exception as e:
            self.logger.error(f"构建LSTM模型失败: {e}")
            return None
    
    def build_transformer_model(self, input_shape: Tuple, lottery_type: str) -> Optional[Model]:
        """
        构建Transformer模型
        
        Args:
            input_shape: 输入形状
            lottery_type: 彩票类型
            
        Returns:
            Transformer模型
        """
        if not TENSORFLOW_AVAILABLE:
            self.logger.error("TensorFlow未安装，无法构建Transformer模型")
            return None
        
        try:
            # 输入层
            inputs = tf.keras.Input(shape=input_shape)
            
            # Multi-Head Attention
            attention_output = MultiHeadAttention(
                num_heads=8,
                key_dim=64
            )(inputs, inputs)
            
            # Add & Norm
            attention_output = LayerNormalization()(inputs + attention_output)
            
            # Feed Forward
            ffn_output = Dense(128, activation='relu')(attention_output)
            ffn_output = Dense(input_shape[1])(ffn_output)
            
            # Add & Norm
            ffn_output = LayerNormalization()(attention_output + ffn_output)
            
            # Global Average Pooling
            pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn_output)
            
            # 输出层
            outputs = Dense(input_shape[1], activation='sigmoid')(pooled)
            
            model = Model(inputs=inputs, outputs=outputs)
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info(f"Transformer模型构建完成: {lottery_type}")
            return model
            
        except Exception as e:
            self.logger.error(f"构建Transformer模型失败: {e}")
            return None
    
    def train_model(self, model: Model, X: np.ndarray, y: np.ndarray, 
                   lottery_type: str, model_name: str,
                   epochs: int = 100, batch_size: int = 32,
                   validation_split: float = 0.2) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            model: 要训练的模型
            X: 训练特征
            y: 训练标签
            lottery_type: 彩票类型
            model_name: 模型名称
            epochs: 训练轮数
            batch_size: 批次大小
            validation_split: 验证集比例
            
        Returns:
            训练结果
        """
        if not TENSORFLOW_AVAILABLE:
            return {'status': 'failed', 'error': 'TensorFlow未安装'}
        
        try:
            # 准备回调函数
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7
                ),
                ModelCheckpoint(
                    filepath=str(self.model_save_path / f"{lottery_type}_{model_name}_best.h5"),
                    monitor='val_loss',
                    save_best_only=True
                )
            ]
            
            # 训练模型
            start_time = datetime.now()
            
            history = model.fit(
                X, y,
                epochs=epochs,
                batch_size=batch_size,
                validation_split=validation_split,
                callbacks=callbacks,
                verbose=1
            )
            
            end_time = datetime.now()
            training_time = (end_time - start_time).total_seconds()
            
            # 保存模型
            model_path = self.model_save_path / f"{lottery_type}_{model_name}.h5"
            model.save(str(model_path))
            
            # 保存训练历史
            history_data = {
                'loss': history.history['loss'],
                'val_loss': history.history['val_loss'],
                'accuracy': history.history.get('accuracy', []),
                'val_accuracy': history.history.get('val_accuracy', [])
            }
            
            history_path = self.model_save_path / f"{lottery_type}_{model_name}_history.json"
            with open(history_path, 'w') as f:
                json.dump(history_data, f)
            
            # 计算最终指标
            final_loss = history.history['val_loss'][-1]
            final_accuracy = history.history.get('val_accuracy', [0])[-1]
            
            # 保存到内存
            self.models[f"{lottery_type}_{model_name}"] = model
            self.training_history[f"{lottery_type}_{model_name}"] = history_data
            
            result = {
                'status': 'success',
                'lottery_type': lottery_type,
                'model_name': model_name,
                'final_loss': float(final_loss),
                'final_accuracy': float(final_accuracy),
                'training_time_seconds': training_time,
                'epochs_trained': len(history.history['loss']),
                'model_path': str(model_path),
                'history_path': str(history_path),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"模型训练完成: {lottery_type}_{model_name}, 损失: {final_loss:.4f}")
            return result
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'lottery_type': lottery_type,
                'model_name': model_name
            }
    
    def predict_with_model(self, model_name: str, input_data: np.ndarray, 
                          lottery_type: str) -> Dict[str, Any]:
        """
        使用训练好的模型进行预测
        
        Args:
            model_name: 模型名称
            input_data: 输入数据
            lottery_type: 彩票类型
            
        Returns:
            预测结果
        """
        try:
            full_model_name = f"{lottery_type}_{model_name}"
            
            # 加载模型（如果未在内存中）
            if full_model_name not in self.models:
                model_path = self.model_save_path / f"{full_model_name}.h5"
                if model_path.exists():
                    self.models[full_model_name] = tf.keras.models.load_model(str(model_path))
                else:
                    return {
                        'status': 'failed',
                        'error': f'模型文件不存在: {model_path}'
                    }
            
            model = self.models[full_model_name]
            
            # 进行预测
            predictions = model.predict(input_data)
            
            # 转换预测结果为号码
            predicted_numbers = self._convert_predictions_to_numbers(
                predictions, lottery_type
            )
            
            # 计算置信度
            confidence = float(np.mean(np.max(predictions, axis=1)))
            
            result = {
                'status': 'success',
                'lottery_type': lottery_type,
                'model_name': model_name,
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'raw_predictions': predictions.tolist(),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"模型预测失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'model_name': model_name,
                'lottery_type': lottery_type
            }
    
    def _convert_predictions_to_numbers(self, predictions: np.ndarray, 
                                      lottery_type: str) -> List[int]:
        """将预测结果转换为号码"""
        try:
            if lottery_type == 'shuangseqiu':
                return self._convert_shuangseqiu_predictions(predictions)
            elif lottery_type == 'daletou':
                return self._convert_daletou_predictions(predictions)
            elif lottery_type == 'fucai3d':
                return self._convert_fucai3d_predictions(predictions)
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"转换预测结果失败: {e}")
            return []
    
    def _convert_shuangseqiu_predictions(self, predictions: np.ndarray) -> List[int]:
        """转换双色球预测结果"""
        # 取最后一次预测
        pred = predictions[-1]
        
        # 红球部分（前33位）
        red_probs = pred[:33]
        red_indices = np.argsort(red_probs)[-6:]  # 选择概率最高的6个
        red_balls = sorted([i + 1 for i in red_indices])
        
        # 蓝球部分（后16位）
        blue_probs = pred[33:49]
        blue_index = np.argmax(blue_probs)
        blue_ball = blue_index + 1
        
        return red_balls + [blue_ball]
    
    def _convert_daletou_predictions(self, predictions: np.ndarray) -> List[int]:
        """转换大乐透预测结果"""
        pred = predictions[-1]
        
        # 前区（前35位）
        front_probs = pred[:35]
        front_indices = np.argsort(front_probs)[-5:]
        front_balls = sorted([i + 1 for i in front_indices])
        
        # 后区（后12位）
        back_probs = pred[35:47]
        back_indices = np.argsort(back_probs)[-2:]
        back_balls = sorted([i + 1 for i in back_indices])
        
        return front_balls + back_balls
    
    def _convert_fucai3d_predictions(self, predictions: np.ndarray) -> List[int]:
        """转换福彩3D预测结果"""
        pred = predictions[-1]
        
        # 直接取最大值对应的数字
        numbers = []
        for i in range(3):
            if i < len(pred):
                numbers.append(int(round(pred[i] * 9)))  # 假设输出是0-1之间的值
            else:
                numbers.append(0)
        
        return numbers
    
    def get_model_info(self, lottery_type: str = None) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            'available_models': list(self.models.keys()),
            'model_save_path': str(self.model_save_path),
            'tensorflow_available': TENSORFLOW_AVAILABLE
        }
        
        if lottery_type:
            # 筛选特定彩票类型的模型
            lottery_models = [name for name in self.models.keys() if name.startswith(lottery_type)]
            info['lottery_models'] = lottery_models
            
            # 获取训练历史
            lottery_history = {
                name: history for name, history in self.training_history.items()
                if name.startswith(lottery_type)
            }
            info['training_history'] = lottery_history
        
        return info
    
    def load_saved_models(self, lottery_type: str = None):
        """加载已保存的模型"""
        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，无法加载模型")
            return
        
        try:
            pattern = f"{lottery_type}_*.h5" if lottery_type else "*.h5"
            
            for model_file in self.model_save_path.glob(pattern):
                if not model_file.name.endswith('_best.h5'):  # 跳过最佳模型文件
                    model_name = model_file.stem
                    try:
                        model = tf.keras.models.load_model(str(model_file))
                        self.models[model_name] = model
                        self.logger.info(f"加载模型: {model_name}")
                    except Exception as e:
                        self.logger.error(f"加载模型失败: {model_name}, {e}")
            
            self.logger.info(f"模型加载完成，共加载{len(self.models)}个模型")
            
        except Exception as e:
            self.logger.error(f"加载保存的模型失败: {e}")
